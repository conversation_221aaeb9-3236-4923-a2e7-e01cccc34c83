APP_ENV=local

DATABASE_URL=postgresql://postgres:securedb@localhost:5432/euromedicadev
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USERNAME=null
SMTP_PASSWORD=null
SMTP_USE_SSL=false
SMTP_EMAIL_FROM="Euromedica Mailer <<EMAIL>>"
GENAI_API_KEY=xxx
OPENAI_API_KEY=xxx
TESSERACT_PATH=xxx
DEFAULT_PASSWORD=xxx
ADMIN_PASSWORD=xxx
SUPER_ADMIN_PASSWORD=xxx
SECRET_KEY=xxx

AWS_REGION=ap-southeast-1
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx
AWS_S3_BUCKET=xxx

HEYGEN_API_KEY=xxx
HEYGEN_AVATAR_ID=xxx
HEYGEN_VOICE_ID=xxx

REPLICA_API_TOKEN=xxx
API_URL="http://127.0.0.1:8080"

VITE_API_URL=https://api-euromedica.dev.zero-one.cloud/api/v1