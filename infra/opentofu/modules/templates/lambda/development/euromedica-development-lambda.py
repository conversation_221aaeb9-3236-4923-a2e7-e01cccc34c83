import json
import boto3
import urllib3
import urllib.parse
import time
from typing import List, Dict
import logging
from datetime import datetime
import os # For os.path.splitext

logger = logging.getLogger()
logger.setLevel("INFO")

s3_client = boto3.client("s3")
http = urllib3.PoolManager()

SLACK_WEBHOOK_URL = "*******************************************************************************"
UPLOAD_API_URL = "https://api-euromedica.dev.zero-one.cloud/api/v1/skin-analyze/upload"
CREATE_LOG_URL = "https://api-euromedica.dev.zero-one.cloud/api/v1/machine-sync-log"
REQUIRED_IMAGES = {"RGB.jpg", "rgb.jpg", "pl.jpg"}  # Required image files

def send_slack_notification(message: str) -> None:
    """Send notification to Slack"""
    try:
        _ = http.request(
            "POST",
            SLACK_WEBHOOK_URL,
            body=json.dumps({"text": message}),
            headers={"Content-Type": "application/json"},
        )
        print(f"Slack notification sent: {message}")
    except Exception as e:
        print(f"Failed to send Slack notification: {str(e)}")

def send_log_create(folder_name: str, images: list[str], pdf: str, status: str, message: str) -> bool:
    """Create Machine Sync Log"""
    try:
        payload = json.dumps({"data": {
            "folder_name": folder_name,
            "images": images,
            "pdf": pdf,
            "status": status,
            "message": message
        }})
        response = http.request(
            "POST",
            CREATE_LOG_URL,
            body=payload,
            headers={"Content-Type": "application/json"},
        )

        if response.status == 201:
            logger.info(f"Successfully sent to Log API: {payload}")
            return True
        else:
            logger.error(f"Log API request failed. Status: {response.status}")
            return False
    except Exception as e:
        logger.error(f"Failed to send Log API: {str(e)}")
        return False

def prepare_api_payload(files: List[str], operator_id: str) -> Dict:
    """
    Prepare API payload with format:
    {
      "images": ["testing4/RGB.jpg", "testing4/pl.jpg"],
      "pdf": "testing4/document.pdf"
    }
    """
    payload = {"images": [], "pdf": None, "operator_id": operator_id}

    for file in files:
        # Skip directory entries (no file extension)
        if '.' not in file:
            continue

        file_path = f"{file}"

        if file.lower().endswith('.pdf'):
            if payload["pdf"] is None:  # Take only the first PDF
                payload["pdf"] = file_path
        else:
            payload["images"].append(file_path)

    print("Prepared payload:", json.dumps(payload, indent=2))
    return payload

def send_to_upload_api(files: List[str], operator_id: str) -> bool:
    """Send data to upload API"""
    payload = prepare_api_payload(files, operator_id)

    try:
        response = http.request(
            "POST",
            UPLOAD_API_URL,
            body=json.dumps(payload),
            headers={"Content-Type": "application/json"}
        )

        logger.info(f"API Request going {response.status}. response: {response.data}")

        if response.status == 201:
            print(f"Successfully sent to API: {json.dumps(payload)}")
            return True
        else:
            print(f"API request failed. Status: {response.status}")
            return False
    except Exception as e:
        print(f"Error sending to API: {str(e)}")
        return False

def check_folder_files(bucket: str, parent_folder: str, date_stamp: str, attempt: int = 1, max_attempts: int = 3) -> bool:
    """Check folder and subfolders for required files"""
    print(f"Check {attempt}/{max_attempts} - Parent Folder: {parent_folder}")

    # Get all objects under parent folder
    res = s3_client.list_objects_v2(Bucket=bucket, Prefix=parent_folder)
    files = [
        obj['Key']
        for obj in res.get('Contents', [])
        if '.' in obj['Key'].split('/')[-1] and not obj['Key'].split('/')[-1].startswith('.')  # Only files with extensions
    ]

    if len(files) == 0 and attempt <= max_attempts:
        send_slack_notification(f"🔄 No files found, retrying... (Attempt {attempt}/{max_attempts})")
        time.sleep(20)
        return check_folder_files(bucket, parent_folder, date_stamp, attempt + 1, max_attempts)
    elif len(files) == 0 and attempt > max_attempts:
        send_slack_notification(f"⛔ **No files found after {max_attempts} attempts!**")
        msg = f"❌ **Failed no files found**"
        send_log_create(parent_folder, [], '', "error", msg)
        return False

    operator_id = files[0].strip('/').split('/')[0]

    folders = {}
    images = []
    pdfs = []
    for file in files:
        parts = file.strip('/').split('/')
        if len(parts) == 4:
            if parts[0] not in folders.keys():
                folders[parts[0]] = []
            folders[parts[0]].append(file)
        elif len(parts) >= 5:
            if parts[1] not in folders.keys():
                folders[parts[1]] = []
            folders[parts[1]].append(file)

    toSend = 1
    for key, value in folders.items():
        send_slack_notification(f"🔍 **Processing sub folder:** `{parent_folder}{key}`")

        checkFiles = [
            obj.split('/')[-1]
            for obj in value
            if '.' in obj.split('/')[-1] and not obj.split('/')[-1].startswith('.')  # Only files with extensions
        ]
        images = [f for f in checkFiles if not f.lower().endswith('.pdf')]
        pdfs = [f for f in checkFiles if f.lower().endswith('.pdf')]

        status_msg = (
            f"📂 **Folder:** `{key}`\n"
            f"🖼️ **Images:** {', '.join(images) or 'None'}\n"
            f"📄 **PDF:** {', '.join(pdfs) or 'None'}"
        )
        send_slack_notification(status_msg)

        if 'RGB.jpg' in images and pdfs:
            files_after_renamed = []
            for file in value:
                parts = file.strip('/').split('/')
                prefix = ""
                new_prefix = ""
                if len(parts) == 1:
                    top_folder = parts[1]
                    prefix = f"{operator_id}/{top_folder}/"
                    new_prefix = f"{operator_id}/{top_folder.lower()}-{date_stamp}/"
                elif len(parts) >= 2:
                    top_folder = parts[1]
                    prefix = f"{operator_id}/{top_folder}/{parts[2]}/"
                    new_prefix = f"{operator_id}/{top_folder.lower()}-{date_stamp}/"
                new_file = file.replace(prefix, new_prefix)
                logger.info(f"Moving file from {file} to {new_file}")
                rename_file(bucket, file, new_file)
                files_after_renamed.append(new_file)



            files_to_delete = [
                obj['Key']
                for obj in res.get('Contents', [])
                if not '.' in obj['Key'].split('/')[-1] or obj['Key'].split('/')[-1].startswith('.')
            ]
            for file in files_to_delete:
                logger.info(f"Deleting object {file=} in {bucket=}")
                s3_client.delete_object(Bucket=bucket, Key=file)

            send_slack_notification("✅ **Complete subfolder!** Sending to API...")
            if send_to_upload_api(files_after_renamed, operator_id):
                send_slack_notification(f"🚀 **Successfully sent subfolder to API!**")
                toSend += 1
            else:
                send_slack_notification(f"❌ **Failed to send subfolder to API!**")
                return False
        else:
            errorMessage = []
            if 'RGB.jpg' not in images:
                errorMessage.append("need 'RGB.jpg' in images")
            if not pdfs:
                errorMessage.append("need pdf file")
            send_slack_notification(f"❌ **Failed subfolder is incomplete! {', '.join(errorMessage)}**")

    if toSend > len(folders):
        return True

    # If no complete subfolders found
    if attempt < max_attempts:
        send_slack_notification(f"🔄 No complete subfolders found, retrying... (Attempt {attempt}/{max_attempts})")
        time.sleep(20)
        return check_folder_files(bucket, parent_folder, date_stamp, attempt + 1, max_attempts)
    else:
        files_after_renamed = []
        for key, value in folders.items():
            for file in value:
                parts = file.strip('/').split('/')
                prefix = ""
                new_prefix = ""
                if len(parts) == 1:
                    top_folder = parts[1]
                    prefix = f"{operator_id}/{top_folder}/"
                    new_prefix = f"{operator_id}/{top_folder.lower()}-{date_stamp}/"
                elif len(parts) >= 2:
                    top_folder = parts[1]
                    prefix = f"{operator_id}/{top_folder}/{parts[2]}/"
                    new_prefix = f"{operator_id}/{top_folder.lower()}-{date_stamp}/"
                new_file = file.replace(prefix, new_prefix)
                logger.info(f"Moving file from {file} to {new_file}")
                rename_file(bucket, file, new_file)
                files_after_renamed.append(new_file)

            files_to_delete = [
                obj['Key']
                for obj in res.get('Contents', [])
                if not '.' in obj['Key'].split('/')[-1] or obj['Key'].split('/')[-1].startswith('.')
            ]
            for file in files_to_delete:
                logger.info(f"Deleting object {file=} in {bucket=}")
                s3_client.delete_object(Bucket=bucket, Key=file)

        send_slack_notification(f"⛔ **No complete subfolders after {max_attempts} attempts!**")
        errorMessage = []
        if 'RGB.jpg' not in images:
            errorMessage.append("need 'RGB.jpg' in images")
        if not pdfs:
            errorMessage.append("need pdf file")
        msg = f"❌ **Failed subfolder is incomplete! {', '.join(errorMessage)}**"

        images = [f for f in files_after_renamed if not f.lower().endswith('.pdf')]
        pdfs = [f for f in files_after_renamed if f.lower().endswith('.pdf')]

        send_log_create(parent_folder, images, ','.join(pdfs), "error", msg)
        return False


def get_content_type_from_extension(file_key):
    _, extension = os.path.splitext(file_key.lower())
    if extension == '.png':
        return 'image/png'
    elif extension == '.jpg' or extension == '.jpeg':
        return 'image/jpeg'
    elif extension == '.pdf':
        return 'application/pdf'
    else:
        logger.warning(f"Unknown extension '{extension}' for key '{file_key}'. Defaulting to 'binary/octet-stream'.")
        return 'application/octet-stream'


def rename_file(source_bucket, source_key, new_key):
    try:
        copy_source = {'Bucket': source_bucket, 'Key': source_key}
        new_content_type = get_content_type_from_extension(new_key)
        s3_client.copy_object(
            CopySource=copy_source,
            Bucket=source_bucket,
            Key=new_key,
            ContentType=new_content_type,
            MetadataDirective='REPLACE',
        )
        s3_client.delete_object(Bucket=source_bucket, Key=source_key)
        logger.info(f"Object renamed from {source_key} to {new_key}")
    except Exception as e:
        logger.error(f"Error renaming object: {e}")
        raise e


def lambda_handler(event, context):
    logger.info("Info of event")
    logger.info(event)
    for record in event["Records"]:
        bucket = record["s3"]["bucket"]["name"]
        object_key = urllib.parse.unquote_plus(record["s3"]["object"]["key"])

        key_length = len(object_key.split("/"))
        if not object_key.endswith("/") or key_length > 3 or key_length == 2:
            continue

        folder_name = object_key
        date_stamp = datetime.now().strftime("%Y%m%d%H%M%S")
        send_slack_notification(f"🔍 **Processing new folder:** `{folder_name}`")
        check_folder_files(bucket, folder_name, date_stamp)

    return {"statusCode": 200, "body": json.dumps("Process completed")}
