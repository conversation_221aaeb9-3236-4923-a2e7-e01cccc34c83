terraform {
  backend "s3" {
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.region
}

data "aws_region" "current" {

}


###################
####### VPC #######
###################
#Running once for new vpc use for all only in base state & lock

module "vpc" {
  source = "./modules/vpc"

  vpc_name    = local.prefix
  cidr_block  = var.vpc_cidr_block
  region_name = data.aws_region.current.name
  common_tags = local.common_tags
}

##############################
####### SECURITY GROUP #######
##############################
module "sg_database" {
  source = "./modules/security_group"

  sg_name     = "${local.prefix}-database"
  description = "Allow access to RDS PostgreSQL"
  vpc_id      = module.vpc.vpc_id
  ingress_rule = [{
    protocol    = "tcp"
    from_port   = 5432
    to_port     = 5432
    cidr_blocks = module.vpc.subnet_public_cidr_blocks
  }]
  egress_rule = [{
    protocol    = -1
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }]
  common_tags = local.common_tags
}

module "sg_docker_swarm" {
  source = "./modules/security_group"

  sg_name     = "${local.prefix}-swarm-cluster"
  description = "Swarm port"
  vpc_id      = module.vpc.vpc_id
  ingress_rule = [{
    protocol    = "tcp"
    from_port   = 2377
    to_port     = 2377
    cidr_blocks = module.vpc.subnet_public_cidr_blocks
    description = "Swarm port"
    }, {
    protocol    = "tcp"
    from_port   = 7946
    to_port     = 7946
    cidr_blocks = module.vpc.subnet_public_cidr_blocks
    description = "Swarm port"
    }, {
    protocol    = "udp"
    from_port   = 7946
    to_port     = 7946
    cidr_blocks = module.vpc.subnet_public_cidr_blocks
    description = "Swarm port"
    }, {
    protocol    = "udp"
    from_port   = 4789
    to_port     = 4789
    cidr_blocks = module.vpc.subnet_public_cidr_blocks
    description = "Swarm port"
  }]
  egress_rule = []
  common_tags = local.common_tags
}
module "sg_bastion" {
  source = "./modules/security_group"

  sg_name     = "${local.prefix}-bastion"
  description = "Control bastion inbound and outbound access"
  vpc_id      = module.vpc.vpc_id
  ingress_rule = [{
    protocol    = "tcp"
    from_port   = 22
    to_port     = 22
    cidr_blocks = ["0.0.0.0/0"]
  }]
  egress_rule = [{
    protocol    = -1
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }]
  common_tags = local.common_tags
}

# Swarm Master Security Group
module "sg_swarm_master" {
  source = "./modules/security_group"

  sg_name     = "${local.prefix}-master"
  description = "Control master inbound and outbound access"
  vpc_id      = module.vpc.vpc_id
  ingress_rule = [{
    protocol                 = "tcp"
    from_port                = 22
    to_port                  = 22
    source_security_group_id = module.sg_bastion.sg_id
    description              = "SSH access from bastion"
    }, {
    protocol    = "tcp"
    from_port   = 80
    to_port     = 80
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTP access"
    }, {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"]
    description = "HTTPS access"
  }]
  egress_rule = [{
    protocol    = -1
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }]
  common_tags = local.common_tags
}

module "sg_swarm_worker" {
  source = "./modules/security_group"

  sg_name     = "${local.prefix}-worker"
  description = "Control worker inbound and outbound access"
  vpc_id      = module.vpc.vpc_id
  ingress_rule = [{
    protocol                 = "tcp"
    from_port                = 22
    to_port                  = 22
    source_security_group_id = module.sg_bastion.sg_id
    description              = "SSH access from bastion"
  }, ]
  egress_rule = [{
    protocol    = -1
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }]
  common_tags = local.common_tags
}

#######################
####### KEYPAIR #######
#######################
module "ec2_keypair" {
  source = "./modules/keypair"

  keyname = var.keyname
}

###################
####### EC2 #######
###################

module "ec2_database" {
  source = "./modules/ec2"

  ami_instance   = var.ubuntu_jammy_ami
  instance_name  = "${local.prefix}-database"
  instance_type  = lookup(var.instance_type, terraform.workspace)
  subnet         = module.vpc.subnet_public_all[0]
  security_group = [module.sg_database.sg_id]
  keyname        = module.ec2_keypair.keyname
  user_data = {
    filename = var.user_data_ec2["database"]
    vars = {
      aws_region   = data.aws_region.current.name
      project_name = local.prefix
    }
  }
  common_tags = local.common_tags

  root_block_device = {
    volume_type           = "gp3"
    volume_size           = var.volume["database"]
    delete_on_termination = true
    encrypted             = true
    kms_key_id            = ""
  }
}
module "ec2_bastion" {
  source = "./modules/ec2"

  ami_instance   = var.ubuntu_noble_ami
  instance_name  = "euromedica-bastion"
  instance_type  = var.instance_type["bastion"]
  subnet         = module.vpc.subnet_public_all[0]
  security_group = [module.sg_bastion.sg_id]
  keyname        = module.ec2_keypair.keyname
  user_data = {
    filename = var.user_data_ec2["bastion"]
    vars = {
      aws_region   = data.aws_region.current.name
      project_name = local.prefix
    }
  }
  common_tags = local.common_tags

  root_block_device = {
    volume_type           = "gp3"
    volume_size           = var.volume["bastion"]
    delete_on_termination = true
    encrypted             = true
    kms_key_id            = ""
  }
}

resource "aws_eip_association" "bastion_eip" {
  instance_id   = module.ec2_bastion.ec2_instance_id
  allocation_id = module.vpc.eip_bastion_id
}

# Modify EC2 instance to use all security groups
module "ec2_master" {
  source = "./modules/ec2"

  ami_instance  = var.ubuntu_jammy_ami
  instance_name = "${local.prefix}-master"
  instance_type = lookup(var.instance_type, terraform.workspace)
  subnet        = module.vpc.subnet_public_all[0]
  security_group = [
    module.sg_swarm_master.sg_id,
    module.sg_docker_swarm.sg_id
  ]
  keyname = module.ec2_keypair.keyname
  user_data = {
    filename = var.user_data_ec2["master"]
    vars = {
      aws_region   = data.aws_region.current.name
      project_name = local.prefix
    }
  }
  common_tags = local.common_tags

  root_block_device = {
    volume_type           = "gp3"
    volume_size           = lookup(var.volume, terraform.workspace)
    delete_on_termination = true
    encrypted             = true
    kms_key_id            = ""
  }
}

resource "aws_eip_association" "master_eip" {
  instance_id   = module.ec2_master.ec2_instance_id
  allocation_id = module.vpc.eip_id
}

resource "time_sleep" "wait_for_master" {
  depends_on = [module.ec2_master, aws_eip_association.master_eip]

  create_duration = "120s"
}

# module "ec2_worker_1" {
#   source = "./modules/ec2"

#   ami_instance  = var.ubuntu_jammy_ami
#   instance_name = "${local.prefix}-worker-1"
#   instance_type = var.instance_type["worker"]
#   subnet        = module.vpc.prod_subnet_public_all[1]
#   security_group = [
#     module.sg_swarm_worker.sg_id,
#     module.sg_docker_swarm.sg_id
#   ]
#   keyname = module.ec2_keypair.keyname
#   user_data = {
#     filename = var.user_data_ec2["worker"]
#     vars = {
#       aws_region   = data.aws_region.current.name
#       project_name = local.prefix
#     }
#   }
#   common_tags = local.common_tags

#   root_block_device = {
#     volume_type           = "gp3"
#     volume_size           = var.volume["worker"]
#     delete_on_termination = true
#     encrypted             = true
#     kms_key_id            = ""
#   }

#   depends_on = [time_sleep.wait_for_master]
# }

###################
##### LAMBDA ######
###################

module "lambda" {
  source         = "./modules/lambda"
  function_name  = "${local.prefix}-lambda"
  memory_size    = var.memory_size
  timeout        = var.timeout_lambda
  lambda_handler = "${local.prefix}-lambda.lambda_handler"
  source_file    = "${path.root}/modules/templates/lambda/${terraform.workspace}/${local.prefix}-lambda.py"
  output_path    = "${path.root}/modules/templates/lambda/${terraform.workspace}/${local.prefix}-lambda.zip"
}

###################
####### S3 ########
###################

module "s3_lambda" {
  source              = "./modules/s3"
  bucket_name         = "${local.prefix}-bucket"
  lambda_function_arn = module.lambda.lambda_function_arn
}

###################
####### ECR #######
###################

module "ecr" {
  source = "./modules/ecr"

  repo_name                      = "euromedica-image"
  common_tags                    = local.common_tags
  untagged_image_expiration_days = 1
}
