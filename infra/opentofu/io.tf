variable "project" {
  default     = "euromedica"
  type        = string
  description = "Project name"
}

variable "region" {
  default     = "ap-southeast-3"
  type        = string
  description = "Region use for all resource"
}

variable "vpc_cidr_block" {
  type        = string
  default     = "**********/16"
  description = "VPC cidr block"
}

variable "keyname" {
  description = "Key pair existing"
  type        = string
  default     = "euromedica-key"
}

variable "public_key_path" {
  type        = string
  default     = "~/.ssh/id_rsa.pub"
  description = "Path of public key for ec2 instance"
}

variable "ubuntu_jammy_ami" {
  // Please consult https://cloud-images.ubuntu.com/locator/ec2/
  default     = "ami-0fa4036f49b11cca9"
  type        = string
  description = "Ubuntu AMI on singapore"
}

variable "ubuntu_jammy_ami" {
  // Please consult https://cloud-images.ubuntu.com/locator/ec2/
  default     = "ami-0fa4036f49b11cca9"
  type        = string
  description = "Ubuntu AMI on singapore"
}

variable "ubuntu_noble_ami" {
  // Please consult https://cloud-images.ubuntu.com/locator/ec2/
  default     = "ami-0acbd6da61bb1f5e0"
  type        = string
  description = "Ubuntu AMI on singapore"
}

variable "instance_type" {
  description = "Instance types for different EC2 instances"
  type        = map(string)
  default = {
    development = "t3.small"
    staging     = "t3.small"
    production  = "t3.medium"
    bastion     = "t3.micro"
    database    = "t3.medium"
  }
}

variable "volume" {
  description = "Instance type"
  type        = map(number)
  default = {
    development = 40
    staging     = 40
    production  = 40
    bastion     = 40
    database    = 40
  }
}

variable "memory_size" {
  type    = number
  default = 256
}

variable "timeout_lambda" {
  type    = number
  default = 90
}

variable "user_data_ec2" {
  type = map(string)
  default = {
    database    = "user-data.sh"
    bastion     = "user-data.sh"
    master      = "user-data-master.sh"
    master_join = "user-data-master-join.sh"
    worker      = "user-data-worker.sh"
  }
  description = "Template file name for ec2 instance apps"
}

locals {
  prefix = "${var.project}-${terraform.workspace}"
  common_tags = {
    Project     = var.project
    Environment = terraform.workspace
    Contact     = "<EMAIL>"
    ManagedBy   = "Terraform"
    Version     = "1.x.x"
    created-by  = "zero-one-group"
  }
}
