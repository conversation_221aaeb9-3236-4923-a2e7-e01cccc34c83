test:
  stage: test
  extends:
    - .only_test
    - .setup
    - .using-postgres
  variables:
    APP_ENV: test
  script:
    - apt update && apt install -y python3-venv python3-pip cmake clang
    - export CHANGED_FILES="$(git diff-tree --no-commit-id --name-only -r $CI_COMMIT_SHORT_SHA)"
    - cp .env.example .env
    - echo -e "\nDATABASE_URL=******************************************/runner-test?sslmode=disable" > apps/api/.env
    - echo -e "\nDATABASE_URL=******************************************/runner-test?sslmode=disable" >> .env
    - pnpm install
    - pnpm migrate up
    - pnpm seed
    - moon api:test
    - |
      if [[ "$CHANGED_FILES" =~ ^apps/mobile//* ]]; then
        if [ ! -d "flutter" ]; then
          git clone https://github.com/flutter/flutter.git -b stable --depth 1
        fi
        export PATH="$PATH:$CI_PROJECT_DIR/flutter/bin"
        moon mobile:test
      fi
    - pnpm migrate down --all
