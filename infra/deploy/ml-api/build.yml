build:ml-api:development:
  stage: build:dev
  interruptible: true
  extends:
    - .build:apps
    - .set_as_dev
    - .only_ml
  variables:
    TAGGING: $CI_COMMIT_SHORT_SHA
    CONTEXT: $CI_PROJECT_DIR/apps/ml-api
    APP_NAME: "ml-api"
  environment:
    name: "dev"
    action: "prepare"

build:ml-api:staging:
  stage: build:staging
  interruptible: true
  extends:
    - .build:apps
    - .set_as_staging
    - .only_ml
  variables:
    TAGGING: $CI_COMMIT_REF_NAME
    CONTEXT: $CI_PROJECT_DIR/apps/ml-api
    APP_NAME: "ml-api"
  environment:
    name: "staging"
    action: "prepare"