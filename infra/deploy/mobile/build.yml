validate:apk:development:
  stage: deploy:apk:dev
  interruptible: true
  extends:
    - .validate:apk
    - .set_as_dev
    - .only_mobile
  environment:
    name: "dev"
    action: "prepare"

validate:apk:staging:
  stage: deploy:apk:staging
  interruptible: true
  extends:
    - .validate:apk
    - .set_as_staging
  environment:
    name: "stg"
    action: "prepare"

validate:apk:production:
  stage: deploy:apk:production
  interruptible: true
  extends:
    - .validate:apk
    - .set_as_production
  environment:
    name: "prod"
    action: "prepare"

build:apk:development:
  stage: deploy:apk:dev
  interruptible: true
  needs: [validate:apk:development]
  extends:
    - .build:apk
    - .set_as_dev
    - .only_mobile
  environment:
    name: "dev"
    action: "prepare"

build:apk:staging:
  stage: deploy:apk:staging
  interruptible: true
  needs: [validate:apk:staging]
  extends:
    - .build:apk
    - .set_as_staging
  environment:
    name: "stg"
    action: "prepare"

build:apk:production:
  stage: deploy:apk:production
  interruptible: true
  needs: [validate:apk:production]
  extends:
    - .build:apk
    - .set_as_production
  environment:
    name: "prod"
    action: "prepare"
