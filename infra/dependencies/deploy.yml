.deploy:apps:
  interruptible: true
  image:
    name: buildpack-deps:24.04-curl
  before_script:
    - /bin/bash -c "$(curl -sL https://git.io/vokNn)"
    - apt-fast update -qy
    - DEBIAN_FRONTEND=noninteractive apt-fast install -y rsync openssh-client gettext-base git
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -t rsa $BASTION_SERVER >> ~/.ssh/known_hosts
  script:
    - touch $CI_PROJECT_DIR/infra/swarm/.env.$CI_ENVIRONMENT_NAME && cp $ENV $CI_PROJECT_DIR/infra/swarm/.env.$CI_ENVIRONMENT_NAME
    - cp $ENV $CI_PROJECT_DIR/infra/swarm/.env
    # - sed -i 's/localhost:5432/**********:5432/g' $CI_PROJECT_DIR/infra/swarm/.env.$CI_ENVIRONMENT_NAME
    # - cp $ENV $CI_PROJECT_DIR/infra/swarm/.env
    # - export $(xargs < $CI_PROJECT_DIR/infra/swarm/.env.$CI_ENVIRONMENT_NAME)
    # - echo "export ENVIRONMENT=\"$CI_ENVIRONMENT_NAME\"" > $CI_PROJECT_DIR/infra/swarm/variable.txt
    # - source $CI_PROJECT_DIR/infra/swarm/variable.txt
    # - echo "$SSH_PRIVATE_KEY_SWARM" > private_key.pem
    # - chmod 600 private_key.pem
    - export $(xargs < $CI_PROJECT_DIR/infra/swarm/.env)
    - envsubst < $CI_PROJECT_DIR/infra/swarm/docker-swarm-backoffice-$CI_ENVIRONMENT_NAME-updated.yml > $CI_PROJECT_DIR/infra/swarm/docker-swarm-backoffice-$CI_ENVIRONMENT_NAME.yml
    - envsubst < $CI_PROJECT_DIR/infra/swarm/docker-swarm-api-$CI_ENVIRONMENT_NAME-updated.yml > $CI_PROJECT_DIR/infra/swarm/docker-swarm-api-$CI_ENVIRONMENT_NAME.yml
    - envsubst < $CI_PROJECT_DIR/infra/swarm/docker-swarm-ml-api-$CI_ENVIRONMENT_NAME-updated.yml > $CI_PROJECT_DIR/infra/swarm/docker-swarm-ml-api-$CI_ENVIRONMENT_NAME.yml
    - rsync -avz -e "ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa -J $USERNAME@$BASTION_SERVER" $CI_PROJECT_DIR/infra/swarm/ $USERNAME@$AWS_SERVER:./swarm
    - export CHANGED_FILES="$(git diff-tree --no-commit-id --name-only -r $CI_COMMIT_SHORT_SHA)"
    - |
      ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa -J $USERNAME@$BASTION_SERVER $USERNAME@$AWS_SERVER <<EOF
      # source ~/swarm/variable.txt
      aws ecr get-login-password --region ap-southeast-3 | docker login --username AWS --password-stdin $ECR_HOST
      echo "Inside $CI_ENVIRONMENT_NAME block"
      # docker stack deploy -c "docker-swarm-lb.yml" lb
      # Deploy base on changes apps
      if echo "$CHANGED_FILES" | grep -qE '^apps/backoffice/|^packages/shared-ui/'; then
        echo "Changes detected in apps/backoffice"
        docker stack deploy -c ~/swarm/docker-swarm-backoffice-$CI_ENVIRONMENT_NAME.yml $CI_ENVIRONMENT_NAME --with-registry-auth
      fi
      if echo "$CHANGED_FILES" | grep -qE '^apps/api/'; then
        echo "Changes detected in apps/api"
        docker stack deploy -c ~/swarm/docker-swarm-api-$CI_ENVIRONMENT_NAME.yml $CI_ENVIRONMENT_NAME --with-registry-auth
      fi
      if echo "$CHANGED_FILES" | grep -qE '^apps/ml-api/'; then
        echo "Changes detected in apps/ml-api"
        docker stack deploy -c ~/swarm/docker-swarm-ml-api-$CI_ENVIRONMENT_NAME.yml $CI_ENVIRONMENT_NAME --with-registry-auth
      fi
      if echo "$CHANGED_FILES" | grep -qE '^infra/' || [[ "$CI_PIPELINE_SOURCE" =~ web ]] || [[ "$CI_ENVIRONMENT_NAME" == "staging" ]] || [[ "$CI_ENVIRONMENT_NAME" == "production" ]]; then
        echo "Changes detected - deploying all apps"
        docker stack deploy -c ~/swarm/docker-swarm-backoffice-$CI_ENVIRONMENT_NAME.yml $CI_ENVIRONMENT_NAME --with-registry-auth
        docker stack deploy -c ~/swarm/docker-swarm-api-$CI_ENVIRONMENT_NAME.yml $CI_ENVIRONMENT_NAME --with-registry-auth
        docker stack deploy -c ~/swarm/docker-swarm-ml-api-$CI_ENVIRONMENT_NAME.yml $CI_ENVIRONMENT_NAME --with-registry-auth
      fi
      EOF

.migrate:
  image:
    name: buildpack-deps:22.04-curl
  before_script:
    - apt-get update && apt-get install -y rsync openssh-client gettext-base
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - |
      cat > ~/.ssh/config << EOF
      Host *
          StrictHostKeyChecking no
          UserKnownHostsFile ~/.ssh/known_hosts
      EOF
    - chmod 600 ~/.ssh/config
    - ssh-keyscan -H -t rsa $BASTION_SERVER >> ~/.ssh/known_hosts
    - cp $ENV .env
    - cp $RSYNCIGNORE rsyncignore
    - rsync --delete --exclude-from='rsyncignore' -avz -e "ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa -J $USERNAME@$BASTION_SERVER" ./ $USERNAME@$AWS_SERVER:/home/<USER>/euromedica-aizer
  script:
    - echo "Running migrations on the dev server"
    - |
      ssh -i ~/.ssh/id_rsa -J $USERNAME@$BASTION_SERVER -o StrictHostKeyChecking=no $USERNAME@$AWS_SERVER <<EOF
        cd /home/<USER>/euromedica-aizer
        cp .env apps/api/
        export PATH=$PATH:/usr/local/go/bin
        pnpm run migrate up
        pnpm run seed
      EOF

.dbdocs:
  image:
    name: 01group/node-flutter-rfc:20230524
  before_script:
    - npm install -g dbdocs
    - export DBDOCS_TOKEN=$DBDOCS_TOKEN
  script:
    - dbdocs build ./docs/dbml/euromedica.dbml --project euromedica/euromedica
