import * as React from "react";

import { Popover, PopoverTrigger, PopoverContent } from "../Popover/Popover";
import { Button } from "../Button";
import { cn } from "#/utils";
import { DayPicker } from "react-day-picker";
import { ChevronLeftIcon, ChevronRightIcon } from "@radix-ui/react-icons";

export interface DatePickerProps {
  /** Controlled selected date.  Pass `undefined` to clear. */
  value?: Date | undefined;
  /** Called whenever a date is picked. */
  onChange?: (date: Date | undefined) => void;
  /** Placeholder shown on the trigger button. */
  placeholder?: string;
  /** Button className override. */
  className?: string;
  /** Button prefix */
  prefix?: React.ReactNode;
  error?: boolean;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  placeholder = "Pick a date",
  className,
  prefix,
  error,
}) => {
  const [internalDate, setInternalDate] = React.useState<Date | undefined>(value);

  const selected = value ?? internalDate;

  const handleSelect = (date: Date | undefined) => {
    if (value === undefined) setInternalDate(date);
    onChange?.(date);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="flex items-center relative">
          {prefix && <div className="absolute left-2">{prefix}</div>}
          <Button
            variant="outline"
            type="button"
            className={cn(
              "w-full justify-start items-center text-left font-normal",
              !selected && "text-muted-foreground",
              prefix && "pl-8",
              className,
              error && "border-destructive"
            )}
          >
            {selected
              ? selected.toLocaleDateString("id-ID", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                })
              : placeholder}
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent>
        <DayPicker
          classNames={{
            month: "space-y-2 w-full",
            caption_label: "text-sm font-bold",
            day: "h-6 w-12 text-center text-secondary-800 font-small aria-selected:opacity-100",
            nav: "relative",
            month_caption: "flex justify-center items-center h-10 font-bold",
            button_next: "absolute right-1",
            button_previous: "absolute left-1",
            outside: "text-secondary-200",
            today: "font-bold bg-secondary-100 rounded-md",
            selected: "bg-primary text-white rounded-md ring-0",
          }}
          components={{
            DayButton: (props) => (
              <Button
                {...props}
                variant="ghost"
                size="icon"
                className="hover:bg-primary hover:text-white rounded-md"
              />
            ),
            PreviousMonthButton: (props) => (
              <Button size="icon" variant="ghost" {...props}>
                <ChevronLeftIcon className="size-4 text-foreground" />
              </Button>
            ),
            NextMonthButton: (props) => (
              <Button size="icon" variant="ghost" {...props}>
                <ChevronRightIcon className="size-4 text-foreground" />
              </Button>
            ),
          }}
          mode="single"
          selected={selected}
          onSelect={handleSelect}
          showOutsideDays={true}
        />
      </PopoverContent>
    </Popover>
  );
};

DatePicker.displayName = "DatePicker";
