[project]
name = "euromedica-aizer"
version = "0.1.0"
description = "Python Root Project Manager for Euromedica Aizer"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
  "alembic>=1.13.3",
  "antialiased-cnns==0.3",
  "fastapi[standard]>=0.115.0",
  "fpdf>=1.7.2",
  "llvmlite>=0.43.0",
  "modal>=0.64.199",
  "numba>=0.60.0",
  "numpy==2.0.2",
  "pandas>=2.2.3",
  "pdf2image>=1.17.0",
  "pillow==10.4.0",
  "psycopg2>=2.9.10",
  "pydantic>=2.10.6",
  "pydantic-settings>=2.8.1",
  "pytesseract>=0.3.13",
  "pytest>=8.3.3",
  "python-dotenv>=1.0.1",
  "rembg>=2.0.59",
  "reportlab>=4.2.5",
  "ruff>=0.6.9",
  "scikit-learn>=1.5.2",
  "scipy>=1.14.1",
  "setuptools>=75.1.0",
  "uvicorn[standard]>=0.31.0",
  "opencv-python>=*********",
  "pyjwt>=2.10.1",
  "pwdlib[argon2,bcrypt]>=0.2.1",
  "openai>=1.67.0",
  "boto3>=1.37.19",
  "locust>=2.34.1",
  "mediapipe>=0.10.14",
  "replicate>=1.0.6",
  "agno>=1.6.3",
  "streamlit>=1.46.0",
  "langchain>=0.3.23",
  "langchain-community>=0.3.21",
  "langchain-openai>=0.3.12",
  "typing-extensions>=4.12.2",
  "pgvector>=0.4.1",
]

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = false

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"

[dependency-groups]
dev = [
    "pyright>=1.1.401",
]
