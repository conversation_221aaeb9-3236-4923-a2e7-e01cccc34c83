from fastapi import APIRouter, BackgroundTasks, HTTPException
from uuid import UUID

from internals.dtos.face_aging import (
    FaceAgingConcernInputDTO,
    FaceAgingConcernOutputDTO,
)

from internals.usecases.face_aging import FaceAgingUseCase

router = APIRouter()

face_aging_use_case = FaceAgingUseCase()


@router.post("/face-aging/concerns/{job_id}", response_model=FaceAgingConcernOutputDTO)
async def face_aging_concerns_endpoint(
    job_id: UUID,
    dto: FaceAgingConcernInputDTO,
    background_tasks: BackgroundTasks,
):
    try:
        background_tasks.add_task(
            face_aging_use_case.face_aging_concerns_service,
            job_id,
            dto,
        )

        return FaceAgingConcernOutputDTO(message="Job created", status=202)

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
