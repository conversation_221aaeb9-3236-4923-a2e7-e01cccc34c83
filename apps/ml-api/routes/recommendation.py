from fastapi import APIRouter, HTTPException
from internals.usecases.recommendation import RecommendationUsecase
from internals.dtos.recommendation import (
    RecommendationInputDTO,
    RecommendationOutputDTO,
)

router = APIRouter()

# Initialize usecase without repository
recommendation_use_case = RecommendationUsecase()


@router.post("/recommendation", response_model=RecommendationOutputDTO)
async def get_recommendation(input_data: RecommendationInputDTO):
    """
    Generate treatment recommendations based on skin analysis and user survey.

    The data parameter is optional and currently not used (CSV files are used instead).
    This will be changed in the future to use the provided data instead of CSV files.
    """
    try:
        result = await recommendation_use_case.generate_recommendation(input_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
