from fastapi import FastAP<PERSON>
from logging.config import dictConfig
from configs.log import log_config

from internals.infrastructure.scheduler.background import lifespan
from routes.face_aging import router as face_aging_router
from routes.skin_analyze import router as skin_analyze_router
from routes.skin_detection import router as skin_detection_router
from routes.recommendation_engine import router as recommendation_engine_router
from routes.recommendation import router as recommendation_router
from routes.scheduler import router as scheduler_router
from routes.summary import router as summary_router
from fastapi.middleware.cors import CORSMiddleware

dictConfig(log_config)

app = FastAPI(
    swagger_ui_parameters={
        "docExpansion": "none",
        "filter": True,
        "displayRequestDuration": True,
    },
    lifespan=lifespan,
)

app.include_router(face_aging_router)
app.include_router(skin_analyze_router)
app.include_router(recommendation_engine_router)
app.include_router(skin_detection_router)

app.include_router(scheduler_router)
app.include_router(summary_router)
app.include_router(recommendation_router)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    return {"message": "Welcome to the Euromedica Aizer API"}
