from PIL import Image, ImageChops
import os
import replicate
import boto3
from botocore.config import Config
import numpy as np
import io
import asyncio
from uuid import UUID
import httpx

from configs.environment import Environment
from internals.dtos.face_aging import (
    FaceAgingConcernCallbackDTO,
    FaceAgingConcernDetail,
    FaceAgingConcernInputDTO,
    FaceAgingConcernOutputGenerated,
    AgingAreaList,
)
from .face_filter import FaceFilter


class FaceAgingUseCase:
    def __init__(self):
        self.face_filter = FaceFilter()

        config = Config(
            region_name=Environment.get_aws_region(),
        )
        aws_region = Environment.get_aws_region()
        self.s3 = boto3.client(
            "s3",
            aws_access_key_id=Environment.get_aws_access_key_id(),
            aws_secret_access_key=Environment.get_aws_secret_access_key(),
            region_name=aws_region,
            endpoint_url=f"https://s3.{aws_region}.amazonaws.com",
            config=config,
        )
        self.s3_bucket = Environment.get_aws_s3_bucket()
        self.api_url = Environment.get_api_url()

    def image_array_to_bytes(self, img_array: np.ndarray) -> io.BytesIO:
        img = Image.fromarray(img_array)
        buffer = io.BytesIO()
        img.save(buffer, format="PNG")
        _ = buffer.seek(0)
        img.close()
        return buffer

    def select_area_from_image_array(
        self,
        img_array: np.ndarray,
        areas: AgingAreaList,
    ) -> io.BytesIO:
        selected_area = self.face_filter.select_face_area(
            areas,
            img_array,
        )
        buffer = self.image_array_to_bytes(selected_area)
        return buffer

    def upload_to_s3(
        self,
        base_image_path: str,
        target_path: str,
        content_type: str,
        buffer: io.BytesIO,
    ) -> str:
        image_path = os.path.join(
            base_image_path,
            target_path,
        )
        _ = self.s3.upload_fileobj(
            buffer,
            self.s3_bucket,
            image_path,
            ExtraArgs={
                "ContentType": content_type,
            },
        )
        return image_path

    def select_and_upload(
        self,
        base_image_path: str,
        areas: AgingAreaList,
        img_array: np.ndarray,
        target_path: str,
    ) -> str:
        selected_image = self.select_area_from_image_array(
            img_array,
            areas,
        )

        image_path = self.upload_to_s3(
            base_image_path,
            target_path,
            "image/png",
            selected_image,
        )
        return image_path

    async def generate_image(
        self,
        input,
        dto: FaceAgingConcernDetail,
        img_array: np.ndarray,
        base_image_path: str,
    ) -> FaceAgingConcernOutputGenerated:
        prediction = await replicate.async_run(
            "lucataco/sdxl-inpainting:a5b13068cc81a89a4fbeefeccc774869fcb34df4dbc92c1555e0f2771d49dde7",
            input,
        )

        content = prediction[0]
        pil_generated_image = Image.open(content)
        generate_image_array = np.array(pil_generated_image)

        generated_original_image = Image.fromarray(generate_image_array).convert("RGBA")
        result_image = generated_original_image.copy()

        if dto.concern != "beautify":
            darken_factor = 0.8
            noise_level = 0.2
            texture_layer = Image.new(
                "RGBA", generated_original_image.size, (128, 128, 128, 255)
            )

            texture_array = np.array(texture_layer).astype(np.float32)
            noise = np.random.normal(0, noise_level * 255, texture_array.shape)
            noisy_texture_array = np.clip(texture_array + noise, 0, 255).astype(
                np.uint8
            )
            noisy_texture_layer = Image.fromarray(noisy_texture_array)
            multiplied_image = ImageChops.multiply(
                generated_original_image, noisy_texture_layer
            )
            strength = 1.0 - darken_factor
            result_image = Image.blend(
                generated_original_image, multiplied_image, alpha=strength
            )

            original_image = Image.fromarray(img_array).convert("RGBA")
            multiplied_image = ImageChops.multiply(original_image, result_image)
            blend_factor = 0.4
            strength = 1.0 - blend_factor
            result_image = Image.blend(
                generated_original_image, multiplied_image, alpha=strength
            )

        pil_generated_image.close()

        generated_image_path = self.select_and_upload(
            base_image_path,
            dto.areas,
            np.array(result_image),
            f"face_aging_{dto.concern}.png",
        )

        ori_image_path = self.select_and_upload(
            base_image_path,
            dto.areas,
            img_array,
            f"selected_area_{dto.concern}.png",
        )

        return FaceAgingConcernOutputGenerated(
            concern=dto.concern,
            generated_image_url=generated_image_path,
            selected_area_url=ori_image_path,
        )

    async def face_aging_concerns_service(
        self, id: UUID, dto: FaceAgingConcernInputDTO
    ):
        base_image = None
        mask_image = None
        base_image_path = "/".join(dto.image_path.split("/")[0:3])
        result_images = []

        response = self.s3.get_object(Bucket=self.s3_bucket, Key=dto.image_path)
        image_data = response["Body"].read()
        pil_image = Image.open(io.BytesIO(image_data))
        img_array = np.array(pil_image)
        pil_image.close()

        image = None
        if "resized" in dto.image_path:
            buffer = self.image_array_to_bytes(img_array)
            image = img_array
            base_image = buffer
        else:
            image = self.face_filter.get_image_resize_write(img_array)
            img_array = image

            buffer = self.image_array_to_bytes(image)
            base_image = io.BytesIO(buffer.getvalue())
            image_path = self.upload_to_s3(
                base_image_path,
                "rgb_resized.jpg",
                "image/jpeg",
                buffer,
            )
            result_images.append(image_path)

        mask = None
        if dto.mask_path:
            response = self.s3.get_object(Bucket=self.s3_bucket, Key=dto.mask_path)
            mask_data = response["Body"].read()
            pil_mask = Image.open(io.BytesIO(mask_data))
            mask = np.array(pil_mask)
            pil_mask.close()

            mask_bytes = self.image_array_to_bytes(mask)
            mask_image = mask_bytes
        else:
            mask = self.face_filter.create_mask(image)
            mask_bytes = self.image_array_to_bytes(mask)
            mask_image = io.BytesIO(mask_bytes.getvalue())

            mask_path = self.upload_to_s3(
                base_image_path,
                "mask.png",
                "image/png",
                mask_bytes,
            )
            result_images.append(mask_path)

        generated = []
        tasks = []
        try:
            async with asyncio.TaskGroup() as tg:
                for concern in dto.concerns:
                    image = io.BytesIO(base_image.getvalue())
                    mask = io.BytesIO(mask_image.getvalue())

                    input = {
                        "image": image,
                        "mask": mask,
                        "scheduler": "DPMSolverMultistep",
                        "steps": 40,
                        "guidance_scale": 8,
                    }
                    match concern.concern:
                        case "pigment":
                            input.update(
                                {
                                    "prompt": """
                                    Increase the appearance of hyperpigmentation by deepening dark spots, creating an uneven skin tone, and enhancing visible discoloration. Add sun damage effects creating red blemishes, and irregular patches of melanin buildup, making the skin appear more blemished. while meticulously preserving and building upon all existing freckles, moles, and current skin pigmentation
                                    """,
                                    "negative_prompt": """
                                    perfect skin, vitalized skin, flawless, smooth, airbrushed, makeup, filters, deformed, disfigured, no dark spots, beautifying, brightened skin,
                                    waxy, plastic, fake skin, CGI, poreless, glossy,
                                    patchy color, uneven lighting, color artifacts, blotchy, desaturated,
                                    symmetrical, patterned, repetitive, grid-like.
                                    """,
                                    "strength": 0.45,
                                    "steps": 50,
                                    "guidance_scale": 10,
                                }
                            )
                        case "scar":
                            input.update(
                                {
                                    "prompt": """
                                    Realistic, distinct, HEALED, TEXTURAL, skin-toned acne scarring. Mainly BOXCAR scars small-medium angular. ICE-PICK scars: small, narrow, softened pits or textural breaks, not sharp holes.
                                    """,
                                    "negative_prompt": """
                                    perfect skin, flawless, smooth, airbrushed, makeup, filters, deformed, disfigured,
                                    excessive aging, deep wrinkles, saggy skin,
                                    bruises, scars, holes, white circle,
                                    waxy, plastic, fake skin, CGI, poreless, glossy,
                                    patchy color, uneven lighting, color artifacts, blotchy, desaturated,
                                    symmetrical, patterned, repetitive, grid-like.
                                    """,
                                    "strength": 0.4,
                                    "guidance_scale": 5,
                                }
                            )
                        case "wrinkles":
                            input.update(
                                {
                                    "prompt": """
                                    Subtly enhance and define fine lines and wrinkles, focusing ONLY on these areas:
                                    1. Crow's feet (outer eyes)
                                    2. Forehead lines
                                    3. Smile lines (nasolabial folds)
                                    Subtly deepen these natural expression lines while preserving all surrounding skin texture. Avoid any severe, overall, or crepey aging effect.
                                    """,
                                    "negative_prompt": """
                                    perfect skin, flawless, smooth, airbrushed, makeup, filters, deformed, disfigured,
                                    excessive aging, deep wrinkles, saggy skin,
                                    bruises, scars, holes, white circle,
                                    waxy, plastic, fake skin, CGI, poreless, glossy,
                                    patchy color, uneven lighting, color artifacts, blotchy, desaturated,
                                    symmetrical, patterned, repetitive, grid-like.
                                    """,
                                    "strength": 0.4,
                                }
                            )
                        case "redness":
                            input.update(
                                {
                                    "prompt": """
                                    Simulate highly sensitive skin: widespread redness, blotchiness, irritation, and inflammation. Add broken capillaries, and raw, fragile skin.
                                    """,
                                    "negative_prompt": """
                                    perfect skin, flawless, smooth, airbrushed, makeup, filters, deformed, disfigured,
                                    excessive aging, deep wrinkles, saggy skin,
                                    bruises, scars, holes, white circle,
                                    waxy, plastic, fake skin, CGI, poreless, glossy,
                                    patchy color, uneven lighting, color artifacts, blotchy, desaturated,
                                    symmetrical, patterned, repetitive, grid-like.
                                    """,
                                    "strength": 0.4,
                                }
                            )
                        case "acne":
                            input.update(
                                {
                                    "prompt": """
                                    Severe active realistic sized acne breakout, high variety, congested irritated skin. Numerous mix of red papules, inflammed pustules, many painful-looking cysts erupting across the face, blackheads, whiteheads. Irregularly scattered, focused on forehead, cheeks, and chin. The underlying base skin tone and lighting must remain natural and consistent
                                    """,
                                    "negative_prompt": """
                                    perfect skin, flawless, smooth, airbrushed, makeup, filters, deformed, disfigured,
                                    excessive aging, deep wrinkles, saggy skin,
                                    bruises, scars, holes, white circle,
                                    waxy, plastic, fake skin, CGI, poreless, glossy,
                                    patchy color, uneven lighting, color artifacts, blotchy, desaturated,
                                    symmetrical, patterned, repetitive, grid-like.
                                    """,
                                    "strength": 0.75,
                                }
                            )
                        case "pores":
                            input.update(
                                {
                                    "prompt": """
                                    Dramatically enlarged, open, and deep facial pores, many containing visible dark blackheads (open comedones). Rough, uneven skin texture with intense oiliness and a shiny, slick appearance. Pores and blackheads most prominent on the nose, forehead, chin. Skin appears slightly dehydrated, accentuating the rough texture. With minor fine lines.
                                    """,
                                    "negative_prompt": """
                                    perfect skin, flawless, smooth, airbrushed, makeup, filters, deformed, disfigured,
                                    excessive aging, deep wrinkles, saggy skin,
                                    bruises, scars, holes, white circle,
                                    waxy, plastic, fake skin, CGI, poreless, glossy,
                                    patchy color, uneven lighting, color artifacts, blotchy, desaturated,
                                    symmetrical, patterned, repetitive, grid-like.
                                    """,
                                    "strength": 0.6,
                                }
                            )
                        case "beautify":
                            input.update(
                                {
                                    "prompt": """Subtly even out skin tone, reduce the visibility of minor blemishes, acne, and redness. Gently soften fine lines and wrinkles. Add a natural, healthy luminosity to the skin. Highly photorealistic, detailed skin texture, preserving pores and original character.""",
                                    "negative_prompt": """
additional wrinkles, plastic skin, waxy, fake skin, airbrushed, overly smooth, poreless, blurry, CGI, 3D render, doll-like,
social media filter, Instagram filter, beautifying filter,
heavy makeup, dramatic makeup, eyeshadow, lipstick,
unnatural skin tones, patchy color, blotchy skin, harsh lighting, flat lighting, glowing artifacts,
deformed, disfigured, change facial features, asymmetrical.
                                    """,
                                    "strength": 0.25,
                                }
                            )

                    task = tg.create_task(
                        self.generate_image(
                            input,
                            concern,
                            img_array,
                            base_image_path,
                        )
                    )
                    tasks.append(task)

            results = await asyncio.gather(*tasks)
            for result in results:
                generated.append(result)
        except Exception as e:
            print(f"Error: {e}")
            for task in tasks:
                error = task.exception()
                if task.done() and task.exception():
                    print(f"Task failed with exception: {task.exception()}")
            raise

        callback_url = f"{self.api_url}/api/v1/face-aging/callback/{id}"
        callback_data = FaceAgingConcernCallbackDTO(
            image_url=result_images, generated_image=generated
        )
        async with httpx.AsyncClient() as client:
            _ = await client.post(
                callback_url,
                json=callback_data.model_dump(exclude_unset=True),
            )
