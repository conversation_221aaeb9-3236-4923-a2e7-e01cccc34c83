from textwrap import dedent
from typing import Dict, Any, List
import json
from agno.models.openai.chat import OpenAIChat
from agno.agent.agent import Agent


from internals.dtos.recommendation import RecommendationInputDTO
from internals.usecases.recommendation_knowledge import RecommendationKnowledge


class SafetyFilterAgent:
    def __init__(self, knowledge: RecommendationKnowledge, openai_api_key: str):
        self.knowledge = knowledge
        self.openai_api_key = openai_api_key
        self._model_id = "gpt-4o-mini"

    def _get_knowledge(self):
        return self.knowledge.knowledge_base

    def _create_safety_agent(self) -> Agent:
        return Agent(
            name="safety_filter_agent",
            model=OpenAIChat(
                id=self._model_id,
                api_key=self.openai_api_key,
                temperature=0.1,
                max_tokens=10000,
            ),
            knowledge=self._get_knowledge(),
            search_knowledge=True,
            description="You are a medical safety specialist for skincare treatments with expertise in contraindication analysis.",
            goal="Filter treatments based on user medical history and contraindications to ensure patient safety.",
            instructions=self._get_safety_instructions(),
            expected_output=self._get_safety_output_format(),
            show_tool_calls=True,
            stream=True,
        )

    def _get_safety_instructions(self) -> str:
        return dedent("""
        You are a medical safety specialist for skincare treatments. Your ONLY job is to find treatments that are safe for the user based on their medical contraindications.

        ## MEDICAL SAFETY FILTERING PROCESS:

        **STEP 1: ANALYZE USER SURVEY FOR CONTRAINDICATIONS**
        - Review user survey answers for contraindication questions
        - Identify contraindication categories: pregnancy, allergies, medications, medical history, skin conditions
        - For each "ya" (yes) answer, note the specific contraindication

        **STEP 2: SEARCH THROUGH ALL TREATMENTS**
        - **CRITICAL**: You MUST search through ALL treatments in the knowledge base
        - Use multiple search queries to ensure comprehensive coverage:
          * Search for "treatment" OR "facial" OR "injection" OR "laser" OR "peeling" OR "mesogun" OR "dermapen" OR "trilogy"
          * Search for specific treatment categories: "Facial", "Injection", "Laser", "Peeling Treatment", "Mesogun", "Dermapen", "Trilogy", "HEAT BASED", "Single Treatment"
          * Search for specific treatment names: "Ageless Therapy", "Derma Snow White Solution", "Exocell 22", "PRP Wajah", "Salmon DNA Vital", etc.
        - **MANDATORY**: Ensure you find at least 50+ treatments to analyze

        **STEP 3: CHECK TREATMENT CONTRAINDICATIONS**
        - For each treatment found, check these specific contraindication columns:
          * "Client sedang Hamil/Sedang pengobatan kanker)" - Pregnancy/Cancer treatment
          * "Client memiliki alergi Salmon" - Salmon allergy
          * "Client memiliki alergi Kacang-kacangan" - Nut allergy
          * "Client sedang mengonsumsi Obat Hormonal" - Hormonal medication
          * "Client sedang mengonsumsi Obat Anti Biotik" - Antibiotic medication
          * "Client sedang mengonsumsi Obat Isotretinoin?" - Isotretinoin medication
          * "Client sedang Menyusui" - Breastfeeding
          * "Client sedang mengonsumsi Obat Pengencer Darah (<3 bulan)" - Blood thinner medication
          * "Client memiliki riwayat penyakit autoimun" - Autoimmune history
          * "Client memiliki riwayat Penyakit Diabetes tidak terkontrol" - Uncontrolled diabetes
          * "Client memiliki riwayat Penyakt Kolesterol Tinggi" - High cholesterol
          * "Client memiliki riwayat Penyakit Pernah Kanker" - Cancer history

        **STEP 4: APPLY SAFETY RULES**
        - **ONLY INCLUDE** treatments where ALL contraindication fields for user's "ya" answers are "Boleh"
        - **EXCLUDE** treatments where ANY contraindication field for user's "ya" answers is "Tidak Boleh"
        - Safety rules:
          * If user answered "ya" and treatment contraindications field = "Tidak Boleh" → EXCLUDE treatment
          * If user answered "ya" and treatment contraindications field = "Boleh" → INCLUDE treatment
          * If user answered "tidak" → treatment is safe (include regardless of contraindication value)

        **STEP 5: CREATE SAFE TREATMENTS LIST**
        - Only include treatments that have "Boleh" for ALL user's "ya" contraindications
        - Medical safety takes absolute priority over all other factors

        **CRITICAL SAFETY RULES:**
        - **ONLY** treatments with "Boleh" for user's contraindications are safe
        - Any treatment with "Tidak Boleh" for user's contraindications is UNSAFE
        - When in doubt about safety → EXCLUDE the treatment
        - Better to exclude a safe treatment than include an unsafe one
        - **MANDATORY**: You must analyze ALL treatments in the database, not just a subset

        **VALIDATION CHECK:**
        - For each treatment you mark as "safe", verify that ALL contraindication fields for user's "ya" answers are "Boleh"
        - If ANY contraindication field for user's "ya" answers is "Tidak Boleh", the treatment MUST be excluded
        - Double-check your logic: User has autoimmune history → Only treatments with "Boleh" for autoimmune are safe
        - **CRITICAL**: Safe treatments should show "Boleh" for user's contraindications, not "Tidak Boleh"

        **SEARCH STRATEGY:**
        - Use broad search terms to find ALL treatments
        - Search multiple times with different keywords
        - If you find less than 40 treatments, search again with different terms
        - Common treatment categories to search: Facial, Injection, Laser, Peeling, Mesogun, Dermapen, Trilogy, HEAT BASED, Single Treatment

        **DO NOT:**
        - Consider treatment relevance to user concerns (that's the recommendation agent's job)
        - Consider treatment popularity or effectiveness
        - Include any treatment with contraindications
        - Make assumptions about treatment safety
        - Stop searching if you find only a few treatments
        - Focus on excluded treatments (only focus on safe treatments)

        **DO:**
        - Be extremely conservative about safety
        - Check every contraindication field thoroughly
        - Exclude treatments if there's any doubt about safety
        - Focus ONLY on medical safety, nothing else
        - Search comprehensively through ALL treatments
        - Ensure you analyze at least 50+ treatments
        - **ONLY** include treatments with "Boleh" for user's contraindications
        """)

    def _get_safety_output_format(self) -> str:
        return dedent("""
        Provide the response in this exact JSON format:
        ```json
        {
            "status": "success",
            "data": {
                "safe_treatments": [
                    {
                        "name": "Treatment Name",
                        "description": "Exact description from treatment data",
                        "categories": ["Exact categories from treatment data"],
                        "price": 0,
                        "quantity": 1,
                        "solved_concerns": ["ALL concern names from treatment data"],
                        "is_top_recommendation": true/false,
                        "contraindications": "Exact contraindications field from treatment data"
                    }
                ],
                "user_contraindications": ["List of contraindications from user survey where user answered 'ya'"]
            },
            "error": null
        }
        ```

        **IMPORTANT**: Only include treatments where ALL contraindication fields for user's "ya" answers are "Boleh".
        Do not include any treatments with "Tidak Boleh" for user's contraindications.
        """)

    def _clean_json_content(self, content: str) -> str:
        json_content = content.strip()

        if "```json" in json_content:
            json_content = json_content.split("```json", 1)[1]
        if "```" in json_content:
            json_content = json_content.split("```", 1)[0]

        if json_content.startswith("json"):
            json_content = json_content[4:].strip()

        return json_content.strip()

    def filter_safe_treatments(self, dto: RecommendationInputDTO) -> Dict[str, Any]:
        """Filter treatments based on user survey contraindications."""

        # Format user survey data into JSON if available
        user_survey_json = None
        if dto.user_survey and dto.user_survey.results:
            user_survey_json = {
                "id": str(dto.user_survey.id) if dto.user_survey.id else None,
                "user_id": str(dto.user_survey.user_id)
                if dto.user_survey.user_id
                else None,
                "skin_analyze_id": str(dto.user_survey.skin_analyze_id)
                if dto.user_survey.skin_analyze_id
                else None,
                "results": [
                    {
                        "question": result.question,
                        "answers": result.answers,
                        "category": result.category,
                    }
                    for result in dto.user_survey.results
                ],
                "created_at": dto.user_survey.created_at,
                "updated_at": dto.user_survey.updated_at,
            }

        agent = self._create_safety_agent()

        message = f"""
        ## User Survey Data
        {json.dumps(user_survey_json, indent=2) if user_survey_json else "No survey data available"}

        ## TASK:
        Find ALL treatments in the knowledge base that are safe for this user based on their medical contraindications.
        Return ONLY treatments where ALL contraindication fields for user's "ya" answers are "Boleh".

        ## CRITICAL INSTRUCTIONS:
        1. **SEARCH COMPREHENSIVELY**: You MUST search through ALL treatments in the database
        2. **USE MULTIPLE SEARCH TERMS**: Search for "treatment", "facial", "injection", "laser", "peeling", "mesogun", "dermapen", "trilogy", "heat based", "single treatment"
        3. **FIND AT LEAST 50+ TREATMENTS**: If you find less than 50 treatments, search again with different terms
        4. **CHECK ALL CONTRAINDICATION COLUMNS**: Look at all 12 contraindication fields for each treatment
        5. **BE THOROUGH**: Don't stop until you've analyzed the complete treatment database

        ## SAFETY VALIDATION:
        - **CRITICAL**: For each treatment you mark as "safe", the contraindication field for user's "ya" answers MUST be "Boleh"
        - If the contraindication field is "Tidak Boleh" for user's "ya" answers → EXCLUDE the treatment
        - **EXAMPLE**: User has autoimmune history → Only treatments with "Boleh" for "Client memiliki riwayat penyakit autoimun" are safe
        - **DOUBLE-CHECK**: Safe treatments should show "Boleh" in contraindications, not "Tidak Boleh"
        - **ONLY** include treatments with "Boleh" for user's contraindications

        ## EXPECTED RESULTS:
        - For a user with autoimmune history, you should find approximately 8 safe treatments
        - Total treatments analyzed should be 50+
        - Safe treatments must have "Boleh" for autoimmune contraindication
        - Focus ONLY on safe treatments, ignore excluded treatments

        Start your comprehensive search now and find ALL safe treatments in the database.
        """

        response_content = ""

        for resp in agent.run(message, stream=True):
            if resp.content:
                response_content += resp.content

        return json.loads(self._clean_json_content(response_content))


class RecommendationAgent:
    def __init__(self, knowledge: RecommendationKnowledge, openai_api_key: str):
        self.knowledge = knowledge
        self.openai_api_key = openai_api_key
        self._model_id = "gpt-4o-mini"

    def _get_knowledge(self):
        return self.knowledge.knowledge_base

    def _create_recommendation_agent(self) -> Agent:
        return Agent(
            name="treatment_recommendation_agent",
            model=OpenAIChat(
                id=self._model_id,
                api_key=self.openai_api_key,
                temperature=0.1,
                max_tokens=10000,
            ),
            knowledge=self._get_knowledge(),
            search_knowledge=False,  # DISABLED: Only work with provided safe treatments
            description="You are SkinAI+, a professional skincare doctor with over 10 years of experience specializing in personalized skincare for Indonesian customers.",
            goal="Recommend the most relevant treatments from a pre-filtered safe treatments list based on user skin concerns.",
            instructions=self._get_recommendation_instructions(),
            expected_output=self._get_recommendation_output_format(),
            show_tool_calls=True,
            stream=True,
        )

    def _get_recommendation_instructions(self) -> str:
        return dedent("""
        You are SkinAI+, a professional skincare doctor. Your job is to recommend treatments from a PRE-FILTERED SAFE TREATMENTS LIST based on user skin concerns.

        ## RECOMMENDATION PROCESS:

        **CRITICAL**: You are working with a SAFE TREATMENTS LIST that has already been filtered for medical contraindications.
        - **ONLY** use treatments from the provided safe treatments list
        - **DO NOT** search the knowledge base for additional treatments
        - **DO NOT** recommend treatments that are not in the safe treatments list
        - All treatments in this list are medically safe for the user

        **STEP 1: ANALYZE USER CONCERNS**
        - Review the user's specific skin concerns
        - Understand the priority and severity of each concern

        **STEP 2: MATCH CONCERNS TO SAFE TREATMENTS**
        - **ONLY** search within the provided safe treatments list for treatments that address user concerns
        - Look for treatments where user concerns appear in CONCERN 1-6 fields
        - Prioritize treatments where user concerns appear in earlier concern fields (CONCERN 1-2)
        - **DO NOT** search outside the safe treatments list

        **STEP 3: TREATMENT SELECTION PRIORITY**
        - **FIRST PRIORITY**: Safe treatments with is_top_recommendation = true that address user concerns
        - **SECOND PRIORITY**: Other safe relevant treatments that directly address user concerns
        - **MANDATORY**: Include ALL safe top recommendations that address user concerns
        - **ACCURACY OVER COVERAGE**: Only recommend treatments that truly address user concerns
        - If no treatments match user concerns well, it's better to return fewer or no recommendations

        **STEP 4: FILTER SOLVED_CONCERNS FOR USER RELEVANCE**
        - Only include concerns from treatment's CONCERN 1-6 fields that MATCH user's actual concerns
        - If no concerns match user concerns → use empty solved_concerns []
        - Example: Treatment has CONCERN 1: "Scar", CONCERN 2: "Wrinkle", CONCERN 3: "Pore"
          User concerns: ["Pore", "Scar"] → solved_concerns: ["Scar", "Pore"] (exclude "Wrinkle")

        **RELEVANCE RULES:**
        - User concerns must EXACTLY MATCH the CONCERN 1-6 fields in safe treatment data
        - **ONLY** use treatments from the provided safe treatments list
        - Prioritize safe treatments where user concerns appear in earlier concern fields (CONCERN 1-2)
        - **DO NOT** search the knowledge base for additional treatments
        - **ACCURACY FIRST**: Only recommend treatments that truly address user concerns

        **TOP RECOMMENDATION RULES:**
        - Check "IS TOP RECOMMENDATION" field in treatment data
        - If "true" or "yes" → Set is_top_recommendation = true
        - If "false" or "no" or empty → Set is_top_recommendation = false
        - Use exactly as it appears in the data - do not change the value

        **SUMMARY FORMAT:**
        - MUST be exactly: "Based on the analysis and survey results, you have concerns with {user_concerns}. Here are the treatment recommendations we specifically suggest for you."
        - Do not change the concern list or use different wording

        **DO:**
        - Focus on treatment relevance to user concerns
        - Use ONLY treatments from the provided safe treatments list
        - Search for each user concern individually in the safe treatments list
        - Include at least one safe treatment per user concern where that concern is CONCERN 1
        - Filter solved_concerns to only include concerns that match user's actual concerns
        - Prioritize safe top recommendations first in the list
        - **PRIORITIZE ACCURACY**: Only recommend treatments that truly address user concerns
        - **ALLOW EMPTY RESULTS**: If no good matches, it's better to return fewer recommendations

        **DON'T:**
        - Invent or modify treatment data (names, descriptions, concerns, prices, etc.)
        - Add concerns that are not in the user's concern list to solved_concerns
        - Include made-up concerns like: hydration, moisture, collagen boost, oxygenation, regeneration, detoxification, uneven texture, skin texture, dead skin cells, acne scars, exfoliation, collagen production, lip rejuvenation, skin renewal, body contouring, firmness, acne, comedo, oil control, clogged pores, deep cleansing, overall skin health, skin purification, sensitivity (unless actually in user concerns)
        - Change the is_top_recommendation value from what appears in the data
        - Recommend treatments that don't address any user concerns
        - Use different wording for the summary format
        - Worry about medical safety (already handled by safety filter)
        - **SEARCH THE KNOWLEDGE BASE** for additional treatments
        - **RECOMMEND TREATMENTS** that are not in the provided safe treatments list
        - **FORCE RECOMMENDATIONS** when there are no good matches
        - **RECOMMEND IRRELEVANT TREATMENTS** just to have more recommendations

        Remember: You are working with a pre-filtered safe list. Focus on finding the most relevant treatments for the user's concerns from ONLY the provided safe treatments list. **ACCURACY IS MORE IMPORTANT THAN QUANTITY**.
        """)

    def _get_recommendation_output_format(self) -> str:
        return dedent("""
        Provide the response in this exact JSON format:
        ```json
        {
            "status": "success",
            "data": {
                "summary": "Based on the analysis and survey results, you have concerns with [formatted_concerns]. Here are the treatment recommendations we specifically suggest for you.",
                "treatments": [
                    {
                        "name": "Treatment Name",
                        "description": "Exact description from treatment data",
                        "categories": ["Exact categories from treatment data"],
                        "price": 0,
                        "quantity": 1,
                        "solved_concerns": ["ALL concern names from treatment data - DO NOT INVENT"],
                        "is_top_recommendation": true/false
                    }
                ]
            },
            "error": null
        }
        ```
        """)

    def _clean_json_content(self, content: str) -> str:
        json_content = content.strip()

        if "```json" in json_content:
            json_content = json_content.split("```json", 1)[1]
        if "```" in json_content:
            json_content = json_content.split("```", 1)[0]

        if json_content.startswith("json"):
            json_content = json_content[4:].strip()

        return json_content.strip()

    def get_treatment_recommendations(
        self, user_concerns: List[str], safe_treatments: List[Dict[str, Any]]
    ) -> str:
        """Generate treatment recommendations from safe treatments list based on user concerns."""

        agent = self._create_recommendation_agent()

        message = f"""
        ## SAFE TREATMENTS LIST (Pre-filtered for medical safety):
        {json.dumps(safe_treatments, indent=2)}

        ## USER'S FINAL CONCERNS:
        The user has these specific concerns: {', '.join(user_concerns)}

        ## TASK:
        From the safe treatments list above, recommend the most relevant treatments for the user's concerns.
        Focus on treatment relevance and effectiveness for the user's specific skin concerns.

        ## CRITICAL INSTRUCTIONS:
        1. **ONLY** use treatments from the provided safe treatments list above
        2. **DO NOT** search the knowledge base for additional treatments
        3. **DO NOT** recommend treatments that are not in the safe treatments list
        4. All treatments in the list are medically safe for the user
        5. Focus on finding the most relevant treatments for the user's concerns: {', '.join(user_concerns)}

        ## ACCURACY OVER COVERAGE:
        - **PRIORITIZE ACCURACY**: Only recommend treatments that truly address user concerns
        - **ALLOW EMPTY RESULTS**: If no treatments match user concerns well, it's better to return fewer or no recommendations
        - **DO NOT FORCE RECOMMENDATIONS**: Don't recommend irrelevant treatments just to have more recommendations
        - **QUALITY OVER QUANTITY**: Better to have 2 accurate recommendations than 10 irrelevant ones

        ## EXPECTED BEHAVIOR:
        - Only recommend treatments that appear in the safe treatments list
        - Match user concerns to treatment concerns (CONCERN 1-6 fields)
        - Prioritize top recommendations (is_top_recommendation = true)
        - If no good matches exist, return fewer or no recommendations
        - **ACCURACY IS MORE IMPORTANT THAN QUANTITY**

        Start recommending treatments from ONLY the provided safe treatments list, prioritizing accuracy over coverage.
        """

        response_content = ""

        for resp in agent.run(message, stream=True):
            if resp.content:
                response_content += resp.content

        return self._clean_json_content(response_content)


class RecommendationAgentManager:
    def __init__(self, knowledge: RecommendationKnowledge, openai_api_key: str):
        self.safety_agent = SafetyFilterAgent(knowledge, openai_api_key)
        self.recommendation_agent = RecommendationAgent(knowledge, openai_api_key)

    def get_treatment_recommendations(self, dto: RecommendationInputDTO) -> str:
        """Two-step process: First filter for safety, then recommend from safe treatments."""

        # Step 1: Filter treatments for safety
        safety_result = self.safety_agent.filter_safe_treatments(dto)

        if safety_result["status"] != "success":
            return json.dumps(
                {
                    "status": "error",
                    "data": None,
                    "error": "Failed to filter treatments for safety",
                }
            )

        # Get safety results
        safe_treatments = safety_result["data"]["safe_treatments"]

        # If no safe treatments, return empty result
        if not safe_treatments:
            return json.dumps(
                {
                    "status": "success",
                    "data": {
                        "summary": f"Based on the analysis and survey results, you have concerns with {', '.join(dto.top_concern)}. However, no treatments are currently safe for your medical profile. Please consult with a healthcare provider for personalized recommendations.",
                        "treatments": [],
                    },
                    "error": null,
                }
            )

        # Step 2: Recommend treatments from safe list
        user_concerns = dto.top_concern
        recommendation_result = self.recommendation_agent.get_treatment_recommendations(
            user_concerns, safe_treatments
        )

        return recommendation_result
