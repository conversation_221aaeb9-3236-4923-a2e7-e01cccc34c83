import os
import re
from io import By<PERSON><PERSON>
from typing import Dict, <PERSON><PERSON>
from datetime import date, datetime
from pdf2image import convert_from_bytes
from PIL import Image
import pytesseract
import json
import tempfile
import numpy as np

from agno.models.openai.chat import OpenAIChat
from agno.agent import Agent
from agno.media import Image as AgnoImage

from configs.environment import Environment


class PDFExtractorUseCase:
    def __init__(self):
        pytesseract.pytesseract.tesseract_cmd = Environment.get_tesseract_path()

    def pdf_to_images(self, file_content):
        images = convert_from_bytes(file_content)
        image_buffers = []

        for image in images:
            img_byte_arr = BytesIO()
            image.save(img_byte_arr, format="PNG")
            _ = img_byte_arr.seek(0)
            image_buffers.append(img_byte_arr)

        return image_buffers

    def image_to_text(self, image_buffer):
        image = Image.open(image_buffer)

        # reference:
        # https://github.com/tesseract-ocr/tesseract/blob/main/doc/tesseract.1.asc
        text = pytesseract.image_to_string(image, config="--psm 6")
        return text

    def process_image_with_openai(self, image_buffer, machine: str):
        agent = Agent(
            model=OpenAIChat(id="gpt-4.1-mini-2025-04-14"),
        )

        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp_file:
            img_byte_arr = BytesIO(image_buffer.getvalue())
            _ = temp_file.write(img_byte_arr.read())
            temp_file_path = temp_file.name

        prompt: str = ""

        if machine == "m9":
            prompt = """
            Extract the following values from a PDF document and convert them into a structured JSON format:
                1. 'Evaluation:' – a integer of percentage value.
                2. 'Skin Age:' – a numerical value in one line with evaluation.
                3. 'Skin Condition:' – a text or percentage value in one line with evaluation.
                4. '[Evaluation]' – a detailed chart representing the evaluation, where each element should be extracted as key-value pairs under the key 'evaluation_chart'.
                5. '[Skin Care Suggestion]' – a text containing recommendations for skin care.
            The JSON structure should look like this:
            {
                'evaluation': 10,
                'skinage': 20,
                'skincondition': 'Poor',
                'evaluationchart': {
                    'r_g_b_pore': 51,
                    'r_g_b_spot': 52,
                    'r_g_b_wrinkle': 53,
                    'p_l_texture': 54,
                    'u_v_porphyrin': 55,
                    'u_v_pigmentation': 56,
                    'u_v_moisture': 57,
                    'sensitive_area': 58,
                    'brown_area': 59,
                    'u_v_damage': 60,
                },
                'skincaresuggestion': '<extracted suggestion>'
            }
            Just return the JSON data.
            """

        elif machine == "mirror":
            prompt = """
            Extract the following values from a PDF document and convert them into a structured JSON format:
                    1. A detailed chart representing the evaluation, where each element should be extracted as key-value pairs under the key 'evaluation_chart'.
                    2. A suggestion analysis which contains a paragraph of text and put them under 'suggestion'.
            The JSON structure should look like this:
            {
                'evaluation_chart': {
                    'rgb_pore': {
                        'blue_value': 60,
                        'purple_value': 40,
                    },
                    'rgb_spot': {
                        'blue_value': 61,
                        'purple_value': 41,
                    },
                    'rgb_wrinkle': {
                        'blue_value': 62,
                        'purple_value': 42,
                    },
                    'pl_texture': {
                        'blue_value': 63,
                        'purple_value': 43,
                    },
                    'uv_porphyrin': {
                        'blue_value': 64,
                        'purple_value': 44,
                    },
                    'uv_pigmentation': {
                        'blue_value': 65,
                        'purple_value': 45,
                    },
                    'uv_moisture': {
                        'blue_value': 66,
                        'purple_value': 46,
                    },
                    'sensitive_area': {
                        'blue_value': 67,
                        'purple_value': 47,
                    },
                    'brown_area': {
                        'blue_value': 68,
                        'purple_value': 48,
                    },
                    'uv_damage': {
                        'blue_value': 69,
                        'purple_value': 49,
                    },
                },
                'suggestion': 'some suggestion',
            }
            Just return the JSON data.
            """

        else:
            raise ValueError("Selected machine does not have generative AI prompt.")

        response = agent.run(
            message=prompt,
            images=[AgnoImage(filepath=temp_file_path)],
        )

        os.remove(temp_file_path)

        if response.content is not None:
            try:
                content = response.content.strip("```json\n").strip("```")
                return json.loads(content)

            except json.JSONDecodeError as e:
                print("Failed to decode JSON: ", e)
                raise ValueError("Invalid JSON returned from the generative model.")

        else:
            raise ValueError("Extract report with OpenAI have no content.")

    @staticmethod
    def to_snake_case(s):
        s = s.replace(" ", "").replace("_", "")
        s = s.replace("[", "").replace("]", "")
        return re.sub(r"(?<!^)(?=[A-Z])", "_", s).lower()

    @staticmethod
    def convert_keys_to_snake_case(data):
        if isinstance(data, dict):
            return {
                PDFExtractorUseCase.to_snake_case(
                    k
                ): PDFExtractorUseCase.convert_keys_to_snake_case(v)
                for k, v in data.items()
            }
        elif isinstance(data, list):
            return [
                PDFExtractorUseCase.convert_keys_to_snake_case(item) for item in data
            ]
        else:
            return data

    def extract_general_info(self, text):
        m9_pattern = r"(\w+(?: \w+)*)\W+(\d{4})-?(\d{2})-?(\d{2})\W+(\d{9,14})(?:\W|\S)+(\d{4}-\d{2}-\d{2})"
        m9_match = re.search(m9_pattern, text)

        metis_pattern = r"Patient name: (\w+(?: \w+)*)\s+Test store: (?:\w.*)*\sTest date: (\d{4})-?(\d{2})-?(\d{2})"
        metis_match = re.search(metis_pattern, text)

        mirror_pattern = (
            r"-?([a-zA-Z]+)\s+(\d{9,14})\s+\S+\s+(\d{2})\s+(\d{4}-\d{2}-\d{2})"
        )
        mirror_match = re.search(mirror_pattern, text)

        if m9_match:
            name = m9_match.group(1)
            birthdate = f"{m9_match.group(2)}-{m9_match.group(3)}-{m9_match.group(4)}"
            phone = m9_match.group(5)
            analytic_date = m9_match.group(6)

            today = date.today()
            birthdate_datetime = datetime.strptime(birthdate, "%Y-%m-%d")
            actual_age = (
                today.year
                - birthdate_datetime.year
                - (
                    (today.month, today.day)
                    < (birthdate_datetime.month, birthdate_datetime.day)
                )
            )

            return {
                "name": name,
                "actual_age": actual_age,
                "phone": phone,
                "analytic_date": analytic_date,
                "machine": "m9",
            }

        elif metis_match:
            name = metis_match.group(1)
            analytic_date = (
                f"{metis_match.group(2)}-{metis_match.group(3)}-{metis_match.group(4)}"
            )

            return {
                "name": name,
                "analytic_date": analytic_date,
                "machine": "metis",
            }

        elif mirror_match:
            name = mirror_match.group(1)
            phone = mirror_match.group(2)
            actual_age = mirror_match.group(3)
            analytic_date = mirror_match.group(4)

            return {
                "name": name,
                "actual_age": actual_age,
                "phone": phone,
                "analytic_date": analytic_date,
                "machine": "mirror",
            }

        else:
            raise ValueError("No match found in the extracted text report.")

    def extract_report_metis_skin_result(self, text):
        pattern = r"Spots (\d+) \d+\s+Pores (\d+) \d+\s+Wrinkle (\d+)"
        match = re.search(pattern, text)

        if match:
            rgb_spot = match.group(1)
            rgb_pore = match.group(2)
            rgb_wrinkle = match.group(3)

            return {
                "rgb_spot": rgb_spot,
                "rgb_pore": rgb_pore,
                "rgb_wrinkle": rgb_wrinkle,
            }
        else:
            raise ValueError(
                "No match found in the extracted text skin result metis report."
            )

    def extract_report_mirror_skin_result(self, skin_result: Dict):
        evaluation_chart = skin_result["evaluation_chart"]

        return {
            "suggestion": skin_result["suggestion"],
            "rgb_pore": evaluation_chart["rgb_pore"]["blue_value"],
            "rgb_spot": evaluation_chart["rgb_spot"]["blue_value"],
            "rgb_wrinkle": evaluation_chart["rgb_wrinkle"]["blue_value"],
            "pl_texture": evaluation_chart["pl_texture"]["blue_value"],
            "uv_porphyrin": evaluation_chart["uv_porphyrin"]["blue_value"],
            "uv_pigmentation": evaluation_chart["uv_pigmentation"]["blue_value"],
            "uv_moisture": evaluation_chart["uv_moisture"]["blue_value"],
            "sensitive_area": evaluation_chart["sensitive_area"]["blue_value"],
            "brown_area": evaluation_chart["brown_area"]["blue_value"],
            "uv_damage": evaluation_chart["uv_damage"]["blue_value"],
        }

    def crop_image_buffer(
        self,
        image_buffer: BytesIO,
        crop_slices: Tuple[slice, slice, slice],
    ) -> BytesIO:
        image = Image.open(image_buffer)
        image_array = np.array(image)

        cropped_array = image_array[crop_slices]
        cropped_image = Image.fromarray(cropped_array)

        buffer = BytesIO()
        cropped_image.save(buffer, format="PNG")
        return buffer

    def extract_pdf(self, file_content):
        image_buffers = self.pdf_to_images(file_content)

        first_page = self.image_to_text(image_buffers[0])
        general_info = self.extract_general_info(first_page)

        snake_case_result = {}

        if general_info["machine"] == "m9":
            if len(image_buffers) == 1:
                buffer = self.crop_image_buffer(
                    image_buffers[0], (slice(1000, None), slice(None), slice(None))
                )
                result = self.process_image_with_openai(
                    buffer,
                    general_info["machine"],
                )
            else:
                result = self.process_image_with_openai(
                    image_buffers[1],
                    general_info["machine"],
                )

            snake_case_result = result

        elif general_info["machine"] == "metis":
            skin_result = self.image_to_text(image_buffers[3])
            snake_case_result = self.extract_report_metis_skin_result(skin_result)

        elif general_info["machine"] == "mirror":
            buffer = self.crop_image_buffer(
                image_buffers[0], (slice(500, None), slice(None), slice(None))
            )
            skin_result = self.process_image_with_openai(
                buffer,
                general_info["machine"],
            )

            snake_case_result = self.extract_report_mirror_skin_result(skin_result)

        else:
            raise TypeError("Report is not recognized")

        return {**general_info, **snake_case_result}
