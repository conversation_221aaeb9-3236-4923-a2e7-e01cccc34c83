"""
Module for Summary Usecases
"""

from configs.environment import Environment
from internals.usecases.summary_agent import SummaryAgent
from openai import OpenAI
from internals.dtos.summary import SummaryInputDTO, SummaryOutputDTO


class SummaryUsecase:
    def __init__(self):
        # Initialize OpenAI client
        self.openai_client = OpenAI(
            api_key=Environment.get_openai_api_key(),
        )

    def generate_summary(self, dto: SummaryInputDTO) -> SummaryOutputDTO:
        summary_agent = SummaryAgent(Environment.get_openai_api_key())
        summary = summary_agent.get_summary(dto.top_concern, dto.user_concern)

        return SummaryOutputDTO(data={"summary": summary})
