import logging
from typing import List
import httpx
import random
from collections import defaultdict

from configs.environment import Environment
from internals.dtos.recommendation import (
    RecommendationInputDTO,
    TreatmentDTO,
)

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class TreatmentRecommendationEngine:
    async def fetch_data(self, page: int = 1, page_size: int = 10):
        url = f"{Environment.get_api_url()}/api/v1/treatment-product?page={page}&page_size={page_size}"
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            _ = response.raise_for_status()
            return response.json().get("data", {})

    async def fetch_all_data(self, page_size: int = 10):
        all_data = []
        current_page = 1

        while True:
            response = await self.fetch_data(page=current_page, page_size=page_size)
            content = response.get("content", [])
            all_data.extend(content)

            total_pages = response.get("total_pages", 1)
            if current_page >= total_pages:
                break
            current_page += 1

        return all_data

    async def _mapping_treatments(self):
        try:
            logger.info("[INFO] Fetching treatment data...")
            records = await self.fetch_all_data(page_size=10)
            logger.info(f"[INFO] Retrieved {len(records)} treatment records.")

            treatments = []
            for row in records:
                if row.get("type") != "treatment":
                    continue

                treatment = {
                    "id": row.get("id"),
                    "item_code": row.get("item_code"),
                    "name": row.get("name"),
                    "media_url": row.get("media_url"),
                    "thumbnail_url": row.get("thumbnail_url"),
                    "description": row.get("description", ""),
                    "categories": [
                        cat.get("name") for cat in (row.get("category") or []) if cat
                    ],
                    "quantity": row.get("quantity"),
                    "price": row.get("price"),
                    "is_top_recommendation": row.get("is_top_recommendation"),
                    "interval": row.get("interval", {}).get("days")
                    if row.get("interval")
                    else None,
                }

                concerns = row.get("concern") or []
                for i, c in enumerate(concerns[:6]):
                    treatment[f"concern_{i + 1}"] = c.get("name")

                contraindications = []
                for survey in row.get("survey_questions") or []:
                    question = (
                        survey.get("question", "")
                        .replace("Are you ", "")
                        .replace("Do you ", "")
                        .replace("?", "")
                    )
                    if survey.get("selected_answer") == 1:
                        contraindications.append(question)
                treatment["contraindications"] = contraindications

                treatments.append(treatment)

            return treatments

        except Exception as e:
            logger.error(f"[ERR] Failed mapping treatments: {e}")
            return []

    async def _filter_treatment_phase(self, dto: RecommendationInputDTO):
        try:
            sa_skin_concerns = dto.sa_skin_concerns
            survey_skin_concerns = dto.survey_skin_concerns
            skin_concerns = list(dict.fromkeys(sa_skin_concerns + survey_skin_concerns))

            user_contras = []
            for survey in dto.survey_contraindications:
                question = (
                    survey.question.replace("Are you ", "")
                    .replace("Do you ", "")
                    .replace("?", "")
                )
                if "no" not in survey.answers[0].lower().strip():
                    user_contras.append(question)

            user_treatment_intervals = dto.treatment_intervals
            treatments = await self._mapping_treatments()
            filtered = []

            if not treatments:
                return filtered

            for treatment in treatments:
                treatment_concerns = [
                    v.lower().strip()
                    for k, v in treatment.items()
                    if k.startswith("concern_") and isinstance(v, str)
                ]

                if not any(
                    c.lower().strip() in treatment_concerns for c in skin_concerns
                ):
                    continue

                treatment_contras = [
                    c.lower().strip() for c in treatment.get("contraindications", [])
                ]
                if any(user_c in treatment_contras for user_c in user_contras):
                    continue

                treatment_interval = treatment.get("interval", 0)
                treatment_name = treatment.get("name", "")
                if any(
                    ti.name == treatment_name and ti.days <= treatment_interval
                    for ti in user_treatment_intervals
                ):
                    continue

                filtered.append(treatment)

            return filtered
        except Exception as e:
            logger.error(f"[ERR] Failed filter treatments: {e}")
            return []

    def _rank_treatments(
        self,
        treatments: list,
        sa_skin_concerns: List[str],
        survey_skin_concerns: List[str],
    ) -> list:
        try:
            weight_survey = 25
            weight_sa = 3
            top_recommendation_bonus = 30

            survey_set = set([c.lower() for c in survey_skin_concerns])
            sa_set = set([c.lower() for c in sa_skin_concerns]) - survey_set

            ranked_treatments = []

            for treatment in treatments:
                score = 0

                for i in range(1, 7):
                    key = f"concern_{i}"
                    concern_value = treatment.get(key)
                    if isinstance(concern_value, str):
                        concern = concern_value.lower().strip()
                        position_weight = 7 - i

                        if concern in survey_set:
                            score += weight_survey
                            score += position_weight
                        elif concern in sa_set:
                            score += weight_sa
                            score += position_weight

                if treatment.get("is_top_recommendation"):
                    score += top_recommendation_bonus

                treatment["raw_score"] = score
                ranked_treatments.append(treatment)

            scores = [t["raw_score"] for t in ranked_treatments]
            if not scores:
                return []
            min_score = min(scores)
            max_score = max(scores)

            for treatment in ranked_treatments:
                if max_score == min_score:
                    normalized = 1.0
                else:
                    normalized = (treatment["raw_score"] - min_score) / (
                        max_score - min_score
                    )
                treatment["total_score"] = round(normalized, 4)
                del treatment["raw_score"]

            return sorted(
                ranked_treatments, key=lambda x: x["total_score"], reverse=True
            )
        except Exception as e:
            logger.error(f"[ERR] Failed ranking treatments: {e}")
            return []

    def _interleave_treatments_by_concern(self, ranked_treatments: list, skin_concerns: List[str]) -> list:
        """
        Interleave treatments with same score to avoid consecutive same concerns.
        This ensures diversity in the top recommendations when scores are tied.
        """
        if not ranked_treatments:
            return ranked_treatments

        score_groups = defaultdict(list)
        for treatment in ranked_treatments:
            score = treatment.get("total_score", 0)
            score_groups[score].append(treatment)

        interleaved_ranked = []
        for score in sorted(score_groups.keys(), reverse=True):
            treatments_in_group = score_groups[score]

            if len(treatments_in_group) == 1:
                interleaved_ranked.extend(treatments_in_group)
            else:
                treatments_with_primary_concern = []
                for treatment in treatments_in_group:
                    primary_concern = None
                    for i in range(1, 7):
                        concern_key = f"concern_{i}"
                        concern_value = treatment.get(concern_key)
                        if isinstance(concern_value, str) and concern_value.lower().strip() in [c.lower().strip() for c in skin_concerns]:
                            primary_concern = concern_value.lower().strip()
                            break
                    treatments_with_primary_concern.append((treatment, primary_concern))

                concern_groups = defaultdict(list)
                for treatment, concern in treatments_with_primary_concern:
                    concern_groups[concern].append(treatment)

                max_len = max(len(group) for group in concern_groups.values()) if concern_groups else 0
                interleaved_group = []

                for i in range(max_len):
                    for concern, treatments in concern_groups.items():
                        if i < len(treatments):
                            interleaved_group.append(treatments[i])

                interleaved_ranked.extend(interleaved_group)

        return interleaved_ranked

    async def generate_summary(self, dto: RecommendationInputDTO) -> str:
        sa_skin_concerns = dto.sa_skin_concerns
        survey_skin_concerns = dto.survey_skin_concerns
        skin_concerns = list(dict.fromkeys(survey_skin_concerns + sa_skin_concerns))

        if len(skin_concerns) > 2:
            formatted_concerns = (
                ", ".join(skin_concerns[:-1]) + f", and {skin_concerns[-1]}"
            )
        elif len(skin_concerns) == 2:
            formatted_concerns = " and ".join(skin_concerns)
        elif len(skin_concerns) == 1:
            formatted_concerns = skin_concerns[0]
        else:
            formatted_concerns = "various skin issues"

        summaries = [
            "Based on the analysis and survey results, we found that [skin_concerns] are your main concerns. This is our recommended treatments.",
            "Our analysis and survey results indicate that [skin_concerns] should be prioritized. These are our recommended treatments.",
            "From the skin analysis and survey results, we identified [skin_concerns] as key focus areas. This is our recommended treatments for you.",
            "Your skin analysis and survey results highlight [skin_concerns] as concerns to address. These are our recommended treatments.",
            "Through careful analysis and your survey results, we’ve identified [skin_concerns] as top priorities. This is our recommended treatments.",
            "Combining the skin analysis and survey results, we recognized [skin_concerns] as needing attention. These are our recommended treatments.",
            "The results from your skin analysis and survey show [skin_concerns] as important issues. This is our recommended treatments.",
            "After reviewing your analysis and survey results, we concluded that [skin_concerns] need to be treated. These are our recommended treatments.",
            "According to the analysis and survey results, [skin_concerns] are the most significant issues. This is our recommended treatments.",
            "Insights from your analysis and survey results reveal [skin_concerns] as areas of concern. These are our recommended treatments.",
        ]

        summary_template = random.choice(summaries)
        final_summary = summary_template.replace("[skin_concerns]", formatted_concerns)

        return final_summary

    async def recommendation(self, dto: RecommendationInputDTO) -> List[TreatmentDTO]:
        sa_skin_concerns = dto.sa_skin_concerns
        survey_skin_concerns = dto.survey_skin_concerns
        skin_concerns = list(dict.fromkeys(sa_skin_concerns + survey_skin_concerns))

        filtered = await self._filter_treatment_phase(dto=dto)
        ranked = self._rank_treatments(
            treatments=filtered,
            sa_skin_concerns=dto.sa_skin_concerns,
            survey_skin_concerns=dto.survey_skin_concerns,
        )
        if not ranked:
            return []

        if ranked:
            ranked = self._interleave_treatments_by_concern(ranked, skin_concerns)

        results: List[TreatmentDTO] = []
        for treatment in ranked:
            solved_concerns = [
                v
                for k, v in treatment.items()
                if k.startswith("concern_") and isinstance(v, str)
            ]
            matched_concerns = [
                c for c in solved_concerns if c in [sc for sc in skin_concerns]
            ]
            results.append(
                TreatmentDTO(
                    id=treatment.get("id"),
                    item_code=treatment.get("item_code"),
                    name=treatment.get("name"),
                    media_url=treatment.get("media_url"),
                    thumbnail_url=treatment.get("thumbnail_url"),
                    description=treatment.get("description"),
                    categories=treatment.get("categories"),
                    quantity=treatment.get("quantity"),
                    price=treatment.get("price"),
                    is_top_recommendation=treatment.get("is_top_recommendation"),
                    solved_concerns=matched_concerns,
                    total_score=treatment.get("total_score"),
                )
            )
        return results
