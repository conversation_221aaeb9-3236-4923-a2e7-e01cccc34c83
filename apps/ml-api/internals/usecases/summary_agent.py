from textwrap import dedent
from typing import List
from agno.models.openai.chat import OpenAIChat
from agno.agent.agent import Agent


class SummaryAgent:
    def __init__(self, openai_api_key: str):
        self.openai_api_key = openai_api_key
        self._model_id = "gpt-4o-mini"

    def _create_Summary_agent(self) -> Agent:
        return Agent(
            name="summary_agent",
            model=OpenAIChat(
                id=self._model_id,
                api_key=self.openai_api_key,
                temperature=0.7,
            ),
            description="You are <PERSON><PERSON><PERSON>+, a professional skincare doctor with over 10 years of experience specializing in personalized skincare for Indonesian customers.",
            goal="Generate a summary of the user's skin concerns and recommend the most relevant treatments from a pre-filtered safe treatments list based on user skin concerns.",
            instructions=self._get_Summary_instructions(),
            show_tool_calls=True,
            stream=True,
        )

    def _get_Summary_instructions(self) -> str:
        return dedent("""
    You are <PERSON><PERSON><PERSON><PERSON>, a professional skincare doctor with over 10 years of experience specializing in personalized skincare for Indonesian customers.

    ## User Skin Analysis Data
    - **Top Skin Concerns**: {top_concern}
    - **User Concerns**: {user_concern}

    ## Response Structure:
    - Start by explaining the user's skin condition based on their top concerns.
    - then explain the user's skin condition based on their user concerns.
    - Explain how these concerns are related to the user's specific scores.
    - Provide a brief explanation of what these concerns mean for their skin health.
    - Keep the response under 560 characters and use plain text without any special characters, formatting, or line breaks.

    Example:
    Your skin concerns based on your skin analysis are pores, pigmentation, and wrinkles. and based on your survey, you have a concern about dehydration, millia and wartz. [then you explain the user's skin condition based on their top concerns and user concerns]
        """)

    def get_summary(self, top_concern: List[str], user_concern: List[str]) -> str:
        """Generate summary from safe treatments list based on user concerns."""

        agent = self._create_Summary_agent()

        message = f"""
        Top Skin Concerns: {top_concern}
        User Concerns: {user_concern}
        """

        response_content = ""

        for resp in agent.run(message, stream=True):
            if resp.content:
                response_content += resp.content

        return response_content
