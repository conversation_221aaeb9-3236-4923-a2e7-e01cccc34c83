import mediapipe as mp

from PIL import Image
import numpy as np
import cv2

from internals.dtos.face_aging import (
    AgingAreaList,
)


class FaceFilter:
    def __init__(self):
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True, max_num_faces=1, refine_landmarks=True
        )
        self.eye_idxs = (
            self.get_unique_points(self.mp_face_mesh.FACEMESH_LEFT_EYE)
            + self.get_unique_points(self.mp_face_mesh.FACEMESH_RIGHT_EYE)
            + self.get_unique_points(self.mp_face_mesh.FACEMESH_LEFT_EYEBROW)
            + self.get_unique_points(self.mp_face_mesh.FACEMESH_RIGHT_EYEBROW)
        )
        self.lips_idxs = self.get_unique_points(self.mp_face_mesh.FACEMESH_LIPS)

        self.libs_regions = [self.mp_face_mesh.FACEMESH_LIPS]
        self.libs_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))

        self.nose_regions = [self.mp_face_mesh.FACEMESH_NOSE]
        self.nose_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))

        self.eye_regions = [
            self.mp_face_mesh.FACEMESH_LEFT_EYE,
            self.mp_face_mesh.FACEMESH_RIGHT_EYE,
            self.mp_face_mesh.FACEMESH_LEFT_EYEBROW,
            self.mp_face_mesh.FACEMESH_RIGHT_EYEBROW,
        ]
        self.eyes_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (30, 30))

        self.mustache_area_region = [
            [
                # Start at left nose wing, go across bottom of nose to right wing
                326,
                164,
                11,
                97,
                98,
                # Go down the right cheek slightly and to the mouth corner
                61,
                327,
                # Trace the top of the upper lip
                185,
                40,
                39,
                37,
                0,
                267,
                269,
                270,
                409,
                # Go from the left mouth corner and up the left cheek slightly
                291,
            ]
        ]
        self.mustache_area_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))

    def resize_image(self, image_array, max_size=1024):
        # Convert numpy array to PIL Image
        image = Image.fromarray(image_array)
        width, height = image.size

        # Calculate new dimensions maintaining aspect ratio
        if width > max_size or height > max_size:
            if width > height:
                new_width = max_size
                new_height = int(height * (max_size / width))
            else:
                new_height = max_size
                new_width = int(width * (max_size / height))
            # Resize the image
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Convert back to numpy array
        return np.array(image)

    def get_unique_points(self, connections):
        indices = set()
        for conn in connections:
            indices.update(conn)
        return list(indices)

    def get_image_resize_write(self, original_image):
        return self.resize_image(image_array=original_image)

    def create_mask(self, image):
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(image_rgb)

        mask = np.zeros(image.shape, dtype=np.uint8)

        def region_mask(regions, mask, kernel):
            for region in regions:
                if isinstance(region, list):
                    region_indices = region
                elif isinstance(region, frozenset):
                    region_indices = self.get_unique_points(region)
                region_points = points[region_indices]
                if len(region_points) > 0:
                    region_hull = cv2.convexHull(region_points)
                    cv2.fillPoly(mask, [region_hull], 255)
            dilated = cv2.dilate(mask, kernel, iterations=1)
            return dilated

        if results.multi_face_landmarks:
            for face_landmarks in results.multi_face_landmarks:
                height, width, _ = image.shape
                points = []
                for landmark in face_landmarks.landmark:
                    x = int(landmark.x * width)
                    y = int(landmark.y * height)
                    points.append([x, y])
                points = np.array(points, dtype=np.int32)

                chin_index, left_brow_index, right_brow_index = 152, 336, 107
                chin_point, left_brow_point, right_brow_point = (
                    points[chin_index],
                    points[left_brow_index],
                    points[right_brow_index],
                )

                brow_midpoint_x = (left_brow_point[0] + right_brow_point[0]) // 2
                chin_to_brow_height = chin_point[1] - left_brow_point[1]

                forehead_height_ratio = 0.4
                forehead_width_ratio = 3.4
                num_virtual_points = 14

                forehead_height = int(chin_to_brow_height * forehead_height_ratio)
                forehead_width = int(
                    (left_brow_point[0] - right_brow_point[0]) * forehead_width_ratio
                )

                virtual_points = []
                for i in range(num_virtual_points):
                    angle = (np.pi * i) / (num_virtual_points - 1)

                    vx = brow_midpoint_x + int((forehead_width / 2) * np.cos(angle))
                    vy = left_brow_point[1] - int(forehead_height * np.sin(angle))

                    virtual_points.append([vx, vy])

                extended_points = np.concatenate((points, virtual_points))
                face_hull = cv2.convexHull(extended_points)
                cv2.fillPoly(mask, [face_hull], (255, 255, 255))

                libs_mask = np.zeros((height, width), dtype=np.uint8)
                dilated_libs_mask = region_mask(
                    self.libs_regions, libs_mask, self.libs_kernel
                )

                nose_mask = np.zeros((height, width), dtype=np.uint8)
                dilated_nose_mask = region_mask(
                    self.nose_regions, nose_mask, self.nose_kernel
                )

                # Prepare a separate mask for eyes (and eyebrows if desired)
                eye_mask = np.zeros((height, width), dtype=np.uint8)
                dilated_eye_mask = region_mask(
                    self.eye_regions, eye_mask, self.eyes_kernel
                )

                dilated_mustache_mask = region_mask(
                    self.mustache_area_region,
                    np.zeros((height, width), dtype=np.uint8),
                    self.mustache_area_kernel,
                )

                mask[dilated_eye_mask == 255] = (0, 0, 0)
                mask[dilated_nose_mask == 255] = (0, 0, 0)
                mask[dilated_libs_mask == 255] = (0, 0, 0)
                mask[dilated_mustache_mask == 255] = (0, 0, 0)
        return mask

    def select_face_area(self, areas: AgingAreaList, image: np.ndarray):
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(image_rgb)
        height, width, _ = image.shape

        if not results.multi_face_landmarks:
            return

        # get all face-landmark points
        for lm in results.multi_face_landmarks:
            pts_all = np.array(
                [[int(p.x * width), int(p.y * height)] for p in lm.landmark],
                dtype=np.int32,
            )

            # full-face hull for base mask
            chin_index, left_brow_index, right_brow_index = 152, 336, 107
            chin_point, left_brow_point, right_brow_point = (
                pts_all[chin_index],
                pts_all[left_brow_index],
                pts_all[right_brow_index],
            )

            brow_midpoint_x = (left_brow_point[0] + right_brow_point[0]) // 2
            chin_to_brow_height = chin_point[1] - left_brow_point[1]

            forehead_height_ratio = 0.4
            forehead_width_ratio = 3.4
            num_virtual_points = 14

            forehead_height = int(chin_to_brow_height * forehead_height_ratio)
            forehead_width = int(
                (left_brow_point[0] - right_brow_point[0]) * forehead_width_ratio
            )

            virtual_points = []
            for i in range(num_virtual_points):
                angle = (np.pi * i) / (num_virtual_points - 1)

                vx = brow_midpoint_x + int((forehead_width / 2) * np.cos(angle))
                vy = left_brow_point[1] - int(forehead_height * np.sin(angle))

                virtual_points.append([vx, vy])

            extended_points = np.concatenate((pts_all, virtual_points))
            face_hull = cv2.convexHull(extended_points)
            full_mask = np.zeros((height, width), dtype=np.uint8)
            _ = cv2.fillPoly(full_mask, [face_hull], (255, 255, 255))

            # compute cutoffs
            eye_pts = pts_all[self.eye_idxs]
            lips_pts = pts_all[self.lips_idxs]
            cutoff_eyes = eye_pts[:, 1].max()
            cutoff_lips = lips_pts[:, 1].min()

            # build each region mask
            masks = {}
            # Upper: everything above eyes
            m = full_mask.copy()
            m[cutoff_eyes:, :] = 0
            masks["upper"] = m
            # Mid: between eyes and lips
            m = full_mask.copy()
            m[:cutoff_eyes, :] = 0
            m[cutoff_lips:, :] = 0
            masks["mid"] = m
            # Lower: everything below lips
            m = full_mask.copy()
            m[:cutoff_lips, :] = 0
            masks["lower"] = m

            # combine requested areas
            combined = np.zeros_like(full_mask)
            for a in areas:
                if a in masks:
                    combined = cv2.bitwise_or(combined, masks[a])

            rgba = cv2.cvtColor(image, cv2.COLOR_BGR2BGRA)

            rgba[:, :, 3] = combined

        return rgba
