"""
Module for Recommendation Usecases
"""

from internals.usecases.treatment_recommendation_engine import (
    TreatmentRecommendationEngine,
)
from internals.dtos.recommendation import (
    RecommendationInputDTO,
    RecommendationOutputDTO,
    RecommendationResponseDTO,
    RecommendationDataDTO,
)


class RecommendationUsecase:
    async def generate_recommendation(
        self, dto: RecommendationInputDTO
    ) -> RecommendationOutputDTO:
        """Generate treatment recommendations based on skin analysis."""

        recommendation_engine = TreatmentRecommendationEngine()
        treatments = await recommendation_engine.recommendation(dto=dto)
        summary = None
        if len(treatments) > 0:
            summary = await recommendation_engine.generate_summary(dto=dto)

        return RecommendationOutputDTO(
            data=RecommendationResponseDTO(
                status="success",
                data=RecommendationDataDTO(summary=summary, treatments=treatments),
                error=None,
            )
        )
