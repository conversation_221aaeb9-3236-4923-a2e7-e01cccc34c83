import asyncio
import os
import csv
from typing import List
from langchain_core.documents import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from internals.usecases.recommendation_knowledge import RecommendationKnowledge

from internals.usecases.util import load_assets


async def inject_csv(filename: str, searchable_fields: List[str]):
    """Enhanced version that creates field-specific documents for better searchability"""
    knowledge = RecommendationKnowledge()
    splitter = RecursiveCharacterTextSplitter(chunk_size=3000, chunk_overlap=50)

    # Use the assets directory path instead of current working directory
    assets_path = load_assets()
    if not assets_path:
        raise Exception("Could not load assets directory path")

    file_path = os.path.join(assets_path, filename)

    try:
        print(f"[INFO] Processing CSV with field awareness: {filename}")
        documents = []

        with open(file_path, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames

            schema_doc = Document(
                page_content=f"CSV Schema for {filename}: Contains fields {', '.join(headers)}. Use this file to search for: {', '.join(headers)}",
                metadata={
                    "filename": filename,
                    "type": "schema",
                    "headers": headers,
                },
            )
            documents.append(schema_doc)

            for idx, row in enumerate(reader):
                main_content = f"Record {idx + 1} from {filename}:\n"
                main_content += "\n".join(
                    f"{k}: {v}" for k, v in row.items() if v and str(v).strip()
                )

                documents.append(
                    Document(
                        page_content=main_content,
                        metadata={
                            "filename": filename,
                            "type": "record",
                            "row": idx,
                            **{k: v for k, v in row.items() if v and str(v).strip()},
                        },
                    )
                )

                for field in headers:
                    field_lower = field.lower()
                    if any(
                        search_term in field_lower for search_term in searchable_fields
                    ):
                        value = row.get(field, "").strip()
                        if value:
                            field_content = f"Field '{field}' in {filename}: {value}\nContext: Record {idx + 1} - {main_content[:200]}..."
                            documents.append(
                                Document(
                                    page_content=field_content,
                                    metadata={
                                        "filename": filename,
                                        "type": "field_index",
                                        "field": field,
                                        "value": value,
                                        "row": idx,
                                    },
                                )
                            )

        docs_splitted = splitter.split_documents(documents)

        print(f"[INFO] Created {len(docs_splitted)} searchable chunks from {filename}")

        ids = []
        for i, doc in enumerate(docs_splitted):
            doc_type = doc.metadata.get("type", "unknown")
            if doc_type == "schema":
                ids.append(f"{filename}_schema_{i}")
            elif doc_type == "field_index":
                field = doc.metadata.get("field", "unknown")
                row = doc.metadata.get("row", 0)
                ids.append(f"{filename}_field_{field}_{row}_{i}")
            else:
                row = doc.metadata.get("row", 0)
                ids.append(f"{filename}_row_{row}_{i}")

        await knowledge.parent_retriever.aadd_documents(
            documents=docs_splitted,
            ids=ids,
        )

        print(f"[OK] Enhanced indexing complete for {filename}")

    except Exception as e:
        print(f"[ERR] Failed to process {filename}: {e}")


if __name__ == "__main__":
    asyncio.run(
        inject_csv(
            "treatments_v2.csv",
            [
                "TREATMENT LIST",
                "DESCRIPTION",
                "CATEGORIES",
                "CONCERN 1",
                "CONCERN 2",
                "CONCERN 3",
                "CONCERN 4",
                "CONCERN 5",
                "CONCERN 6",
                "IS TOP RECOMMENDATION",
                "INTERVAL",
                "Client sedang Hamil/Sedang pengobatan kanker)",
                "Client memiliki alergi Salmon",
                "Client memiliki alergi Kacang-kacangan",
                "Client sedang mengonsumsi Obat Hormonal",
                "Client sedang mengonsumsi Obat Anti Biotik",
                "Client sedang mengonsumsi Obat Isotretinoin?",
                "Client sedang Menyusui",
                "Client sedang mengonsumsi Obat Pengencer Darah (<3 bulan)",
                "Client memiliki riwayat penyakit autoimun",
                "Client memiliki riwayat Penyakit Diabetes tidak terkontrol",
                "Client memiliki riwayat Penyakt Kolesterol Tinggi",
                "Client memiliki riwayat Penyakit Pernah Kanker",
                "PRICE",
                "QUANTITY",
            ],
        )
    )
