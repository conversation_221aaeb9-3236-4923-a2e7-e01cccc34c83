from configs.environment import Environment
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import PGVector
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.retrievers import ParentDocumentRetriever
from langchain.storage import InMemoryStore
from agno.knowledge.langchain import LangChainKnowledgeBase


class RecommendationKnowledge:
    def __init__(self):
        self.embeddings = OpenAIEmbeddings(
            model="text-embedding-3-small", api_key=Environment.get_openai_api_key()
        )
        self.vector_store = PGVector(
            embedding_function=self.embeddings,
            collection_name="vector_store_skjs",
            connection_string=Environment.get_database_url(),
            use_jsonb=True,
        )
        self.memory_store = InMemoryStore()
        self.parent_retriever = ParentDocumentRetriever(
            vectorstore=self.vector_store,
            docstore=self.memory_store,
            child_splitter=RecursiveCharacterTextSplitter(
                chunk_size=300, chunk_overlap=150
            ),
        )
        self.knowledge_base = LangChainKnowledgeBase(
            retriever=self.vector_store.as_retriever(search_kwargs={"k": 100}),
            embeddings=self.embeddings,
        )
