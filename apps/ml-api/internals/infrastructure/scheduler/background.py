import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI
import logging
from internals.infrastructure.scheduler.treatment_scheduler import TreatmentScheduler

logger = logging.getLogger()
logger.setLevel("INFO")


async def background_scheduler():
    scheduler = TreatmentScheduler()
    while True:
        try:
            await scheduler.run()
            logger.info("[SCHEDULER] Done. Waiting for next run...")
            print("[SCHEDULER] Done. Waiting for next run...")
        except Exception as e:
            logger.error(f"[SCHEDULER] Error: {e}")
        await asyncio.sleep(60 * 60 * 12)  # 12 hours


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f"[LIFESPAN] Starting app: {app.title}")
    task = asyncio.create_task(background_scheduler())
    yield
    _ = task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        logger.info("[SCHEDULER] Task cancelled.")
