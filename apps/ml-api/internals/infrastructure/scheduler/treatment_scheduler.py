import httpx
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from configs.environment import Environment
from internals.usecases.recommendation_knowledge import RecommendationKnowledge
import logging

logger = logging.getLogger()
logger.setLevel("INFO")


class TreatmentScheduler:
    def __init__(self):
        self.knowledge = RecommendationKnowledge()
        self.splitter = RecursiveCharacterTextSplitter(
            chunk_size=300, chunk_overlap=150
        )

    async def fetch_data(self, page: int = 1, page_size: int = 10):
        url = f"{Environment.get_api_url()}/api/v1/treatment-product?page={page}&page_size={page_size}"
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            _ = response.raise_for_status()
            return response.json().get("data", {})

    async def fetch_all_data(self, page_size: int = 10):
        all_data = []
        current_page = 1

        while True:
            response = await self.fetch_data(page=current_page, page_size=page_size)
            content = response.get("content", [])
            all_data.extend(content)

            total_pages = response.get("total_pages", 1)
            if current_page >= total_pages:
                break
            current_page += 1

        return all_data

    async def run(self):
        try:
            logger.info("[INFO] Fetching treatment data...")
            records = await self.fetch_all_data(page_size=10)
            logger.info(f"[INFO] Retrieved {len(records)} treatment records.")

            _ = self.knowledge.vector_store.delete_collection()
            logger.info("[INFO] Existing vector store cleared.")
            self.knowledge.vector_store.create_collection()
            logger.info("[INFO] Vector store created.")

            documents = []
            for row in records:
                if row.get("type") != "treatment":
                    continue

                item_code = row.get("item_code")
                name = row.get("name")
                description = row.get("description", "")
                category = row.get("category", [])
                category_name = category[0].get("name") if category else None
                quantity = row.get("quantity")
                price = row.get("price")
                is_top = row.get("is_top_recommendation")
                interval = row.get("interval")
                interval_desc = f"{interval.get('days')} days" if interval else None

                concern_list = row.get("concern", [])
                concerns = [
                    f"Concern {i + 1}: {c.get('name')}"
                    for i, c in enumerate(concern_list[:6])
                ]

                surveys = row.get("survey_questions", [])
                survey_texts = []
                for survey in surveys:
                    q = (
                        survey.get("question", "")
                        .replace("Apakah Anda", "Client")
                        .replace("?", "")
                    )
                    permission = (
                        "Tidak Boleh" if survey.get("selected_answer") == 1 else "Boleh"
                    )
                    survey_texts.append(f"{q}: {permission}")

                full_text = "\n".join(
                    [
                        f"Item Code: {item_code}",
                        f"Name: {name}",
                        f"Description: {description}",
                        f"Category: {category_name}",
                        f"Quantity: {quantity}",
                        f"Price: {price}",
                        f"Top Recommendation: {is_top}",
                        f"Interval: {interval_desc}",
                        *concerns,
                        "Client sedang Hamil/Sedang pengobatan kanker: Tidak Boleh",
                        *survey_texts,
                    ]
                )

                documents.append(
                    Document(
                        page_content=full_text,
                        metadata={
                            "item_code": item_code,
                            "name": name,
                            "category": category_name,
                        },
                    )
                )

            if not documents:
                logger.warning("[WARN] No treatment documents found to index.")
                return

            docs_splitted = self.splitter.split_documents(documents)
            logger.info(f"[OK] Splitted into {len(docs_splitted)} chunks.")
            await self.knowledge.parent_retriever.aadd_documents(docs_splitted)
            logger.info("[OK] Indexed successfully.")
        except Exception as e:
            logger.error(f"[ERR] Failed during scheduling: {e}")
