import time
from uuid import UUI<PERSON>, uuid4
from pydantic import BaseModel, Field
from typing import Type<PERSON><PERSON><PERSON>, Literal, List, Optional

from .survey import SurveyAnswer
from .treatment_category import TreatmentCategory
from .skin_problem import SkinProblemIndication
from .treatment_interval import TreatmentInterval


TreatmentProductType: TypeAlias = Literal["treatment", "product"]
TreatmentProductRemark: TypeAlias = Literal["serum", "blood", "neither"]


class TreatmentProductSurveyQuestion(BaseModel):
    id: UUID
    question: str
    answers: List[SurveyAnswer]
    selected_answer: int
    question_order: int


class TreatmentProductGetManyConcern(BaseModel):
    id: Optional[UUID] = Field(default_factory=uuid4)
    name: str
    concern_indications: Optional[List[SkinProblemIndication]] = None
    created_at: int = Field(default_factory=lambda: int(time.time() * 1000))
    updated_at: int = Field(default_factory=lambda: int(time.time() * 1000))


class TreatmentProduct(BaseModel):
    id: Optional[UUID] = Field(default_factory=uuid4)
    item_code: Optional[str] = None
    name: Optional[str] = None
    type: Optional[TreatmentProductType] = None
    description: Optional[str] = None
    interval_id: Optional[str] = None
    price: Optional[int] = None
    media_url: Optional[str] = None
    notes: Optional[str] = None
    quantity: Optional[int] = Field(default=1)
    is_top_recommendation: Optional[bool] = False
    duration_top_recommendation: Optional[int] = None
    survey_questions: Optional[List[TreatmentProductSurveyQuestion]] = None
    created_at: Optional[int] = Field(default_factory=lambda: int(time.time() * 1000))
    updated_at: Optional[int] = Field(default_factory=lambda: int(time.time() * 1000))
    category: Optional[List[TreatmentCategory]] = None
    interval: Optional[TreatmentInterval] = None
    concern: Optional[List[TreatmentProductGetManyConcern]] = None
