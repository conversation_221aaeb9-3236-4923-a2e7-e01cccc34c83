"""
Module for User Survey entity
"""

import time
from uuid import UUID, uuid4
from pydantic import BaseModel, <PERSON>
from typing import List, Optional


class UserSurveyResult(BaseModel):
    question: str
    answers: List[str]
    category: Optional[str] = None


class UserSurvey(BaseModel):
    id: Optional[UUID] = Field(default_factory=uuid4)
    user_id: Optional[UUID] = None
    skin_analyze_id: Optional[UUID] = None
    results: Optional[List[UserSurveyResult]] = None
    created_at: Optional[int] = Field(default_factory=lambda: int(time.time() * 1000))
    updated_at: Optional[int] = Field(default_factory=lambda: int(time.time() * 1000))
