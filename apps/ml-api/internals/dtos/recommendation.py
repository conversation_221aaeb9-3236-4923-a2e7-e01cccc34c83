"""
Module for Recommendation DTOs
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any


class TreatmentIntervalDTO(BaseModel):
    name: str
    days: int


class SurveyContraindicationDTO(BaseModel):
    question: str
    answers: List[str]


class RecommendationInputDTO(BaseModel):
    sa_skin_concerns: List[str]
    survey_skin_concerns: List[str]
    treatment_intervals: List[TreatmentIntervalDTO]
    survey_contraindications: List[SurveyContraindicationDTO]


class TreatmentDTO(BaseModel):
    id: str
    item_code: str
    name: str
    description: str
    media_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    categories: List[str]
    quantity: int
    price: int
    is_top_recommendation: bool
    solved_concerns: List[str]
    total_score: float


class RecommendationDataDTO(BaseModel):
    summary: Optional[str] = None
    treatments: Optional[List[TreatmentDTO]] = None


class RecommendationResponseDTO(BaseModel):
    status: Optional[str] = None
    data: Optional[RecommendationDataDTO] = None
    error: Optional[Dict[str, Any]] = None


class RecommendationOutputDTO(BaseModel):
    data: RecommendationResponseDTO
