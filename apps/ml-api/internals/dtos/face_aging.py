"""
Module for Upload Face Aging DTOs
"""

from uuid import UUID
from pydantic import BaseModel, Field
from typing import Literal, List


AgingConcern = Literal[
    "pores",
    "acne",
    "scar",
    "pigment",
    "wrinkles",
    "redness",
    "beautify",
]


AgingAreas = Literal["upper", "mid", "lower"]
AgingAreaList = List[AgingAreas]


class UploadFaceAgingInputDTO(BaseModel):
    analyze_id: UUID = Field(..., description="Depends on Skin Analyze")


class UploadFaceAgingOutputDTO(BaseModel):
    base64_image: str


class FaceAgingConcernDetail(BaseModel):
    concern: AgingConcern
    areas: AgingAreaList


class FaceAgingConcernInputDTO(BaseModel):
    image_path: str
    mask_path: str | None
    concerns: List[FaceAgingConcernDetail]


class FaceAgingConcernOutputGenerated(BaseModel):
    concern: AgingConcern
    generated_image_url: str
    selected_area_url: str


class FaceAgingConcernCallbackDTO(BaseModel):
    image_url: List[str]
    generated_image: List[FaceAgingConcernOutputGenerated]


class FaceAgingConcernOutputDTO(BaseModel):
    message: str
    status: int
