#!/usr/bin/env python3
"""
Test script to check database connection and knowledge base status
"""

import psycopg2
import sys
from psycopg2 import sql
import traceback

# Database configuration from knowledge.py
DATABASE_URL = "postgresql://postgres:securedb@localhost:5432/euromedicadev"

def test_basic_connection():
    """Test basic PostgreSQL connection"""
    print("🔍 Testing basic PostgreSQL connection...")
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ PostgreSQL connection successful!")
        print(f"   Database version: {version[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_database_exists():
    """Test if the target database exists"""
    print("\n🔍 Testing if database 'euromedicadev' exists...")
    try:
        # Connect to postgres database first
        base_url = "postgresql://postgres:securedb@localhost:5432/postgres"
        conn = psycopg2.connect(base_url)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'euromedicadev';")
        exists = cursor.fetchone()
        
        if exists:
            print("✅ Database 'euromedicadev' exists!")
        else:
            print("❌ Database 'euromedicadev' does not exist!")
            print("   You may need to create it first.")
        
        cursor.close()
        conn.close()
        return bool(exists)
        
    except Exception as e:
        print(f"❌ Error checking database existence: {e}")
        return False

def test_pgvector_extension():
    """Test if pgvector extension is installed"""
    print("\n🔍 Testing pgvector extension...")
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Check if pgvector extension exists
        cursor.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector';")
        exists = cursor.fetchone()
        
        if exists:
            print("✅ pgvector extension is installed!")
        else:
            print("❌ pgvector extension is not installed!")
            print("   You may need to install it with: CREATE EXTENSION vector;")
        
        cursor.close()
        conn.close()
        return bool(exists)
        
    except Exception as e:
        print(f"❌ Error checking pgvector extension: {e}")
        return False

def test_vector_store_table():
    """Test if vector store table exists"""
    print("\n🔍 Testing vector store table...")
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Check if the langchain vector store table exists
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%vector%' OR table_name LIKE '%langchain%';
        """)
        tables = cursor.fetchall()
        
        if tables:
            print("✅ Vector store tables found:")
            for table in tables:
                print(f"   - {table[0]}")
                
                # Check row count
                cursor.execute(f"SELECT COUNT(*) FROM {table[0]};")
                count = cursor.fetchone()[0]
                print(f"     Rows: {count}")
        else:
            print("❌ No vector store tables found!")
            print("   The knowledge base may not be initialized.")
        
        cursor.close()
        conn.close()
        return len(tables) > 0
        
    except Exception as e:
        print(f"❌ Error checking vector store tables: {e}")
        return False

def test_knowledge_base_initialization():
    """Test if we can initialize the knowledge base"""
    print("\n🔍 Testing knowledge base initialization...")
    try:
        from knowledge import KnowledgeRepo
        
        knowledge = KnowledgeRepo()
        print("✅ Knowledge base initialized successfully!")
        
        # Try to perform a simple search
        try:
            results = knowledge.knowledge_base.search("treatment")
            print(f"✅ Knowledge base search works! Found {len(results)} results")
            if results:
                print(f"   Sample result: {results[0][:100]}...")
        except Exception as e:
            print(f"⚠️  Knowledge base search failed: {e}")
            print("   This might indicate empty knowledge base.")
        
        return True
        
    except Exception as e:
        print(f"❌ Knowledge base initialization failed: {e}")
        print(f"   Full error: {traceback.format_exc()}")
        return False

def main():
    """Run all database tests"""
    print("🧪 Database Connection and Knowledge Base Test")
    print("=" * 50)
    
    tests = [
        test_basic_connection,
        test_database_exists,
        test_pgvector_extension,
        test_vector_store_table,
        test_knowledge_base_initialization
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    test_names = [
        "Basic PostgreSQL Connection",
        "Database Exists",
        "pgvector Extension",
        "Vector Store Tables",
        "Knowledge Base Initialization"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {i+1}. {name}: {status}")
    
    passed = sum(results)
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Knowledge base should work correctly.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")
        print("\n💡 Common solutions:")
        print("   - Start PostgreSQL: brew services start postgresql")
        print("   - Create database: createdb euromedicadev")
        print("   - Install pgvector: CREATE EXTENSION vector;")
        print("   - Load treatment data into vector store")

if __name__ == "__main__":
    main()
