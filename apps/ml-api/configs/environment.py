import os
from dotenv import load_dotenv
import platform


_ = load_dotenv()


class Environment:
    @staticmethod
    def get_database_url() -> str:
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            raise ValueError("DATABASE_URL environment variable not set.")
        return database_url

    @staticmethod
    def get_genai_api_key() -> str:
        genai_api_key = os.getenv("GENAI_API_KEY")
        if not genai_api_key:
            raise ValueError("GENAI_API_KEY environment variable not set.")
        return genai_api_key

    @staticmethod
    def get_openai_api_key() -> str:
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set.")
        return openai_api_key

    @staticmethod
    def get_tesseract_path() -> str:
        path = r"/usr/bin/tesseract"
        if platform.system() == "windows":
            path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
        elif platform.system() == "Darwin":
            path = r"/usr/local/bin/tesseract"

        tesseract_path = os.getenv("TESSERACT_PATH", path)
        if not tesseract_path:
            raise ValueError("TESSERACT_PATH environment variable not set.")
        return tesseract_path

    @staticmethod
    def get_python_path() -> str:
        python_path = os.getenv("PYTHONPATH")
        if not python_path:
            raise ValueError("PYTHONPATH environment variable not set.")
        return python_path

    @staticmethod
    def get_default_password() -> str:
        default_password = os.getenv("DEFAULT_PASSWORD")
        if not default_password:
            raise ValueError("DEFAULT_PASSWORD environment variable not set.")
        return default_password

    @staticmethod
    def get_admin_password() -> str:
        admin_password = os.getenv("ADMIN_PASSWORD")
        if not admin_password:
            raise ValueError("ADMIN_PASSWORD environment variable not set.")
        return admin_password

    @staticmethod
    def get_super_admin_password() -> str:
        super_admin_password = os.getenv("SUPER_ADMIN_PASSWORD")
        if not super_admin_password:
            raise ValueError("SUPER_ADMIN_PASSWORD environment variable not set.")
        return super_admin_password

    @staticmethod
    def get_secret_key() -> str:
        secret_key = os.getenv("SECRET_KEY")
        if not secret_key:
            raise ValueError("SECRET_KEY environment variable not set.")
        return secret_key

    @staticmethod
    def get_image_dir() -> str:
        image_dir = os.getenv("IMAGE_DIR", "uploads/images/")
        if not image_dir:
            raise ValueError("IMAGE_DIR environment variable not set.")

        os.makedirs(image_dir, exist_ok=True)
        return image_dir

    @staticmethod
    def get_pdf_dir() -> str:
        pdf_dir = os.getenv("PDF_DIR", "uploads/reports/")
        if not pdf_dir:
            raise ValueError("PDF_DIR environment variable not set.")

        os.makedirs(pdf_dir, exist_ok=True)
        return pdf_dir

    @staticmethod
    def get_aws_region() -> str:
        aws_region = os.getenv("AWS_REGION")
        if not aws_region:
            raise ValueError("AWS_REGION environment variable not set.")
        return aws_region

    @staticmethod
    def get_aws_access_key_id() -> str:
        aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        if not aws_access_key_id:
            raise ValueError("AWS_ACCESS_KEY_ID environment variable not set.")
        return aws_access_key_id

    @staticmethod
    def get_aws_secret_access_key() -> str:
        aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        if not aws_secret_access_key:
            raise ValueError("AWS_SECRET_ACCESS_KEY environment variable not set.")
        return aws_secret_access_key

    @staticmethod
    def get_aws_s3_bucket() -> str:
        aws_s3_bucket = os.getenv("AWS_S3_BUCKET")
        if not aws_s3_bucket:
            raise ValueError("AWS_S3_BUCKET environment variable not set.")
        return aws_s3_bucket

    @staticmethod
    def get_heygen_api_key() -> str:
        heygen_api_key = os.getenv("HEYGEN_API_KEY")
        if not heygen_api_key:
            raise ValueError("HEYGEN_API_KEY environment variable not set.")
        return heygen_api_key

    @staticmethod
    def get_avatar_id() -> str:
        avatar_id = os.getenv("HEYGEN_AVATAR_ID")
        if not avatar_id:
            raise ValueError("HEYGEN_AVATAR_ID environment variable not set.")
        return avatar_id

    @staticmethod
    def get_voice_id() -> str:
        voice_id = os.getenv("HEYGEN_VOICE_ID")
        if not voice_id:
            raise ValueError("HEYGEN_VOICE_ID environment variable not set.")
        return voice_id

    @staticmethod
    def get_api_url() -> str:
        api_url = os.getenv("API_URL")
        if not api_url:
            raise ValueError("API_URL environment variable not set.")
        return api_url
