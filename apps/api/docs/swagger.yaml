basePath: /
definitions:
  domain.CreateMachineSyncLog:
    properties:
      data:
        type: object
    type: object
  domain.Empty:
    type: object
  domain.FaceAgingArea:
    enum:
    - upper
    - mid
    - lower
    type: string
    x-enum-varnames:
    - FaceAgingAreaUpper
    - FaceAgingAreaMid
    - FaceAgingAreaLower
  domain.FaceAgingConcern:
    enum:
    - pores
    - acne
    - scar
    - pigment
    - wrinkles
    - redness
    - beautify
    type: string
    x-enum-varnames:
    - FaceAgingConcernPore
    - FaceAgingConcernAcne
    - FaceAgingConcernScar
    - FaceAgingConcernPigmentation
    - FaceAgingConcernWrinkle
    - FaceAgingConcernRedness
    - FaceAgingConcernBeautify
  domain.FaceAgingConcernDetailMLRequest:
    properties:
      areas:
        items:
          $ref: '#/definitions/domain.FaceAgingArea'
        type: array
      concern:
        $ref: '#/definitions/domain.FaceAgingConcern'
    type: object
  domain.FaceAgingConcernMLRequest:
    properties:
      concerns:
        items:
          $ref: '#/definitions/domain.FaceAgingConcernDetailMLRequest'
        type: array
      image_path:
        type: string
      mask_path:
        type: string
    type: object
  domain.FaceAgingConcernMLResponse:
    properties:
      generated_image:
        items:
          $ref: '#/definitions/domain.FaceAgingGeneratedMLResponse'
        type: array
      image_url:
        items:
          type: string
        type: array
    type: object
  domain.FaceAgingConcernRequest:
    properties:
      concerns:
        items:
          $ref: '#/definitions/domain.FaceAgingConcernDetailMLRequest'
        type: array
      is_beautify:
        type: boolean
    type: object
  domain.FaceAgingConcernResponse:
    properties:
      generated_images:
        items:
          $ref: '#/definitions/domain.FaceAgingGeneratedMLResponse'
        type: array
    type: object
  domain.FaceAgingGeneratedMLResponse:
    properties:
      concern:
        $ref: '#/definitions/domain.FaceAgingConcern'
      generated_image_url:
        type: string
      selected_area_url:
        type: string
    type: object
  domain.FaceAgingJobQueue:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      error_message:
        type: string
      id:
        type: string
      payload:
        $ref: '#/definitions/domain.FaceAgingConcernMLRequest'
      response:
        $ref: '#/definitions/domain.FaceAgingConcernResponse'
      retry_count:
        type: integer
      skin_analyze_id:
        type: string
      status:
        $ref: '#/definitions/domain.JobQueueStatus'
      updated_at:
        type: integer
    type: object
  domain.Gender:
    enum:
    - male
    - female
    type: string
    x-enum-varnames:
    - Male
    - Female
  domain.JobQueueStatus:
    enum:
    - queued
    - processing
    - completed
    - failed
    type: string
    x-enum-varnames:
    - JobQueueStatusQueued
    - JobQueueStatusProcessing
    - JobQueueStatusCompleted
    - JobQueueStatusFailed
  domain.LoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        minLength: 8
        type: string
    required:
    - email
    - password
    type: object
  domain.LoginResponse:
    properties:
      refresh_token:
        type: string
      token:
        type: string
    type: object
  domain.MachineSyncLog:
    properties:
      created_at:
        type: integer
      data:
        type: object
      id:
        type: string
    type: object
  domain.MachineSyncLogStatCount:
    properties:
      error_count:
        type: integer
      success_count:
        type: integer
    type: object
  domain.MediaS3RequestPresignUrl:
    properties:
      filename:
        example: file.jpg
        type: string
    required:
    - filename
    type: object
  domain.MediaS3Response:
    properties:
      object_key:
        type: string
      url:
        type: string
    type: object
  domain.PaginationData-domain_Empty:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.Empty'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_ParameterSkinEvaluation:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.ParameterSkinEvaluation'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_SkinAnalyze:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.SkinAnalyze'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_SkinProblemIndication:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.SkinProblemIndication'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_SkinProblemResponse:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.SkinProblemResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_SurveyResponse:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.SurveyResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_SurveyResponseNested:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.SurveyResponseNested'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_TreatmentCategoryResponse:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.TreatmentCategoryResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_TreatmentIntervalResponse:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.TreatmentIntervalResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_TreatmentProductGetMany:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.TreatmentProductGetMany'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_UserResponse:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.UserResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationData-domain_UserSurvey:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.UserSurvey'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationResponse-domain_Empty:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_Empty'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_ParameterSkinEvaluation:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_ParameterSkinEvaluation'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_SkinAnalyze:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_SkinAnalyze'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_SkinProblemIndication:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_SkinProblemIndication'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_SkinProblemResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_SkinProblemResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_SurveyResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_SurveyResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_SurveyResponseNested:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_SurveyResponseNested'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_TreatmentCategoryResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_TreatmentCategoryResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_TreatmentIntervalResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_TreatmentIntervalResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_TreatmentProductGetMany:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_TreatmentProductGetMany'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_UserResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_UserResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationResponse-domain_UserSurvey:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationData-domain_UserSurvey'
      message:
        type: string
      status:
        type: string
    type: object
  domain.PaginationStatsData-domain_MachineSyncLog-domain_MachineSyncLogStatCount:
    properties:
      content:
        items:
          $ref: '#/definitions/domain.MachineSyncLog'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      stats:
        $ref: '#/definitions/domain.MachineSyncLogStatCount'
      total_data:
        type: integer
      total_pages:
        type: integer
    type: object
  domain.PaginationStatsResponse-domain_MachineSyncLog-domain_MachineSyncLogStatCount:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.PaginationStatsData-domain_MachineSyncLog-domain_MachineSyncLogStatCount'
      message:
        type: string
      status:
        type: string
    type: object
  domain.ParameterSkinEvaluation:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      id:
        type: string
      lower_point:
        type: integer
      name:
        type: string
      parameter_order:
        type: integer
      updated_at:
        type: integer
      updated_by:
        type: string
      upper_point:
        type: integer
    type: object
  domain.ParameterSkinEvaluationRequest:
    properties:
      lower_point:
        minimum: 0
        type: integer
      name:
        type: string
      upper_point:
        minimum: 0
        type: integer
    required:
    - name
    type: object
  domain.RecommendationResponse:
    properties:
      summary:
        type: string
      treatments:
        items:
          $ref: '#/definitions/domain.RecommendedTreatment'
        type: array
    type: object
  domain.RecommendationTreatmentMLResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/domain.Treatment'
        type: array
      text:
        type: string
      video:
        type: string
    type: object
  domain.RecommendedTreatment:
    properties:
      categories:
        items:
          type: string
        type: array
      description:
        type: string
      id:
        type: string
      is_top_recommendation:
        type: boolean
      item_code:
        type: string
      media_url:
        type: string
      name:
        type: string
      price:
        type: integer
      quantity:
        type: integer
      solved_concerns:
        items:
          type: string
        type: array
      thumbnail_url:
        type: string
      total_score:
        type: number
    type: object
  domain.RefreshRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  domain.SingleResponse-array_string:
    properties:
      code:
        type: integer
      data:
        items:
          type: string
        type: array
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_Empty:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.Empty'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_FaceAgingJobQueue:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.FaceAgingJobQueue'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_LoginResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.LoginResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_MachineSyncLog:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.MachineSyncLog'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_MediaS3Response:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.MediaS3Response'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_ParameterSkinEvaluation:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.ParameterSkinEvaluation'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_RecommendationResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.RecommendationResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_RecommendationTreatmentMLResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.RecommendationTreatmentMLResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SkinAnalyze:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SkinAnalyze'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SkinDetectionResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SkinDetectionResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SkinProblem:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SkinProblem'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SkinProblemIndication:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SkinProblemIndication'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SkinProblemResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SkinProblemResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SummaryResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SummaryResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SurveyResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SurveyResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_SurveyResponseNested:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.SurveyResponseNested'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_TreatmentCategoryResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.TreatmentCategoryResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_TreatmentIntervalResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.TreatmentIntervalResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_TreatmentProduct:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.TreatmentProduct'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_TreatmentProductResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.TreatmentProductResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_UserResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.UserResponse'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SingleResponse-domain_UserSurvey:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/domain.UserSurvey'
      message:
        type: string
      status:
        type: string
    type: object
  domain.SkinAnalyze:
    properties:
      actual_age:
        type: integer
      brown_area:
        type: integer
      created_at:
        type: integer
      created_by:
        type: string
      evaluation_rate:
        type: integer
      id:
        type: string
      input_date:
        type: string
      name:
        type: string
      operator_id:
        type: string
      path_images:
        items:
          type: string
        type: array
      path_pdf:
        type: string
      phone_number:
        type: string
      pl_texture:
        type: integer
      rgb_pore:
        type: integer
      rgb_spot:
        type: integer
      rgb_wrinkle:
        type: integer
      sensitive_area:
        type: integer
      skin_age:
        type: integer
      skin_condition:
        type: string
      suggestion:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
      uv_damage:
        type: integer
      uv_moisture:
        type: integer
      uv_pigmentation:
        type: integer
      uv_porphyrin:
        type: integer
    type: object
  domain.SkinAnalyzeUploadRequest:
    properties:
      images:
        items:
          type: string
        type: array
      operator_id:
        type: string
      pdf:
        type: string
    type: object
  domain.SkinDetectionResponse:
    properties:
      generated_image_url:
        type: string
    type: object
  domain.SkinProblem:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.SkinProblemIndication:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.SkinProblemIndicationRequest:
    properties:
      name:
        example: Brown Area
        type: string
    required:
    - name
    type: object
  domain.SkinProblemRequest:
    properties:
      name:
        example: Acne
        type: string
      skin_problem_indication_ids:
        items:
          type: string
        maxItems: 4
        type: array
        uniqueItems: true
    required:
    - name
    - skin_problem_indication_ids
    type: object
  domain.SkinProblemResponse:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      name:
        example: Acne
        type: string
      skin_problem_indications:
        items:
          $ref: '#/definitions/domain.SkinProblemIndication'
        type: array
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.SkinProblemType:
    enum:
    - special
    - general
    type: string
    x-enum-varnames:
    - SkinProblemSpecial
    - SkinProblemGeneral
  domain.SummaryResponse:
    properties:
      summary:
        type: string
    type: object
  domain.SurveyAnswer:
    properties:
      description:
        type: string
      image_url:
        type: string
      title:
        type: string
    type: object
  domain.SurveyCategory:
    enum:
    - contraindication
    - informational
    type: string
    x-enum-varnames:
    - Contraindication
    - Informational
  domain.SurveyRequest:
    properties:
      answers:
        items:
          $ref: '#/definitions/domain.SurveyAnswer'
        type: array
      category:
        allOf:
        - $ref: '#/definitions/domain.SurveyCategory'
        enum:
        - informational
        - contraindication
      description:
        type: string
      is_multiple:
        type: boolean
      parent_question_answer:
        minimum: 0
        type: integer
      parent_question_id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      question:
        type: string
      question_order:
        minimum: 0
        type: integer
      type:
        $ref: '#/definitions/domain.SurveyType'
    required:
    - answers
    - category
    - description
    - question
    - type
    type: object
  domain.SurveyRequestChildQuestion:
    properties:
      answers:
        items:
          $ref: '#/definitions/domain.SurveyAnswer'
        type: array
      category:
        allOf:
        - $ref: '#/definitions/domain.SurveyCategory'
        enum:
        - informational
        - contraindication
      description:
        type: string
      is_multiple:
        type: boolean
      parent_question_answer:
        minimum: 0
        type: integer
      question:
        type: string
      question_order:
        minimum: 0
        type: integer
      type:
        $ref: '#/definitions/domain.SurveyType'
    required:
    - answers
    - category
    - description
    - parent_question_answer
    - question
    - type
    type: object
  domain.SurveyRequestNested:
    properties:
      answers:
        items:
          $ref: '#/definitions/domain.SurveyAnswer'
        type: array
      category:
        allOf:
        - $ref: '#/definitions/domain.SurveyCategory'
        enum:
        - informational
        - contraindication
      child_questions:
        items:
          $ref: '#/definitions/domain.SurveyRequestChildQuestion'
        type: array
      description:
        type: string
      is_multiple:
        type: boolean
      question:
        type: string
      question_order:
        minimum: 0
        type: integer
      type:
        $ref: '#/definitions/domain.SurveyType'
    required:
    - answers
    - category
    - description
    - question
    - type
    type: object
  domain.SurveyResponse:
    properties:
      answers:
        items:
          $ref: '#/definitions/domain.SurveyAnswer'
        type: array
      category:
        $ref: '#/definitions/domain.SurveyCategory'
      created_at:
        type: integer
      created_by:
        type: string
      description:
        type: string
      id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      is_multiple:
        type: boolean
      is_static:
        type: boolean
      parent_question_answer:
        type: integer
      parent_question_id:
        type: string
      question:
        type: string
      question_order:
        type: integer
      type:
        $ref: '#/definitions/domain.SurveyType'
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.SurveyResponseNested:
    properties:
      answers:
        items:
          $ref: '#/definitions/domain.SurveyAnswer'
        type: array
      category:
        $ref: '#/definitions/domain.SurveyCategory'
      child_questions:
        items:
          $ref: '#/definitions/domain.SurveyResponse'
        type: array
      created_at:
        type: integer
      created_by:
        type: string
      description:
        type: string
      id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      is_multiple:
        type: boolean
      is_static:
        type: boolean
      parent_question_answer:
        type: integer
      parent_question_id:
        type: string
      question:
        type: string
      question_order:
        type: integer
      type:
        $ref: '#/definitions/domain.SurveyType'
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.SurveyType:
    enum:
    - single_full
    - multiple_full
    - horizontal_bar
    - text
    - dropdown
    type: string
    x-enum-varnames:
    - SingleFull
    - MultipleFull
    - HorizontalBar
    - Text
    - SurveyDropdown
  domain.Treatment:
    properties:
      concern:
        type: string
      concern_group:
        type: string
      created_at:
        type: integer
      created_by:
        type: string
      id:
        type: string
      name:
        type: string
      price:
        type: integer
      sku:
        type: string
      type:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentCategory:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentCategoryRequest:
    properties:
      name:
        example: Facial
        type: string
    required:
    - name
    type: object
  domain.TreatmentCategoryResponse:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      name:
        example: Facial
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentInterval:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      days:
        type: integer
      id:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentIntervalRequest:
    properties:
      days:
        example: 0
        minimum: 0
        type: integer
    required:
    - days
    type: object
  domain.TreatmentIntervalResponse:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      days:
        example: 0
        type: integer
      id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentProduct:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      description:
        type: string
      duration_top_recommendation:
        type: integer
      id:
        type: string
      interval_id:
        type: string
      is_top_recommendation:
        type: boolean
      item_code:
        type: string
      media_url:
        type: string
      name:
        type: string
      notes:
        type: string
      price:
        type: integer
      quantity:
        type: integer
      thumbnail_url:
        type: string
      type:
        $ref: '#/definitions/domain.TreatmentProductType'
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentProductGetMany:
    properties:
      category:
        items:
          $ref: '#/definitions/domain.TreatmentCategory'
        type: array
      concern:
        items:
          $ref: '#/definitions/domain.TreatmentProductGetManyConcern'
        type: array
      created_at:
        type: integer
      created_by:
        type: string
      description:
        type: string
      duration_top_recommendation:
        type: integer
      id:
        type: string
      interval:
        $ref: '#/definitions/domain.TreatmentInterval'
      interval_id:
        type: string
      is_top_recommendation:
        type: boolean
      item_code:
        type: string
      media_url:
        type: string
      name:
        type: string
      notes:
        type: string
      price:
        type: integer
      quantity:
        type: integer
      survey_questions:
        items:
          $ref: '#/definitions/domain.TreatmentProductSurveyQuestion'
        type: array
      thumbnail_url:
        type: string
      type:
        $ref: '#/definitions/domain.TreatmentProductType'
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentProductGetManyConcern:
    properties:
      concern_indications:
        items:
          $ref: '#/definitions/domain.SkinProblemIndication'
        type: array
      created_at:
        type: integer
      created_by:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentProductRequest:
    properties:
      category_ids:
        items:
          type: string
        maxItems: 3
        type: array
        uniqueItems: true
      concern_ids:
        items:
          type: string
        maxItems: 6
        type: array
        uniqueItems: true
      description:
        type: string
      duration_top_recommendation:
        example: 1747776517509
        type: integer
      interval_id:
        type: string
      is_top_recommendation:
        type: boolean
      item_code:
        example: SPDT69420
        type: string
      media_url:
        description: AWS S3 object key
        example: media/path/to/file
        type: string
      name:
        type: string
      notes:
        type: string
      price:
        minimum: 0
        type: integer
      quantity:
        minimum: 0
        type: integer
      survey_questions:
        items:
          $ref: '#/definitions/domain.TreatmentProductSurveyQuestionInput'
        type: array
      thumbnail_url:
        description: AWS S3 object key
        example: media/path/to/file
        type: string
      type:
        $ref: '#/definitions/domain.TreatmentProductType'
    required:
    - description
    - item_code
    - name
    - type
    type: object
  domain.TreatmentProductResponse:
    properties:
      category:
        items:
          $ref: '#/definitions/domain.TreatmentCategory'
        type: array
      concern:
        items:
          $ref: '#/definitions/domain.SkinProblem'
        type: array
      created_at:
        type: integer
      created_by:
        type: string
      description:
        type: string
      duration_top_recommendation:
        type: integer
      id:
        type: string
      interval:
        $ref: '#/definitions/domain.TreatmentInterval'
      is_top_recommendation:
        type: boolean
      item_code:
        type: string
      media_url:
        type: string
      name:
        type: string
      notes:
        type: string
      price:
        type: integer
      quantity:
        type: integer
      survey_questions:
        items:
          $ref: '#/definitions/domain.TreatmentProductSurveyQuestion'
        type: array
      thumbnail_url:
        type: string
      type:
        $ref: '#/definitions/domain.TreatmentProductType'
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.TreatmentProductSurveyQuestion:
    properties:
      answers:
        items:
          $ref: '#/definitions/domain.SurveyAnswer'
        type: array
      id:
        type: string
      question:
        type: string
      question_order:
        type: integer
      selected_answer:
        type: integer
    type: object
  domain.TreatmentProductSurveyQuestionInput:
    properties:
      selected_answer:
        type: integer
      survey_question_id:
        type: string
    type: object
  domain.TreatmentProductType:
    enum:
    - treatment
    - product
    type: string
    x-enum-varnames:
    - TreatmentType
    - ProductType
  domain.UpdatePasswordRequest:
    properties:
      password:
        example: new-password123
        minLength: 8
        type: string
    required:
    - password
    type: object
  domain.UserRequest:
    properties:
      address:
        example: 123 Main St
        type: string
      birth_date:
        example: 946684800000
        type: integer
      email:
        example: <EMAIL>
        type: string
      gender:
        allOf:
        - $ref: '#/definitions/domain.Gender'
        example: male
      name:
        example: John Doe
        type: string
      password:
        example: password123
        minLength: 8
        type: string
      phone_number:
        example: "081234567890"
        type: string
      role:
        allOf:
        - $ref: '#/definitions/domain.UserRole'
        example: admin
    required:
    - email
    - name
    - phone_number
    - role
    type: object
  domain.UserResponse:
    properties:
      address:
        example: 123 Main St
        type: string
      birth_date:
        example: 946684800000
        type: integer
      created_at:
        type: integer
      created_by:
        type: string
      email:
        example: <EMAIL>
        type: string
      gender:
        allOf:
        - $ref: '#/definitions/domain.Gender'
        example: male
      id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      name:
        example: John Doe
        type: string
      phone_number:
        example: "081234567890"
        type: string
      role:
        allOf:
        - $ref: '#/definitions/domain.UserRole'
        example: admin
      updated_at:
        type: integer
      updated_by:
        type: string
    type: object
  domain.UserRole:
    enum:
    - admin
    - branch
    - client
    type: string
    x-enum-varnames:
    - Admin
    - Branch
    - Client
  domain.UserSurvey:
    properties:
      created_at:
        type: integer
      created_by:
        type: string
      id:
        type: string
      results:
        items:
          $ref: '#/definitions/domain.UserSurveyResult'
        type: array
      skin_analyze_id:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
      user_id:
        type: string
    type: object
  domain.UserSurveyRequest:
    properties:
      results:
        items:
          $ref: '#/definitions/domain.UserSurveyResult'
        type: array
      skin_analyze_id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
      user_id:
        example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        type: string
    required:
    - results
    type: object
  domain.UserSurveyResult:
    properties:
      answers:
        items:
          type: string
        type: array
      category:
        $ref: '#/definitions/domain.SurveyCategory'
      id:
        type: string
      question:
        type: string
    type: object
host: localhost:8080
info:
  contact: {}
  description: This is the API documentation for the Aizer application.
  title: Aizer API
  version: "1.0"
paths:
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.LoginRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_LoginResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Login with user account
      tags:
      - auth
  /api/v1/auth/refresh:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.RefreshRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_LoginResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get token based on refresh token
      tags:
      - auth
  /api/v1/face-aging/callback/{id}:
    post:
      consumes:
      - application/json
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.FaceAgingConcernMLResponse'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_FaceAgingJobQueue'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Face aging with concern
      tags:
      - face-aging
  /api/v1/face-aging/concerns/{id}:
    post:
      consumes:
      - application/json
      parameters:
      - description: Skin analyze ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.FaceAgingConcernRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_FaceAgingJobQueue'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Face aging with concern
      tags:
      - face-aging
  /api/v1/face-aging/status/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Job id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_FaceAgingJobQueue'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Face aging with concern
      tags:
      - face-aging
  /api/v1/machine-sync-log:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationStatsResponse-domain_MachineSyncLog-domain_MachineSyncLogStatCount'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get many machine sync log
      tags:
      - machine-sync-log
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateMachineSyncLog'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_MachineSyncLog'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Create machine sync log
      tags:
      - machine-sync-log
  /api/v1/media/s3/upload/presign-url:
    post:
      consumes:
      - application/json
      parameters:
      - description: Info file
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.MediaS3RequestPresignUrl'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_MediaS3Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Upload media file to AWS S3
      tags:
      - media
  /api/v1/parameter-skin-evaluation:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_ParameterSkinEvaluation'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many parameter skin evaluation detail
      tags:
      - parameter-skin-evaluation
  /api/v1/parameter-skin-evaluation/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Parameter Skin Evaluation ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_ParameterSkinEvaluation'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get parameter skin evaluation detail by id
      tags:
      - parameter-skin-evaluation
    put:
      consumes:
      - application/json
      parameters:
      - description: Parameter Skin Evaluation ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.ParameterSkinEvaluationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_ParameterSkinEvaluation'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update parameter skin evaluation detail by id
      tags:
      - parameter-skin-evaluation
  /api/v1/recommendation-treatment/{id}:
    post:
      consumes:
      - application/json
      parameters:
      - description: Skin analyze ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_RecommendationTreatmentMLResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Recommendation treatment
      tags:
      - recommendation
  /api/v1/recommendation/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Analyze ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_RecommendationResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get recommendation by ID
      tags:
      - recommendation
  /api/v1/skin-analyze:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: name
        type: string
      - in: query
        name: operator_id
        type: string
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      - enum:
        - id
        - name
        - created_at
        - updated_at
        in: query
        name: sort_column
        type: string
      - enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_SkinAnalyze'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many skin analyzes
      tags:
      - skin-analyze
  /api/v1/skin-analyze/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Analyze ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinAnalyze'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get skin analyze by ID
      tags:
      - skin-analyze
  /api/v1/skin-analyze/top-concern/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Analyze ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-array_string'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get top concern skin analyzes
      tags:
      - skin-analyze
  /api/v1/skin-analyze/upload:
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SkinAnalyzeUploadRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinAnalyze'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Upload skin analyze image
      tags:
      - skin-analyze
  /api/v1/skin-detection/pores/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Analyze ID
        in: path
        name: id
        required: true
        type: string
      - enum:
        - pores
        - acne
        - scar
        - pigment
        - wrinkles
        - redness
        - beautify
        in: query
        name: concern
        required: true
        type: string
        x-enum-varnames:
        - FaceAgingConcernPore
        - FaceAgingConcernAcne
        - FaceAgingConcernScar
        - FaceAgingConcernPigmentation
        - FaceAgingConcernWrinkle
        - FaceAgingConcernRedness
        - FaceAgingConcernBeautify
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinDetectionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get pore lines
      tags:
      - skin-detection
  /api/v1/skin-detection/wrinkles/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Analyze ID
        in: path
        name: id
        required: true
        type: string
      - enum:
        - pores
        - acne
        - scar
        - pigment
        - wrinkles
        - redness
        - beautify
        in: query
        name: concern
        required: true
        type: string
        x-enum-varnames:
        - FaceAgingConcernPore
        - FaceAgingConcernAcne
        - FaceAgingConcernScar
        - FaceAgingConcernPigmentation
        - FaceAgingConcernWrinkle
        - FaceAgingConcernRedness
        - FaceAgingConcernBeautify
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinDetectionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get wrinkle lines
      tags:
      - skin-detection
  /api/v1/skin-problem:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: name
        type: string
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      - enum:
        - special
        - general
        in: query
        name: type
        type: string
        x-enum-varnames:
        - SkinProblemSpecial
        - SkinProblemGeneral
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_SkinProblemResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many skin problem detail
      tags:
      - skin-problem
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SkinProblemRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblemResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new skin problem (for treatment-product)
      tags:
      - skin-problem
  /api/v1/skin-problem-indication:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: name
        type: string
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_SkinProblemIndication'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many skin problem indication detail
      tags:
      - skin-problem-indication
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SkinProblemIndicationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblemIndication'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new skin problem indication
      tags:
      - skin-problem-indication
  /api/v1/skin-problem-indication/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Skin Problem Indication ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblemIndication'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Delete skin problem indication detail by id
      tags:
      - skin-problem-indication
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Problem Indication ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblemIndication'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get skin problem indication detail by id
      tags:
      - skin-problem-indication
    put:
      consumes:
      - application/json
      parameters:
      - description: Skin Problem Indication ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SkinProblemIndicationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblemIndication'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update skin problem indication detail by id
      tags:
      - skin-problem-indication
  /api/v1/skin-problem/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Skin Problem ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblem'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Delete skin problem detail by id
      tags:
      - skin-problem
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Problem ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblemResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get skin problem detail by id
      tags:
      - skin-problem
    put:
      consumes:
      - application/json
      parameters:
      - description: Skin Problem ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SkinProblemRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SkinProblemResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update skin problem detail by id
      tags:
      - skin-problem
  /api/v1/summary/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Skin Analyze ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SummaryResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get summary by ID
      tags:
      - summary
  /api/v1/survey:
    get:
      consumes:
      - application/json
      parameters:
      - collectionFormat: multi
        example:
        - contraindication
        in: query
        items:
          type: string
        name: category
        type: array
      - collectionFormat: multi
        example:
        - primary
        in: query
        items:
          type: string
        name: groups
        type: array
      - in: query
        name: is_static
        type: boolean
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      - in: query
        name: question
        type: string
      - enum:
        - id
        - question
        - category
        - group
        - mobile
        in: query
        name: sort_column
        type: string
      - enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - collectionFormat: multi
        example:
        - single_full
        in: query
        items:
          type: string
        name: type
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_SurveyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      summary: Get many survey detail
      tags:
      - survey
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SurveyRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SurveyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new survey
      tags:
      - survey
  /api/v1/survey/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Survey ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SurveyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Delete survey detail by id
      tags:
      - survey
    get:
      consumes:
      - application/json
      parameters:
      - description: Survey ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SurveyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get survey detail by id
      tags:
      - survey
    put:
      consumes:
      - application/json
      parameters:
      - description: Survey ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SurveyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SurveyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update survey detail by id
      tags:
      - survey
  /api/v1/survey/nested:
    get:
      consumes:
      - application/json
      parameters:
      - collectionFormat: multi
        in: query
        items:
          type: string
        name: category
        type: array
      - in: query
        name: is_static
        type: boolean
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      - in: query
        name: question
        type: string
      - enum:
        - id
        - question
        - category
        - mobile
        in: query
        name: sort_column
        type: string
      - enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - collectionFormat: multi
        in: query
        items:
          type: string
        name: type
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_SurveyResponseNested'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      summary: Get many survey parent detail with child questions
      tags:
      - survey
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SurveyRequestNested'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SurveyResponseNested'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new survey with child questions
      tags:
      - survey
  /api/v1/survey/nested/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: Survey ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SurveyResponseNested'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get survey detail by id with child questions
      tags:
      - survey
    put:
      consumes:
      - application/json
      parameters:
      - description: Survey ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.SurveyRequestNested'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_SurveyResponseNested'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update survey detail by id with child questions
      tags:
      - survey
  /api/v1/treatment-category:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_TreatmentCategoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many treatment category detail
      tags:
      - treatment-category
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.TreatmentCategoryRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new treatment category
      tags:
      - treatment-category
  /api/v1/treatment-category/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Treatment Category ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Delete treatment category detail by id
      tags:
      - treatment-category
    get:
      consumes:
      - application/json
      parameters:
      - description: Treatment Category ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get treatment category detail by id
      tags:
      - treatment-category
    put:
      consumes:
      - application/json
      parameters:
      - description: Treatment Category ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.TreatmentCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentCategoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update treatment category detail by id
      tags:
      - treatment-category
  /api/v1/treatment-interval:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_TreatmentIntervalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many treatment interval detail
      tags:
      - treatment-interval
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.TreatmentIntervalRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new treatment interval
      tags:
      - treatment-interval
  /api/v1/treatment-interval/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Treatment Interval ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Delete treatment interval detail by id
      tags:
      - treatment-interval
    get:
      consumes:
      - application/json
      parameters:
      - description: Treatment Interval ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get treatment interval detail by id
      tags:
      - treatment-interval
    put:
      consumes:
      - application/json
      parameters:
      - description: Treatment Interval ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.TreatmentIntervalRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentIntervalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update treatment interval detail by id
      tags:
      - treatment-interval
  /api/v1/treatment-product:
    get:
      consumes:
      - application/json
      parameters:
      - collectionFormat: multi
        in: query
        items:
          type: string
        name: category_ids
        type: array
      - in: query
        name: is_top_recommendation
        type: boolean
      - in: query
        minimum: 0
        name: max_price
        type: integer
      - in: query
        minimum: 0
        name: min_price
        type: integer
      - in: query
        name: name
        type: string
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      - enum:
        - id
        - name
        - type
        - price
        - created_at
        - updated_at
        - top_recommendation_name
        in: query
        name: sort_column
        type: string
      - enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - collectionFormat: multi
        example:
        - treatment
        in: query
        items:
          type: string
        name: types
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_TreatmentProductGetMany'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      summary: Get many treatment or product detail
      tags:
      - treatment-product
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.TreatmentProductRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentProductResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new treatment or product
      tags:
      - treatment-product
  /api/v1/treatment-product/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: Treatment Product ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentProduct'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Delete treatment or product detail by id
      tags:
      - treatment-product
    get:
      consumes:
      - application/json
      parameters:
      - description: Treatment Product ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentProductResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get treatment or product detail by id
      tags:
      - treatment-product
    put:
      consumes:
      - application/json
      parameters:
      - description: Treatment Product ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.TreatmentProductRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_TreatmentProductResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update treatment or product detail by id
      tags:
      - treatment-product
  /api/v1/user:
    get:
      consumes:
      - application/json
      parameters:
      - in: query
        name: name
        type: string
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      - collectionFormat: multi
        example:
        - admin
        in: query
        items:
          type: string
        name: roles
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many user detail
      tags:
      - user
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Create new user
      tags:
      - user
  /api/v1/user-survey:
    get:
      consumes:
      - application/json
      parameters:
      - default: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        in: query
        minimum: 1
        name: page_size
        type: integer
      - example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        in: query
        name: skin_analyze_id
        type: string
      - example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        in: query
        name: user_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_UserSurvey'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.PaginationResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get many user survey detail
      tags:
      - user-survey
    post:
      consumes:
      - application/json
      parameters:
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UserSurveyRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_UserSurvey'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Create new user survey
      tags:
      - user-survey
  /api/v1/user-survey/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: User Survey ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_UserSurvey'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Get user survey detail by id
      tags:
      - user-survey
  /api/v1/user/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Delete user by id
      tags:
      - user
    get:
      consumes:
      - application/json
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      summary: Get user detail by id
      tags:
      - user
    put:
      consumes:
      - application/json
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update user detail by id
      tags:
      - user
  /api/v1/user/{id}/update-password:
    put:
      consumes:
      - application/json
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: Request body
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.UpdatePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/domain.SingleResponse-domain_Empty'
      security:
      - BearerAuth: []
      summary: Update user password by id
      tags:
      - user
schemes:
- http
securityDefinitions:
  BearerAuth:
    description: 'Format value: Bearer xxx'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
