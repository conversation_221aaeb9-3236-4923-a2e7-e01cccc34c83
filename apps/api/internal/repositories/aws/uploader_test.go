package aws_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/internal/repositories/aws"
	"api/internal/repositories/aws/mocks"
	"api/utils"
)

func TestS3Uploader(t *testing.T) {
	var (
		ctx = context.Background()

		mockS3Client   = new(mocks.S3Client)
		mockS3Uploader = new(mocks.S3Uploader)
		uploader       = aws.NewUploader(mockS3Client, mockS3Uploader)
	)

	var (
		bucketName     = "test-bucket"
		filename       = "test_file.mp4"
		objectKey      = fmt.Sprintf("media/2025-05-23_11-21-17/%s", filename)
		s3UploadOutput = manager.UploadOutput{Key: &objectKey}
		file           = utils.CreateDummyIOReader("test")
	)

	t.Run("Success upload file", func(t *testing.T) {
		mockS3Client.On(
			"HeadObject",
			mock.Anything,
			mock.Anything,
			mock.Anything,
		).
			Return(
				&s3.HeadObjectOutput{},
				nil,
			).
			Once()

		mockS3Uploader.On(
			"Upload",
			mock.Anything,
			mock.Anything,
		).
			Return(&s3UploadOutput, nil).
			Once()

		output, err := uploader.UploadS3Object(ctx, bucketName, objectKey, file)

		assert.NoError(t, err)
		assert.NotNil(t, output)
	})

	t.Run("Success create folder", func(t *testing.T) {
		mockS3Client.On(
			"PutObject",
			mock.Anything,
			mock.Anything,
			mock.Anything,
		).
			Return(
				&s3.PutObjectOutput{},
				nil,
			).
			Once()

		folderPath := utils.DummyID
		err := uploader.CreateFolder(ctx, bucketName, folderPath)

		assert.NoError(t, err)
	})

	t.Run("Success delete folder", func(t *testing.T) {
		mockS3Client.On(
			"DeleteObject",
			mock.Anything,
			mock.Anything,
			mock.Anything,
		).
			Return(
				&s3.DeleteObjectOutput{},
				nil,
			).
			Once()

		folderPath := utils.DummyID
		err := uploader.DeleteFolder(ctx, bucketName, folderPath)

		assert.NoError(t, err)
	})
}
