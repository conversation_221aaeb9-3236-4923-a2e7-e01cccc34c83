// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	mock "github.com/stretchr/testify/mock"
)

// NewS3Client creates a new instance of S3Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewS3Client(t interface {
	mock.TestingT
	Cleanup(func())
}) *S3Client {
	mock := &S3Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// S3Client is an autogenerated mock type for the S3Client type
type S3Client struct {
	mock.Mock
}

type S3Client_Expecter struct {
	mock *mock.Mock
}

func (_m *S3Client) EXPECT() *S3Client_Expecter {
	return &S3Client_Expecter{mock: &_m.Mock}
}

// DeleteObject provides a mock function for the type S3Client
func (_mock *S3Client) DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {
	var tmpRet mock.Arguments
	if len(optFns) > 0 {
		tmpRet = _mock.Called(ctx, params, optFns)
	} else {
		tmpRet = _mock.Called(ctx, params)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for DeleteObject")
	}

	var r0 *s3.DeleteObjectOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.DeleteObjectInput, ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)); ok {
		return returnFunc(ctx, params, optFns...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.DeleteObjectInput, ...func(*s3.Options)) *s3.DeleteObjectOutput); ok {
		r0 = returnFunc(ctx, params, optFns...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*s3.DeleteObjectOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *s3.DeleteObjectInput, ...func(*s3.Options)) error); ok {
		r1 = returnFunc(ctx, params, optFns...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// S3Client_DeleteObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteObject'
type S3Client_DeleteObject_Call struct {
	*mock.Call
}

// DeleteObject is a helper method to define mock.On call
//   - ctx
//   - params
//   - optFns
func (_e *S3Client_Expecter) DeleteObject(ctx interface{}, params interface{}, optFns ...interface{}) *S3Client_DeleteObject_Call {
	return &S3Client_DeleteObject_Call{Call: _e.mock.On("DeleteObject",
		append([]interface{}{ctx, params}, optFns...)...)}
}

func (_c *S3Client_DeleteObject_Call) Run(run func(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options))) *S3Client_DeleteObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]func(*s3.Options))
		run(args[0].(context.Context), args[1].(*s3.DeleteObjectInput), variadicArgs...)
	})
	return _c
}

func (_c *S3Client_DeleteObject_Call) Return(deleteObjectOutput *s3.DeleteObjectOutput, err error) *S3Client_DeleteObject_Call {
	_c.Call.Return(deleteObjectOutput, err)
	return _c
}

func (_c *S3Client_DeleteObject_Call) RunAndReturn(run func(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)) *S3Client_DeleteObject_Call {
	_c.Call.Return(run)
	return _c
}

// HeadObject provides a mock function for the type S3Client
func (_mock *S3Client) HeadObject(ctx context.Context, params *s3.HeadObjectInput, optFns ...func(*s3.Options)) (*s3.HeadObjectOutput, error) {
	var tmpRet mock.Arguments
	if len(optFns) > 0 {
		tmpRet = _mock.Called(ctx, params, optFns)
	} else {
		tmpRet = _mock.Called(ctx, params)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for HeadObject")
	}

	var r0 *s3.HeadObjectOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.HeadObjectInput, ...func(*s3.Options)) (*s3.HeadObjectOutput, error)); ok {
		return returnFunc(ctx, params, optFns...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.HeadObjectInput, ...func(*s3.Options)) *s3.HeadObjectOutput); ok {
		r0 = returnFunc(ctx, params, optFns...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*s3.HeadObjectOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *s3.HeadObjectInput, ...func(*s3.Options)) error); ok {
		r1 = returnFunc(ctx, params, optFns...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// S3Client_HeadObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeadObject'
type S3Client_HeadObject_Call struct {
	*mock.Call
}

// HeadObject is a helper method to define mock.On call
//   - ctx
//   - params
//   - optFns
func (_e *S3Client_Expecter) HeadObject(ctx interface{}, params interface{}, optFns ...interface{}) *S3Client_HeadObject_Call {
	return &S3Client_HeadObject_Call{Call: _e.mock.On("HeadObject",
		append([]interface{}{ctx, params}, optFns...)...)}
}

func (_c *S3Client_HeadObject_Call) Run(run func(ctx context.Context, params *s3.HeadObjectInput, optFns ...func(*s3.Options))) *S3Client_HeadObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]func(*s3.Options))
		run(args[0].(context.Context), args[1].(*s3.HeadObjectInput), variadicArgs...)
	})
	return _c
}

func (_c *S3Client_HeadObject_Call) Return(headObjectOutput *s3.HeadObjectOutput, err error) *S3Client_HeadObject_Call {
	_c.Call.Return(headObjectOutput, err)
	return _c
}

func (_c *S3Client_HeadObject_Call) RunAndReturn(run func(ctx context.Context, params *s3.HeadObjectInput, optFns ...func(*s3.Options)) (*s3.HeadObjectOutput, error)) *S3Client_HeadObject_Call {
	_c.Call.Return(run)
	return _c
}

// PutObject provides a mock function for the type S3Client
func (_mock *S3Client) PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {
	var tmpRet mock.Arguments
	if len(optFns) > 0 {
		tmpRet = _mock.Called(ctx, params, optFns)
	} else {
		tmpRet = _mock.Called(ctx, params)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for PutObject")
	}

	var r0 *s3.PutObjectOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.PutObjectInput, ...func(*s3.Options)) (*s3.PutObjectOutput, error)); ok {
		return returnFunc(ctx, params, optFns...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *s3.PutObjectInput, ...func(*s3.Options)) *s3.PutObjectOutput); ok {
		r0 = returnFunc(ctx, params, optFns...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*s3.PutObjectOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *s3.PutObjectInput, ...func(*s3.Options)) error); ok {
		r1 = returnFunc(ctx, params, optFns...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// S3Client_PutObject_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PutObject'
type S3Client_PutObject_Call struct {
	*mock.Call
}

// PutObject is a helper method to define mock.On call
//   - ctx
//   - params
//   - optFns
func (_e *S3Client_Expecter) PutObject(ctx interface{}, params interface{}, optFns ...interface{}) *S3Client_PutObject_Call {
	return &S3Client_PutObject_Call{Call: _e.mock.On("PutObject",
		append([]interface{}{ctx, params}, optFns...)...)}
}

func (_c *S3Client_PutObject_Call) Run(run func(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options))) *S3Client_PutObject_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := args[2].([]func(*s3.Options))
		run(args[0].(context.Context), args[1].(*s3.PutObjectInput), variadicArgs...)
	})
	return _c
}

func (_c *S3Client_PutObject_Call) Return(putObjectOutput *s3.PutObjectOutput, err error) *S3Client_PutObject_Call {
	_c.Call.Return(putObjectOutput, err)
	return _c
}

func (_c *S3Client_PutObject_Call) RunAndReturn(run func(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)) *S3Client_PutObject_Call {
	_c.Call.Return(run)
	return _c
}
