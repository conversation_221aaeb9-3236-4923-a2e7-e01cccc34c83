package aws

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/rs/zerolog/log"
)

//go:generate mockery
type S3Uploader interface {
	Upload(
		ctx context.Context,
		input *s3.PutObjectInput,
		opts ...func(*manager.Uploader),
	) (*manager.UploadOutput, error)
}

//go:generate mockery
type S3Client interface {
	PutObject(
		ctx context.Context,
		params *s3.PutObjectInput,
		optFns ...func(*s3.Options),
	) (*s3.PutObjectOutput, error)
	HeadObject(
		ctx context.Context,
		params *s3.HeadObjectInput,
		optFns ...func(*s3.Options),
	) (*s3.HeadObjectOutput, error)
	DeleteObject(
		ctx context.Context,
		params *s3.DeleteObjectInput,
		optFns ...func(*s3.Options),
	) (*s3.DeleteObjectOutput, error)
}

type S3Action struct {
	s3Client   S3Client
	s3Uploader S3Uploader
}

func NewUploader(s3Client S3Client, s3Uploader S3Uploader) S3Action {
	return S3Action{s3Client: s3Client, s3Uploader: s3Uploader}
}

func (actor S3Action) UploadS3Object(
	ctx context.Context,
	bucketName string,
	objectKey string,
	file io.Reader,
) (*manager.UploadOutput, error) {
	bucketInfo := s3.HeadObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectKey),
	}

	input := s3.PutObjectInput{
		Bucket: bucketInfo.Bucket,
		Key:    bucketInfo.Key,
		Body:   file,
	}

	output, err := actor.s3Uploader.Upload(
		ctx,
		&input,
	)

	if err != nil {
		log.Error().Err(err).Msgf(
			"Couldn't upload object to %v:%v.",
			bucketName, objectKey,
		)
		return nil, err
	}

	err = s3.
		NewObjectExistsWaiter(actor.s3Client).
		Wait(
			ctx,
			&bucketInfo,
			time.Minute,
		)

	if err != nil {
		log.Error().Err(err).Msgf(
			"Failed attempt to wait for object %s to exist.\n",
			objectKey,
		)
		return nil, err
	}

	return output, nil
}

func (u S3Action) CreateFolder(
	ctx context.Context,
	bucketName, folderName string,
) error {
	if folderName[len(folderName)-1] != '/' {
		folderName += "/"
	}

	_, err := u.s3Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: &bucketName,
		Key:    &folderName,
	})

	if err != nil {
		return fmt.Errorf("failed to create folder %s in bucket %s: %v", folderName, bucketName, err)
	}

	return nil
}

func (u S3Action) DeleteFolder(
	ctx context.Context,
	bucketName, folderName string,
) error {
	if folderName[len(folderName)-1] != '/' {
		folderName += "/"
	}

	_, err := u.s3Client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: &bucketName,
		Key:    &folderName,
	})

	if err != nil {
		return fmt.Errorf("failed to delete folder %s in bucket %s: %v", folderName, bucketName, err)
	}

	return nil
}
