package http

import (
	"api/domain"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"

	"github.com/rs/zerolog/log"
)

type RecommendationHttp struct {
	client  *http.Client
	baseURL string
}

func NewRecommendationHttp() *RecommendationHttp {
	mlApiUrl := os.Getenv("ML_API_URL")
	if mlApiUrl == "" {
		mlApiUrl = "http://localhost:8080"
	}
	return &RecommendationHttp{
		client:  &http.Client{},
		baseURL: mlApiUrl,
	}
}

func parseErrorDetail(resp *http.Response) error {
	bodyBytes, _ := io.ReadAll(resp.Body)

	var errDetail struct {
		Detail string `json:"detail"`
	}
	_ = json.Unmarshal(bodyBytes, &errDetail)

	if errDetail.Detail != "" {
		log.Error().Str("detail", errDetail.Detail).Int("status_code", resp.StatusCode).Msg("ML API error")
		return fmt.Errorf("ML API error: %s (status code: %d)", errDetail.Detail, resp.StatusCode)
	}

	log.Error().Str("body", string(bodyBytes)).Int("status_code", resp.StatusCode).Msg("ML API error - unstructured")
	return fmt.Errorf("ML API error: %s (status code: %d)", http.StatusText(resp.StatusCode), resp.StatusCode)
}

func (h *RecommendationHttp) RecommendationTreatment(
	ctx context.Context,
	data *domain.RecommendationTreatmentMLRequest,
) (*domain.RecommendationTreatmentMLResponse, error) {
	url := fmt.Sprintf("%s/recommendation-treatment", h.baseURL)

	reqBody, err := json.Marshal(data)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal request body")
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := h.client.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send request to ML API")
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, parseErrorDetail(resp)
	}

	var response domain.RecommendationTreatmentMLResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		log.Error().Err(err).Msg("Failed to decode recommendation treatment response")
		return nil, err
	}

	return &response, nil
}

func (h *RecommendationHttp) GetRecommendation(
	ctx context.Context,
	data *domain.GetRecommendationMLRequest,
) (*domain.RecommendationResponse, error) {
	url := fmt.Sprintf("%s/recommendation", h.baseURL)

	reqBody, err := json.Marshal(data)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal request body")
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := h.client.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send request to ML API")
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, parseErrorDetail(resp)
	}

	var mlResponse struct {
		Data struct {
			Status string                        `json:"status"`
			Data   domain.RecommendationResponse `json:"data"`
			Error  interface{}                   `json:"error"`
		} `json:"data"`
	}
	err = json.NewDecoder(resp.Body).Decode(&mlResponse)
	if err != nil {
		log.Error().Err(err).Msg("Failed to decode get recommendation response")
		return nil, err
	}

	return &mlResponse.Data.Data, nil
}
