package http

import (
	"api/domain"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"

	"github.com/rs/zerolog/log"
)

type FaceAgingHttp struct {
	client  *http.Client
	baseURL string
}

func NewFaceAgingHttp() *FaceAgingHttp {
	mlApiUrl := os.Getenv("ML_API_URL")
	if mlApiUrl == "" {
		mlApiUrl = "http://localhost:8080"
	}
	return &FaceAgingHttp{
		client:  &http.Client{},
		baseURL: mlApiUrl,
	}
}

func (h *FaceAgingHttp) FaceAgingWithConcern(
	ctx context.Context,
	id string,
	data *domain.FaceAgingConcernMLRequest,
) (*domain.FaceAgingConcernMLResponse, error) {
	url := fmt.Sprintf("%s/face-aging/concerns/%s", h.baseURL, id)

	reqBody, err := json.Marshal(data)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal request body")
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.Error().Err(err).Msg("Failed to create request")
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := h.client.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send request")
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Error().Msgf("Error response body: %s", string(bodyBytes))
		log.Error().Msgf("Failed to upload image, status code: %d", resp.StatusCode)
		return nil, fmt.Errorf("failed to upload image, status code: %d", resp.StatusCode)
	}

	var response domain.FaceAgingConcernMLResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		log.Error().Err(err).Msg("Failed to decode response body")
		return nil, err
	}

	return &response, nil
}
