package postgres_test

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
)

func newLogDataJSON(status string, message string, testMarker string) json.RawMessage {
	return json.RawMessage(
		fmt.Sprintf(`{"status": "%s", "message": "%s", "test_marker": "%s"}`,
			status,
			message,
			testMarker,
		),
	)
}

func createTestLogWithTimestamp(
	t *testing.T,
	ctx context.Context,
	dbPool *pgxpool.Pool,
	ts time.Time,
	status string,
	messageSuffix string,
) string {
	t.Helper()
	var logID string
	tsUTC := ts.In(time.UTC)
	jsonData := newLogDataJSON(
		status,
		fmt.Sprintf("Log for statistics - %s", messageSuffix),
		messageSuffix,
	)

	createdAtUnixMilli := tsUTC.UnixMilli()

	err := dbPool.QueryRow(ctx,
		`INSERT INTO machine_sync_logs (data, created_at) VALUES ($1, $2) RETURNING id::text`,
		jsonData, createdAtUnixMilli).Scan(&logID)
	require.NoError(
		t,
		err,
		"Error log insert: timestamp %v (milidetik %d), status %s",
		tsUTC,
		createdAtUnixMilli,
		status,
	)
	return logID
}

func TestMachineSyncRepository(t *testing.T) {
	dbPool := utils.GetTestDBPool(t)
	t.Cleanup(func() {
		dbPool.Close()
	})

	repo := postgres.NewMachineSyncLogRepository(dbPool)
	ctx := context.Background()

	t.Run("TestCreateMachineSyncLog", func(t *testing.T) {
		logData := json.RawMessage(`{
            "folder_name": "john doe create",
            "images": ["rgb.jpg", "pl_texture.jpg"],
            "pdf": "customer_report.pdf",
            "status": "success",
            "message": null,
            "test_marker": "create_log_test"
        }`)
		log := &domain.MachineSyncLog{
			Data: logData,
		}

		createdLog, err := repo.CreateMachineSyncLog(ctx, log)
		require.NoError(t, err, "Failed to create machine sync log")
		require.NotNil(t, createdLog, "Log should not be nil")
		assert.NotEmpty(t, createdLog.ID, "ID should not be empty")

		t.Cleanup(func() {
			_, errDel := dbPool.Exec(ctx, `DELETE from machine_sync_logs WHERE id = $1`, createdLog.ID)
			require.NoErrorf(t, errDel, "Error cleanup machine sync log: %v", errDel)
		})
	})

	t.Run("TestGetManyMachineSyncLog", func(t *testing.T) {
		t.Run("Success get success and fail machine sync log", func(t *testing.T) {
			baseTime := time.Now().In(time.UTC).Truncate(time.Millisecond)
			message := "success_fail_today_get_many"
			createTestLogWithTimestamp(t, ctx, dbPool, baseTime, "success", message)
			createTestLogWithTimestamp(t, ctx, dbPool, baseTime, "error", message)

			createTestLogWithTimestamp(t, ctx, dbPool, baseTime.Add(-1*24*time.Hour), "error", message)

			filter := domain.MachineSyncLogFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 10,
				},
			}
			getData, statistic, pagination, err := repo.GetManyMachineSyncLog(ctx, &filter)
			require.NoError(t, err, "Failed to get many machine sync log")
			require.NotNil(t, getData, "Failed get many data empty")
			require.NotNil(t, statistic, "Failed get many statistic data empty")
			require.NotNil(t, pagination, "Failed get pagination data")

			assert.GreaterOrEqual(t, statistic.SuccessCount, uint32(1))
			assert.GreaterOrEqual(t, statistic.ErrorCount, uint32(1))
			assert.Equal(t, 2, pagination)

			t.Cleanup(func() {
				_, errDel := dbPool.Exec(ctx, `DELETE FROM machine_sync_logs WHERE data->>'test_marker' = $1`, message)
				require.NoErrorf(t, errDel, "Error cleanup machine sync log: %v", errDel)
			})
		})
	})
}
