package postgres

import (
	"context"
	"fmt"
	"strings"
	"text/template"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"api/domain"
	"api/utils"
)

type skinProblemRepository struct {
	db *pgxpool.Pool
}

func NewSkinProblemRepository(
	db *pgxpool.Pool,
) *skinProblemRepository {
	return &skinProblemRepository{db}
}

func insertSkinProblemGroup(
	ctx context.Context,
	tx pgx.Tx,
	skinProblemID string,
	indicationIDs []string,
) (err error) {
	var problemGroupRows []domain.SkinProblemGroup

	for i, id := range indicationIDs {
		problemGroupRows = append(
			problemGroupRows,
			domain.SkinProblemGroup{
				SkinProblemID:           skinProblemID,
				SkinProblemIndicationID: id,
				ProblemOrder:            i,
			},
		)
	}

	problemGroupRowSrc := pgx.CopyFromSlice(
		len(problemGroupRows),
		func(i int) ([]any, error) {
			return []any{
				problemGroupRows[i].SkinProblemID,
				problemGroupRows[i].SkinProblemIndicationID,
				problemGroupRows[i].ProblemOrder,
			}, nil
		},
	)

	_, err = tx.CopyFrom(
		ctx,
		pgx.Identifier{"skin_problem_groups"},
		[]string{
			"skin_problem_id",
			"skin_problem_indication_id",
			"problem_order",
		},
		problemGroupRowSrc,
	)

	return err
}

func (repo *skinProblemRepository) GetSkinProblemIndications(
	ctx context.Context,
	ids []string,
) (data []domain.SkinProblemIndication, err error) {
	indicationArgs := pgx.StrictNamedArgs{"indicationIDs": ids}

	indicationQuery := `
	SELECT *
	FROM skin_problem_indications
	WHERE id = any (@indicationIDs)
	ORDER BY
	array_position(@indicationIDs, skin_problem_indications.id)
	`

	if len(ids) > 0 {
		rows, err := repo.db.Query(ctx, indicationQuery, indicationArgs)
		defer rows.Close()

		if err != nil {
			log.Error().Err(err).Msg("Failed to query skin problem indication")
			return nil, err
		}

		indicationData, err := pgx.CollectRows(
			rows,
			pgx.RowToStructByName[domain.SkinProblemIndication],
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get skin problem indication")
			return nil, err
		}

		data = indicationData
	}

	if len(ids) != len(data) {
		var foundIDs []string
		for _, item := range data {
			foundIDs = append(foundIDs, item.ID)
		}

		notFoundIDs := utils.DiffIDs(ids, foundIDs)

		return nil, fmt.Errorf(
			"Skin problem indication IDs not found: %v",
			strings.Join(notFoundIDs, ", "),
		)
	}

	return data, nil
}

func (repo *skinProblemRepository) Create(
	ctx context.Context,
	request *domain.SkinProblemRequest,
) (*domain.SkinProblem, error) {
	var (
		name = cases.
			Title(language.English).
			String(request.Name)

		args = pgx.StrictNamedArgs{
			"name": name,
		}
	)

	insertQuery := `
	INSERT INTO skin_problems (name)
	VALUES (@name)
	RETURNING *
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	newRows, err := tx.Query(
		ctx,
		insertQuery,
		args,
	)

	defer newRows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert skin problem")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	newData, err := pgx.CollectExactlyOneRow(
		newRows,
		pgx.RowToAddrOfStructByName[domain.SkinProblem],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get insert skin problem result")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	err = insertSkinProblemGroup(
		ctx,
		tx,
		newData.ID,
		request.SkinProblemIndicationIDs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert skin problem group")
		tx.Rollback(ctx)
		return nil, err
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit create skin problem transaction")
		return nil, err
	}

	return newData, nil
}

func (repo *skinProblemRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblemResponse, error) {
	args := pgx.StrictNamedArgs{"id": &id}

	query := `
	SELECT
	skin_problems.*,
	jsonb_agg(spi.* order by spg.problem_order asc) filter (where spi.id is not null) as skin_problem_indications
	FROM skin_problems
	LEFT JOIN skin_problem_groups as spg on skin_problems.id = spg.skin_problem_id
	LEFT JOIN skin_problem_indications as spi on spg.skin_problem_indication_id = spi.id
	WHERE skin_problems.id = @id
	GROUP BY skin_problems.id
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to get skin problem by id")
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.SkinProblemResponse],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get skin problem by id result")
		return nil, utils.CustomPostgresErr(err)
	}

	return data, nil
}

func (repo *skinProblemRepository) UpdateByID(
	ctx context.Context,
	existingData *domain.SkinProblemResponse,
	request *domain.SkinProblemRequest,
) (*domain.SkinProblem, error) {
	var (
		name = cases.
			Title(language.English).
			String(request.Name)

		deleteSkinProblemRelationArgs = pgx.StrictNamedArgs{
			"skinProblemID": existingData.ID,
		}

		updateSkinProblemArgs = pgx.StrictNamedArgs{
			"skinProblemID": existingData.ID,
			"name":          name,
		}
	)

	deleteSkinProblemGroupQuery := `
	DELETE
	FROM skin_problem_groups
	WHERE skin_problem_id = @skinProblemID
	`

	updateSkinProblemQuery := `
	UPDATE skin_problems
	SET
	name = @name,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = @skinProblemID
	RETURNING *
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	_, err = tx.Exec(
		ctx,
		deleteSkinProblemGroupQuery,
		deleteSkinProblemRelationArgs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed delete existing skin problem group")
		tx.Rollback(ctx)
		return nil, err
	}

	rows, err := tx.Query(
		ctx,
		updateSkinProblemQuery,
		updateSkinProblemArgs,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to update skin problem")
		tx.Rollback(ctx)
		return nil, err
	}

	updatedData, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.SkinProblem],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get treatment skin problem result")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	err = insertSkinProblemGroup(
		ctx,
		tx,
		updatedData.ID,
		request.SkinProblemIndicationIDs,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert skin problem group")
		tx.Rollback(ctx)
		return nil, err
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit create skin problem transaction")
		return nil, err
	}

	return updatedData, nil
}

func (repo *skinProblemRepository) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.SkinProblem, error) {
	args := pgx.StrictNamedArgs{"id": id}

	query := `
	DELETE
	FROM skin_problems
	WHERE id = @id
	RETURNING *
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to run delete skin problem query")
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.SkinProblem],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get delete skin problem result")
		return nil, utils.CustomPostgresErr(err)
	}

	return data, nil
}

func (repo *skinProblemRepository) GetMany(
	ctx context.Context,
	filter *domain.SkinProblemFilter,
) ([]domain.SkinProblemResponse, int, error) {
	var (
		baseQueryBuilder  = new(strings.Builder)
		countQueryBuilder = new(strings.Builder)
		conditionalTmpl   = make(map[string]string)
		composedFilter    []string
		filterArgs        = make(pgx.StrictNamedArgs)
	)

	baseQueryTmpl := `
	SELECT
	skin_problems.*,
	jsonb_agg(spi.* order by spg.problem_order asc) filter (where spi.id is not null) as skin_problem_indications
	FROM skin_problems
	LEFT JOIN skin_problem_groups as spg on skin_problems.id = spg.skin_problem_id
	LEFT JOIN skin_problem_indications as spi on spg.skin_problem_indication_id = spi.id
	{{if .Where}}{{.Where}}{{end -}}
	GROUP BY skin_problems.id
	`

	countQueryTmpl := `
	SELECT
	count(distinct skin_problems.name)
	FROM skin_problems
	LEFT JOIN skin_problem_groups as spg on skin_problems.id = spg.skin_problem_id
	LEFT JOIN skin_problem_indications as spi on spg.skin_problem_indication_id = spi.id
	{{if .Where}}{{.Where}}{{end -}}
	`

	if filter.Name != "" {
		composedFilter = append(composedFilter, `skin_problems.name ilike @name`)
		filterArgs["name"] = fmt.Sprintf("%%%s%%", filter.Name)
	}

	if filter.Type != nil {
		problemType := *filter.Type

		switch problemType {
		case domain.SkinProblemSpecial:
			composedFilter = append(composedFilter, `spi.id is null`)
		case domain.SkinProblemGeneral:
			composedFilter = append(composedFilter, `spi.id is not null`)
		}
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		conditionalTmpl["Where"] = *whereClause
	}

	baseTmpl := template.Must(template.New("baseQuery").Parse(baseQueryTmpl))
	if err := baseTmpl.Execute(baseQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("Error build base query: %v", err)
	}

	countTmpl := template.Must(template.New("countQuery").Parse(countQueryTmpl))
	if err := countTmpl.Execute(countQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("Error build count query: %v", err)
	}

	baseQuery := baseQueryBuilder.String()
	countQuery := countQueryBuilder.String()

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery, filterArgs).Scan(&totalData)

	if err != nil {
		log.Error().Err(err).Msg("Failed to query total skin problems")
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery, filterArgs)
	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to query get many skin problems")
		return nil, 0, err
	}

	results, err := pgx.CollectRows(
		rows,
		pgx.RowToStructByName[domain.SkinProblemResponse],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to collect get many skin problems")
		return nil, 0, err
	}

	return results, totalData, nil
}
