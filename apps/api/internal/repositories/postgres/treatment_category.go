package postgres

import (
	"context"
	"fmt"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"api/domain"
	"api/utils"
)

type treatmentCategoryRepository struct {
	db *pgxpool.Pool
}

func NewTreatmentCategoryRepository(
	db *pgxpool.Pool,
) *treatmentCategoryRepository {
	return &treatmentCategoryRepository{db}
}

func (repo *treatmentCategoryRepository) Create(
	ctx context.Context,
	data *domain.TreatmentCategory,
) error {
	duplicateQuery := `
	SELECT
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_categories
	WHERE name = $1
	`

	name := cases.
		Title(language.English).
		String(data.Name)

	err := repo.db.QueryRow(
		ctx,
		duplicateQuery,
		name,
	).
		Scan(
			&data.ID,
			&data.Name,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil && err != pgx.ErrNoRows {
		return err
	}

	if data.ID != "" {
		return nil
	}

	insertQuery := `
	INSERT INTO treatment_categories (name)
	VALUES ($1)
	RETURNING
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	`

	err = repo.db.QueryRow(
		ctx,
		insertQuery,
		name,
	).
		Scan(
			&data.ID,
			&data.Name,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil {
		return err
	}

	return nil
}

func (repo *treatmentCategoryRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentCategory, error) {
	query := `
	SELECT
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_categories
	WHERE id = $1
	`

	var data domain.TreatmentCategory

	err := repo.db.QueryRow(
		ctx,
		query,
		&id,
	).
		Scan(
			&data.ID,
			&data.Name,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, err
	}

	return &data, nil
}

func (repo *treatmentCategoryRepository) UpdateByID(
	ctx context.Context,
	existingData *domain.TreatmentCategory,
	newData *domain.TreatmentCategory,
) error {
	query := `
	UPDATE treatment_categories
	SET
	name = $1,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = $2
	RETURNING
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	`

	name := cases.
		Title(language.English).
		String(newData.Name)

	err := repo.db.QueryRow(
		ctx,
		query,
		name,
		newData.ID,
	).
		Scan(
			&existingData.ID,
			&existingData.Name,
			&existingData.CreatedAt,
			&existingData.CreatedBy,
			&existingData.UpdatedAt,
			&existingData.UpdatedBy,
		)

	if err != nil {
		return err
	}

	return nil
}

func (repo *treatmentCategoryRepository) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentCategory, error) {
	query := `
	DELETE
	FROM treatment_categories
	WHERE id = $1
	RETURNING
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	`

	var data domain.TreatmentCategory

	err := repo.db.QueryRow(
		ctx,
		query,
		&id,
	).
		Scan(
			&data.ID,
			&data.Name,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, utils.CustomPostgresErr(err)
	}

	return &data, nil
}

func (repo *treatmentCategoryRepository) GetMany(
	ctx context.Context,
	filter *domain.TreatmentCategoryFilter,
) ([]domain.TreatmentCategory, int, error) {
	baseQuery := `
	SELECT
	id,
	name,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_categories
	`

	countQuery := `SELECT count(*) FROM treatment_categories`

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery).Scan(&totalData)

	if err != nil {
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery)
	defer rows.Close()

	if err != nil {
		return nil, 0, err
	}

	var results []domain.TreatmentCategory

	for rows.Next() {
		var data domain.TreatmentCategory

		err = rows.Scan(
			&data.ID,
			&data.Name,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

		if err != nil {
			return nil, 0, err
		}

		results = append(results, data)
	}

	return results, totalData, nil
}
