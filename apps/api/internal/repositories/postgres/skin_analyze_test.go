package postgres_test

import (
	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
	"context"
	"fmt"
	"time"

	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSkinAnalyzeRepository(t *testing.T) {
	dbPool := utils.GetTestDBPool(t)
	t.Cleanup(func() {
		dbPool.Close()
	})

	repo := postgres.NewSkinAnalyzeRepository(dbPool)
	ctx := context.Background()

	password := "test-password"
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
	dummyUser := &domain.User{
		Name:        "Test User 3",
		Email:       email,
		Role:        domain.Branch,
		PhoneNumber: "08123456789",
		Password:    &password,
	}
	createUser, err := createTestUser(t, dbPool, dummyUser)
	require.NoError(t, err)

	createdID := ""

	var ids []string
	cleanupByIDs := `
	delete
	from skin_analyzes
	where id = any (@ids)
	`

	t.Cleanup(func() {
		args := pgx.NamedArgs{"ids": ids}

		err := utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupByIDs,
			args,
		)
		require.NoErrorf(t, err, "Error cleanup: %v", err)

		cleanupUser := `
        delete
        from users
        where id = @id
        `
		args = pgx.NamedArgs{"id": createUser.ID}
		err = utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupUser,
			args,
		)
		require.NoErrorf(t, err, "Error cleanup: %v", err)
	})

	actualAge := 30
	phoneNumber := "1234567890"
	evaluationRate := 5
	skinAge := 30
	skinCondition := "Good"
	plTexture := 75
	uvPorphyrin := 80
	uvPigmentation := 60
	uvMoisture := 70
	sensitiveArea := 70
	brownArea := 80
	uvDamage := 90
	suggestion := "Use sunscreen"
	skinAnalyze := &domain.SkinAnalyze{
		OperatorID:     createUser.ID,
		Name:           "John Doe",
		ActualAge:      &actualAge,
		InputDate:      "2023-10-01",
		PhoneNumber:    &phoneNumber,
		EvaluationRate: &evaluationRate,
		SkinAge:        &skinAge,
		SkinCondition:  &skinCondition,
		RGBPore:        75,
		RGBSpot:        50,
		RGBWrinkle:     70,
		PLTexture:      &plTexture,
		UVPorphyrin:    &uvPorphyrin,
		UVPigmentation: &uvPigmentation,
		UVMoisture:     &uvMoisture,
		SensitiveArea:  &sensitiveArea,
		BrownArea:      &brownArea,
		UVDamage:       &uvDamage,
		Suggestion:     &suggestion,
		PathImages:     []string{"image1.jpg", "image2.jpg"},
		PathPDF:        "/path/to/pdf",
	}

	t.Run("TestCreateSkinAnalyze", func(t *testing.T) {
		skinAnalyze, err := repo.CreateSkinAnalyze(ctx, skinAnalyze)
		require.NoError(t, err, "Failed to create skin analyze")
		require.NotNil(t, skinAnalyze, "Skin analyze should not be nil")

		assert.Equal(t, "John Doe", skinAnalyze.Name, "Name should match")

		assert.NotEmpty(t, skinAnalyze.ID, "ID should not be empty")
		ids = append(ids, skinAnalyze.ID)
		createdID = skinAnalyze.ID
	})

	t.Run("TestGetSkinAnalyzeByID", func(t *testing.T) {
		skinAnalyze, err := repo.GetSkinAnalyzeByID(ctx, createdID)
		require.NoError(t, err, "Failed to get skin analyze by ID")
		require.NotNil(t, skinAnalyze, "Skin analyze should not be nil")
		assert.Equal(t, createdID, skinAnalyze.ID, "ID should match")
	})

	t.Run("TestUpdateSkinAnalyzeByID", func(t *testing.T) {
		skinAnalyze, err := repo.GetSkinAnalyzeByID(ctx, createdID)
		require.NoError(t, err, "Failed to get skin analyze by ID")
		require.NotNil(t, skinAnalyze, "Skin analyze should not be nil")
		assert.Equal(t, createdID, skinAnalyze.ID, "ID should match")

		skinAnalyze.PathImages = append(skinAnalyze.PathImages, "new_image.jpg")

		updatedSkinAnalyze, err := repo.UpdateSkinAnalyzeByID(ctx, createdID, skinAnalyze)
		require.NoError(t, err, "Failed to update skin analyze")
		require.NotNil(t, updatedSkinAnalyze, "Updated skin analyze should not be nil")
		assert.Equal(t, skinAnalyze.ID, updatedSkinAnalyze.ID, "ID should match")
		assert.Contains(t, updatedSkinAnalyze.PathImages, "new_image.jpg", "PathImages should contain new_image.jpg")
	})

	t.Run("TestGetAllSkinAnalyzes", func(t *testing.T) {
		t.Run("Get many data skin analyzes", func(t *testing.T) {
			filter := domain.SkinAnalyzeFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 10,
				},
			}
			skinAnalyzes, count, err := repo.GetManySkinAnalyzes(ctx, &filter)
			require.NoError(t, err, "Failed to get all skin analyzes")
			require.NotEmpty(t, skinAnalyzes, "Skin analyzes should not be empty")
			assert.Greater(t, count, 0, "Count should be greater than 0")
			assert.Greater(t, len(skinAnalyzes), 0, "Skin analyzes should be greater than 0")
			assert.LessOrEqual(t, len(skinAnalyzes), filter.PageSize, "Skin analyzes should not exceed page size")
		})

		t.Run("Get many data skin analyzes with operator id filter", func(t *testing.T) {
			filter := domain.SkinAnalyzeFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 10,
				},
				OperatorID: createUser.ID,
			}
			skinAnalyzes, count, err := repo.GetManySkinAnalyzes(ctx, &filter)
			require.NoError(t, err, "Failed to get all skin analyzes")
			require.NotEmpty(t, skinAnalyzes, "Skin analyzes should not be empty")
			assert.Equal(t, count, 1, "Count should be greater than 0")
			assert.Equal(t, len(skinAnalyzes), 1, "Skin analyzes should be greater than 0")
			assert.LessOrEqual(t, len(skinAnalyzes), filter.PageSize, "Skin analyzes should not exceed page size")
		})

		t.Run("Get many data skin analyzes with name filter", func(t *testing.T) {
			filter := domain.SkinAnalyzeFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 10,
				},
				Name: "john",
			}
			skinAnalyzes, count, err := repo.GetManySkinAnalyzes(ctx, &filter)
			require.NoError(t, err, "Failed to get all skin analyzes")
			require.NotEmpty(t, skinAnalyzes, "Skin analyzes should not be empty")
			assert.Greater(t, count, 0, "Count should be greater than 0")
			assert.Greater(t, len(skinAnalyzes), 0, "Skin analyzes should be greater than 0")
			assert.LessOrEqual(t, len(skinAnalyzes), filter.PageSize, "Skin analyzes should not exceed page size")
		})

		t.Run("Get many data skin analyzes with sort", func(t *testing.T) {
			filter := domain.SkinAnalyzeFilter{
				Pagination: domain.Pagination{
					Page:     1,
					PageSize: 10,
				},
				SortColumn: "created_at",
				SortOrder:  "desc",
			}

			secondData := *skinAnalyze
			secondData.Name = "John Doe Two"

			newData, err := repo.CreateSkinAnalyze(ctx, &secondData)
			require.NoError(t, err, "Failed to create skin analyze")
			require.NotNil(t, newData, "Skin analyze should not be nil")

			ids = append(ids, newData.ID)

			skinAnalyzes, count, err := repo.GetManySkinAnalyzes(ctx, &filter)

			assert.NoError(t, err, "Failed to get all skin analyzes")
			assert.NotEmpty(t, skinAnalyzes, "Skin analyzes should not be empty")
			assert.Greater(t, count, 1, "Count should be greater than 1, for comparison")
			assert.GreaterOrEqual(
				t,
				skinAnalyzes[0].CreatedAt,
				skinAnalyzes[1].CreatedAt,
			)
		})
	})
}

func createTestSkinAnalyze(
	t *testing.T,
	p *pgxpool.Pool,
	sa *domain.SkinAnalyze,
) (*domain.SkinAnalyze, error) {
	t.Helper()
	repo := postgres.NewSkinAnalyzeRepository(p)
	ctx := context.Background()
	return repo.CreateSkinAnalyze(ctx, sa)
}
