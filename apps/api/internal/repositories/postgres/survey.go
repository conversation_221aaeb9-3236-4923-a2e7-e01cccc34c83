package postgres

import (
	"context"
	"fmt"
	"strings"
	"text/template"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"

	"api/domain"
	"api/utils"
)

type surveyRepository struct {
	db *pgxpool.Pool
}

func NewSurveyRepository(db *pgxpool.Pool) *surveyRepository {
	return &surveyRepository{db}
}

func insertChildQuestions(
	ctx context.Context,
	tx pgx.Tx,
	parentQuestionID string,
	childQuestions []domain.SurveyRequestChildQuestion,
) (err error) {
	childQuestionRowSrc := pgx.CopyFromSlice(
		len(childQuestions),
		func(i int) ([]any, error) {
			return []any{
				parentQuestionID,
				childQuestions[i].ParentQuestionAnswer,
				childQuestions[i].Description,
				childQuestions[i].Question,
				childQuestions[i].Answers,
				childQuestions[i].IsMultiple,
				childQuestions[i].Type,
				childQuestions[i].QuestionOrder,
				childQuestions[i].Category,
			}, nil
		},
	)

	_, err = tx.CopyFrom(
		ctx,
		pgx.Identifier{"survey_questions"},
		[]string{
			"parent_question_id",
			"parent_question_answer",
			"description",
			"question",
			"answers",
			"is_multiple",
			"type",
			"question_order",
			"category",
		},
		childQuestionRowSrc,
	)

	return err
}

func surveyGetTreatmentProductID(
	ctx context.Context,
	tx pgx.Tx,
) ([]string, error) {
	treatmentProductRows, err := tx.Query(
		ctx,
		`SELECT id FROM treatment_products`,
	)

	defer treatmentProductRows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to run query treatment product ids")
		return nil, err
	}

	treatmentProductIDs, err := pgx.CollectRows(
		treatmentProductRows,
		func(row pgx.CollectableRow) (string, error) {
			var id string
			err = treatmentProductRows.Scan(&id)
			return id, err
		},
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get treatment product ids")
		return nil, err
	}

	return treatmentProductIDs, nil
}

func surveyGetChildQuestionIDs(
	ctx context.Context,
	tx pgx.Tx,
	parentQuestionID string,
) ([]string, error) {
	getChildQuestionArgs := pgx.StrictNamedArgs{
		"parentQuestionID": parentQuestionID,
		"category":         domain.Contraindication,
	}

	getChildQuestionQuery := `
	SELECT id
	FROM survey_questions
	WHERE (parent_question_id = @parentQuestionID AND category = @category)
	`

	getChildQuestionRows, err := tx.Query(
		ctx,
		getChildQuestionQuery,
		getChildQuestionArgs,
	)

	defer getChildQuestionRows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to run get survey child question query")
		return nil, err
	}

	childQuestionIDs, err := pgx.CollectRows(
		getChildQuestionRows,
		func(row pgx.CollectableRow) (string, error) {
			var id string
			err = getChildQuestionRows.Scan(&id)
			return id, err
		},
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get survey child question")
		return nil, err
	}

	return childQuestionIDs, nil
}

func (repo *surveyRepository) Create(
	ctx context.Context,
	surveyData *domain.Survey,
) (*domain.Survey, error) {
	args := pgx.StrictNamedArgs{
		"parentQuestionID":     surveyData.ParentQuestionID,
		"parentQuestionAnswer": surveyData.ParentQuestionAnswer,
		"description":          surveyData.Description,
		"question":             surveyData.Question,
		"answers":              surveyData.Answers,
		"isMultiple":           surveyData.IsMultiple,
		"type":                 surveyData.Type,
		"questionOrder":        surveyData.QuestionOrder,
		"category":             surveyData.Category,
	}

	query := `
	INSERT INTO survey_questions
	(
		parent_question_id,
		parent_question_answer,
		description,
		question,
		answers,
		is_multiple,
		type,
		question_order,
		category
	)
	VALUES (
		@parentQuestionID,
		@parentQuestionAnswer,
		@description,
		@question,
		@answers,
		@isMultiple,
		@type,
		@questionOrder,
		@category
	)
	RETURNING *
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	surveyRows, err := tx.Query(
		ctx,
		query,
		args,
	)

	defer surveyRows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to insert survey")
		tx.Rollback(ctx)
		return nil, err
	}

	surveyData, err = pgx.CollectExactlyOneRow(
		surveyRows,
		pgx.RowToAddrOfStructByName[domain.Survey],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get survey result")
		tx.Rollback(ctx)
		return nil, err
	}

	if surveyData.Category == domain.Contraindication {
		treatmentProductIDs, err := surveyGetTreatmentProductID(ctx, tx)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get treatment product ids")
			tx.Rollback(ctx)
			return nil, err
		}

		var treatmentProductSurveyRows []domain.TreatmentProductSurvey

		for i := 0; i < len(treatmentProductIDs); i++ {
			treatmentProductSurveyRows = append(
				treatmentProductSurveyRows,
				domain.TreatmentProductSurvey{
					TreatmentProductID: treatmentProductIDs[i],
					SurveyID:           surveyData.ID,
					SelectedAnswer:     1,
				},
			)
		}

		treatmentProductSurveyRowSrc := pgx.CopyFromSlice(
			len(treatmentProductSurveyRows),
			func(i int) ([]any, error) {
				return []any{
					treatmentProductSurveyRows[i].TreatmentProductID,
					treatmentProductSurveyRows[i].SurveyID,
					treatmentProductSurveyRows[i].SelectedAnswer,
				}, nil
			},
		)

		_, err = tx.CopyFrom(
			ctx,
			pgx.Identifier{"treatment_products_surveys"},
			[]string{
				"treatment_product_id",
				"survey_id",
				"selected_answer",
			},
			treatmentProductSurveyRowSrc,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to copy from treatment_products_surveys")
			tx.Rollback(ctx)
			return nil, err
		}
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit insert survey data")
		return nil, err
	}

	return surveyData, nil
}

func (repo *surveyRepository) CreateNested(
	ctx context.Context,
	data *domain.SurveyRequestNested,
) (*domain.SurveyResponseNested, error) {
	parentQuestionArgs := pgx.StrictNamedArgs{
		"description":   data.Description,
		"question":      data.Question,
		"answers":       data.Answers,
		"isMultiple":    data.IsMultiple,
		"type":          data.Type,
		"questionOrder": data.QuestionOrder,
		"category":      data.Category,
	}

	insertParentQuestionQuery := `
	INSERT INTO survey_questions
	(
		description,
		question,
		answers,
		is_multiple,
		type,
		question_order,
		category
	)
	VALUES (
		@description,
		@question,
		@answers,
		@isMultiple,
		@type,
		@questionOrder,
		@category
	)
	RETURNING id
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	var parentQuestionID string

	err = tx.QueryRow(
		ctx,
		insertParentQuestionQuery,
		parentQuestionArgs,
	).
		Scan(&parentQuestionID)

	if err != nil {
		log.Error().Err(err).Msg("Failed to run CreateNested parent question query")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	err = insertChildQuestions(
		ctx,
		tx,
		parentQuestionID,
		data.ChildQuestions,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to run CreateNested child questions query")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	if data.Category == domain.Contraindication {
		treatmentProductIDs, err := surveyGetTreatmentProductID(ctx, tx)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get treatment product ids")
			tx.Rollback(ctx)
			return nil, err
		}

		childQuestionIDs, err := surveyGetChildQuestionIDs(ctx, tx, parentQuestionID)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get survey child question ids")
			tx.Rollback(ctx)
			return nil, err
		}

		allSurveyIDs := append(childQuestionIDs, parentQuestionID)

		var treatmentProductSurveyRows []domain.TreatmentProductSurvey

		for i := 0; i < len(treatmentProductIDs); i++ {
			for j := 0; j < len(allSurveyIDs); j++ {
				treatmentProductSurveyRows = append(
					treatmentProductSurveyRows,
					domain.TreatmentProductSurvey{
						TreatmentProductID: treatmentProductIDs[i],
						SurveyID:           allSurveyIDs[j],
						SelectedAnswer:     1,
					},
				)
			}
		}

		treatmentProductSurveyRowSrc := pgx.CopyFromSlice(
			len(treatmentProductSurveyRows),
			func(i int) ([]any, error) {
				return []any{
					treatmentProductSurveyRows[i].TreatmentProductID,
					treatmentProductSurveyRows[i].SurveyID,
					treatmentProductSurveyRows[i].SelectedAnswer,
				}, nil
			},
		)

		_, err = tx.CopyFrom(
			ctx,
			pgx.Identifier{"treatment_products_surveys"},
			[]string{
				"treatment_product_id",
				"survey_id",
				"selected_answer",
			},
			treatmentProductSurveyRowSrc,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to copy from treatment_products_surveys")
			tx.Rollback(ctx)
			return nil, err
		}
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit insert survey data")
		return nil, err
	}

	result, err := repo.GetByIDNested(ctx, &parentQuestionID)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get CreateNested result data")
		return nil, err
	}

	return result, nil
}

func (repo *surveyRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.Survey, error) {
	args := pgx.StrictNamedArgs{"id": *id}

	query := `
	SELECT *
	FROM survey_questions
	WHERE id = @id
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.Survey],
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, err
	}

	return data, nil
}

func (repo *surveyRepository) GetByIDNested(
	ctx context.Context,
	id *string,
) (*domain.SurveyResponseNested, error) {
	args := pgx.StrictNamedArgs{"id": *id}

	query := `
	with
	parent_questions as (
		select *
		from survey_questions as sq
		where sq.parent_question_id is null
	)
	select
	pq.*,
	jsonb_agg(cq.*) filter (where cq.id is not null) as child_questions
	from parent_questions as pq
	left join survey_questions as cq on pq.id = cq.parent_question_id
	where pq.id = @id
	group by
	pq.id,
	pq.parent_question_id,
	pq.parent_question_answer,
	pq.description,
	pq.category,
	pq.question,
	pq.answers,
	pq.is_multiple,
	pq.type,
	pq.question_order,
	pq.is_static,
	pq.created_at,
	pq.created_by,
	pq.updated_at,
	pq.updated_by
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to run GetNestedByID query")
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.SurveyResponseNested],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to collect GetNestedByID query result")
		return nil, utils.CustomPostgresErr(err)
	}

	return data, nil
}

func (repo *surveyRepository) UpdateByID(
	ctx context.Context,
	existingData *domain.Survey,
	newData *domain.Survey,
) (*domain.Survey, error) {
	args := pgx.StrictNamedArgs{
		"id":                   existingData.ID,
		"parentQuestionID":     newData.ParentQuestionID,
		"parentQuestionAnswer": newData.ParentQuestionAnswer,
		"description":          newData.Description,
		"question":             newData.Question,
		"answers":              newData.Answers,
		"isMultiple":           newData.IsMultiple,
		"type":                 newData.Type,
		"questionOrder":        newData.QuestionOrder,
		"category":             newData.Category,
		"isStatic":             false,
	}

	query := `
	UPDATE survey_questions
	SET
	parent_question_id = @parentQuestionID,
	parent_question_answer = @parentQuestionAnswer,
	description = @description,
	question = @question,
	answers = @answers,
	is_multiple = @isMultiple,
	type = @type,
	question_order = @questionOrder,
	category = @category,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = @id
	AND is_static = @isStatic
	RETURNING *
	`

	deleteTreatmentProductSurveyQuery := `
	DELETE
	FROM treatment_products_surveys
	WHERE survey_id = $1
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	rows, err := tx.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to run update survey query")
		tx.Rollback(ctx)
		return nil, err
	}

	updatedData, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.Survey],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get updated survey")
		tx.Rollback(ctx)
		return nil, err
	}

	if existingData.Category == domain.Contraindication &&
		newData.Category != domain.Contraindication {
		_, err := tx.Exec(
			ctx,
			deleteTreatmentProductSurveyQuery,
			updatedData.ID,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to delete treatment_products_surveys")
			tx.Rollback(ctx)
			return nil, err
		}
	}

	if existingData.Category != domain.Contraindication &&
		newData.Category == domain.Contraindication {
		treatmentProductIDs, err := surveyGetTreatmentProductID(ctx, tx)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get treatment product ids")
			tx.Rollback(ctx)
			return nil, err
		}

		var treatmentProductSurveyRows []domain.TreatmentProductSurvey

		for i := 0; i < len(treatmentProductIDs); i++ {
			treatmentProductSurveyRows = append(
				treatmentProductSurveyRows,
				domain.TreatmentProductSurvey{
					TreatmentProductID: treatmentProductIDs[i],
					SurveyID:           newData.ID,
					SelectedAnswer:     1,
				},
			)
		}

		treatmentProductSurveyRowSrc := pgx.CopyFromSlice(
			len(treatmentProductSurveyRows),
			func(i int) ([]any, error) {
				return []any{
					treatmentProductSurveyRows[i].TreatmentProductID,
					treatmentProductSurveyRows[i].SurveyID,
					treatmentProductSurveyRows[i].SelectedAnswer,
				}, nil
			},
		)

		_, err = tx.CopyFrom(
			ctx,
			pgx.Identifier{"treatment_products_surveys"},
			[]string{
				"treatment_product_id",
				"survey_id",
				"selected_answer",
			},
			treatmentProductSurveyRowSrc,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to copy from treatment_products_surveys")
			return nil, err
		}
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit update survey data")
		return nil, err
	}

	return updatedData, nil
}

func (repo *surveyRepository) UpdateByIDNested(
	ctx context.Context,
	id *string,
	newData *domain.SurveyRequestNested,
) (*domain.SurveyResponseNested, error) {
	var (
		existingCategory domain.SurveyCategory
		parentQuestionID string
	)

	parentQuestionArgs := pgx.StrictNamedArgs{
		"id":            *id,
		"description":   newData.Description,
		"question":      newData.Question,
		"answers":       newData.Answers,
		"isMultiple":    newData.IsMultiple,
		"type":          newData.Type,
		"questionOrder": newData.QuestionOrder,
		"category":      newData.Category,
		"isStatic":      false,
	}

	updateParentQuestionQuery := `
	UPDATE survey_questions
	SET
	description = @description,
	question = @question,
	answers = @answers,
	is_multiple = @isMultiple,
	type = @type,
	question_order = @questionOrder,
	category = @category,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = @id
	AND is_static = @isStatic
	RETURNING id
	`

	existingCategoryQuery := `
	SELECT category
	FROM survey_questions
	WHERE id = $1
	`

	deleteTreatmentProductSurveyQuery := `
	DELETE
	FROM treatment_products_surveys
	WHERE survey_id = $1
	`

	tx, err := repo.db.Begin(ctx)

	if err != nil {
		log.Error().Err(err).Msg("Error starting db transaction")
		return nil, fmt.Errorf("Error starting db transaction")
	}

	err = tx.QueryRow(
		ctx,
		existingCategoryQuery,
		*id,
	).Scan(&existingCategory)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to run UpdateByIDNested existing survey query",
		)
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	err = tx.QueryRow(
		ctx,
		updateParentQuestionQuery,
		parentQuestionArgs,
	).
		Scan(&parentQuestionID)

	if err != nil {
		log.Error().Err(err).Msg("Failed to run UpdateByIDNested parent question query")
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	// There's a case when user delete or create new child questions.
	// So, rather than check the child questions one by one, let's just delete
	// the existing one and insert the new one from request body.
	commandTag, err := tx.Exec(
		ctx,
		`DELETE FROM survey_questions WHERE parent_question_id = $1`,
		*id,
	)

	if err != nil {
		log.Error().Err(err).Msgf(
			"Error delete existing child questions: %s\n",
			commandTag.String(),
		)
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	err = insertChildQuestions(
		ctx,
		tx,
		parentQuestionID,
		newData.ChildQuestions,
	)

	if err != nil {
		log.Error().Err(err).Msg(
			"Failed to run UpdateByIDNested child questions query",
		)
		tx.Rollback(ctx)
		return nil, utils.CustomPostgresErr(err)
	}

	if existingCategory == domain.Contraindication &&
		newData.Category != domain.Contraindication {
		_, err := tx.Exec(
			ctx,
			deleteTreatmentProductSurveyQuery,
			parentQuestionID,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to delete treatment_products_surveys")
			tx.Rollback(ctx)
			return nil, err
		}
	}

	if newData.Category == domain.Contraindication {
		treatmentProductIDs, err := surveyGetTreatmentProductID(ctx, tx)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get treatment product ids")
			tx.Rollback(ctx)
			return nil, err
		}

		childQuestionIDs, err := surveyGetChildQuestionIDs(ctx, tx, parentQuestionID)

		if err != nil {
			log.Error().Err(err).Msg("Failed to get survey child question ids")
			tx.Rollback(ctx)
			return nil, err
		}

		var allSurveyIDs []string

		if existingCategory != domain.Contraindication {
			allSurveyIDs = append(allSurveyIDs, parentQuestionID)
		}
		allSurveyIDs = append(allSurveyIDs, childQuestionIDs...)

		var treatmentProductSurveyRows []domain.TreatmentProductSurvey

		for i := 0; i < len(treatmentProductIDs); i++ {
			for j := 0; j < len(allSurveyIDs); j++ {
				treatmentProductSurveyRows = append(
					treatmentProductSurveyRows,
					domain.TreatmentProductSurvey{
						TreatmentProductID: treatmentProductIDs[i],
						SurveyID:           allSurveyIDs[j],
						SelectedAnswer:     1,
					},
				)
			}
		}

		treatmentProductSurveyRowSrc := pgx.CopyFromSlice(
			len(treatmentProductSurveyRows),
			func(i int) ([]any, error) {
				return []any{
					treatmentProductSurveyRows[i].TreatmentProductID,
					treatmentProductSurveyRows[i].SurveyID,
					treatmentProductSurveyRows[i].SelectedAnswer,
				}, nil
			},
		)

		_, err = tx.CopyFrom(
			ctx,
			pgx.Identifier{"treatment_products_surveys"},
			[]string{
				"treatment_product_id",
				"survey_id",
				"selected_answer",
			},
			treatmentProductSurveyRowSrc,
		)

		if err != nil {
			log.Error().Err(err).Msg("Failed to copy from treatment_products_surveys")
			return nil, err
		}
	}

	if err := tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit update survey data")
		return nil, err
	}

	result, err := repo.GetByIDNested(ctx, &parentQuestionID)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get UpdateByIDNested result data")
		return nil, err
	}

	return result, nil
}

func (repo *surveyRepository) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.Survey, error) {
	args := pgx.StrictNamedArgs{"id": *id}

	query := `
	DELETE
	FROM survey_questions
	WHERE id = @id
	RETURNING *
	`

	rows, err := repo.db.Query(
		ctx,
		query,
		args,
	)

	defer rows.Close()

	if err != nil {
		return nil, err
	}

	data, err := pgx.CollectExactlyOneRow(
		rows,
		pgx.RowToAddrOfStructByName[domain.Survey],
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, err
	}

	return data, nil
}

func (repo *surveyRepository) GetMany(
	ctx context.Context,
	filter *domain.SurveyFilter,
) ([]domain.Survey, int, error) {
	var (
		composedFilter []string
		filterArgs     = make(pgx.StrictNamedArgs)
	)

	baseQuery := `
	SELECT *
	FROM survey_questions
	`

	countQuery := `
	SELECT count(*)
	FROM survey_questions
	`

	if filter.IsStatic != nil {
		composedFilter = append(composedFilter, `survey_questions.is_static = @isStatic`)
		filterArgs["isStatic"] = *filter.IsStatic
	}

	if filter.Question != nil {
		composedFilter = append(
			composedFilter,
			`survey_questions.question ilike @question`,
		)
		filterArgs["question"] = fmt.Sprintf("%%%s%%", *filter.Question)
	}

	if len(filter.Type) > 0 {
		composedFilter = append(composedFilter, `survey_questions.type = any (@type)`)
		filterArgs["type"] = filter.Type
	}

	if len(filter.Category) > 0 {
		composedFilter = append(composedFilter, `survey_questions.category = any (@category)`)
		filterArgs["category"] = filter.Category
	}

	// Check domain for list options (in `oneof` tag).
	if len(filter.Groups) > 0 {
		selectedGroup := make(map[string]bool)

		for _, group := range filter.Groups {
			selectedGroup[group] = true
		}

		if selectedGroup["primary"] && !selectedGroup["secondary"] {
			composedFilter = append(
				composedFilter,
				`survey_questions.parent_question_id is null`,
			)
		} else if !selectedGroup["primary"] && selectedGroup["secondary"] {
			composedFilter = append(
				composedFilter,
				`survey_questions.parent_question_id is not null`,
			)
		}
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		baseQuery += fmt.Sprintf("%s", *whereClause)
		countQuery += fmt.Sprintf("%s", *whereClause)
	}

	if filter.SortColumn != "" && filter.SortOrder != "" {
		var column string

		// Check domain file on oneof struct tag.
		switch filter.SortColumn {
		case "group":
			column = `survey_questions.parent_question_id is not null`
		case "mobile": // Check seeder/seed/survey_question_data.go.
			column = `
			array_position(
				array[
					'1f04ea29-b556-4793-80ca-f0314f49917d',
					'2097dda1-544a-4d7a-89c6-6314bcefa592',
					'959ac8b0-b1a7-4da6-beaf-70063c83fc53',
					'94e22537-8ff3-4702-ac26-87c0a60139cb',
					'cef51648-1f44-43dd-944f-a677d37f8db4',
					'4864e913-0c7f-458f-9b19-dd091ccb254e'
				]::uuid[],
				survey_questions.id
			)
			`
		default:
			column = fmt.Sprintf("survey_questions.%s", filter.SortColumn)
		}

		baseQuery += fmt.Sprintf(
			"ORDER BY %s %s\n",
			column,
			filter.SortOrder,
		)
	}

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery, filterArgs).Scan(&totalData)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get total survey")
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery, filterArgs)
	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to query get many survey")
		return nil, 0, err
	}

	results, err := pgx.CollectRows(
		rows,
		pgx.RowToStructByName[domain.Survey],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to collect get many survey results")
		return nil, 0, err
	}

	return results, totalData, nil
}

func (repo *surveyRepository) GetManyNested(
	ctx context.Context,
	filter *domain.SurveyFilterNested,
) ([]domain.SurveyResponseNested, int, error) {
	var (
		baseQueryBuilder = new(strings.Builder)
		conditionalTmpl  = make(map[string]string)
		composedFilter   []string
		filterArgs       = make(pgx.StrictNamedArgs)
	)

	baseQueryTmpl := `
	with
	parent_questions as (
		select *
		from survey_questions as sq
		where sq.parent_question_id is null
	)
	select
	pq.*,
	jsonb_agg(cq.*) filter (where cq.id is not null) as child_questions
	from parent_questions as pq
	left join survey_questions as cq on pq.id = cq.parent_question_id
	{{if .Where}}{{.Where}}{{end -}}
	group by
	pq.id,
	pq.parent_question_id,
	pq.parent_question_answer,
	pq.description,
	pq.category,
	pq.question,
	pq.answers,
	pq.is_multiple,
	pq.type,
	pq.question_order,
	pq.is_static,
	pq.created_at,
	pq.created_by,
	pq.updated_at,
	pq.updated_by
	{{if .OrderBy}}{{.OrderBy}}{{end -}}
	`

	countQuery := `
	with
	parent_questions as (
		select *
		from survey_questions as sq
		where sq.parent_question_id is null
	)
	select count(*)
	from parent_questions as pq
	`

	if filter.IsStatic != nil {
		composedFilter = append(composedFilter, `pq.is_static = @isStatic`)
		filterArgs["isStatic"] = *filter.IsStatic
	}

	if filter.Question != nil {
		composedFilter = append(composedFilter, `pq.question ilike @question`)
		filterArgs["question"] = fmt.Sprintf("%%%s%%", *filter.Question)
	}

	if filter.Type != nil {
		composedFilter = append(composedFilter, `pq.type = any (@type)`)
		filterArgs["type"] = filter.Type
	}

	if filter.Category != nil {
		composedFilter = append(composedFilter, `pq.category = any (@category)`)
		filterArgs["category"] = filter.Category
	}

	whereClause := utils.GetComposedWhereClause(composedFilter)
	if whereClause != nil {
		conditionalTmpl["Where"] = *whereClause
		countQuery += fmt.Sprintf("%s", *whereClause)
	}

	if filter.SortColumn != "" && filter.SortOrder != "" {
		var column string

		// Check domain file on oneof struct tag.
		switch filter.SortColumn {
		case "mobile": // Check seeder/seed/survey_question_data.go.
			column = `
			array_position(
				array[
					'1f04ea29-b556-4793-80ca-f0314f49917d',
					'959ac8b0-b1a7-4da6-beaf-70063c83fc53',
					'94e22537-8ff3-4702-ac26-87c0a60139cb',
					'4864e913-0c7f-458f-9b19-dd091ccb254e',
					'2097dda1-544a-4d7a-89c6-6314bcefa592'
				]::uuid[],
				pq.id
			)
			`
		default:
			column = fmt.Sprintf("pq.%s", filter.SortColumn)
		}

		conditionalTmpl["OrderBy"] = fmt.Sprintf(
			"ORDER BY %s %s\n",
			column,
			filter.SortOrder,
		)
	}

	baseTmpl := template.Must(template.New("baseQuery").Parse(baseQueryTmpl))
	if err := baseTmpl.Execute(baseQueryBuilder, conditionalTmpl); err != nil {
		return nil, 0, fmt.Errorf("Error build base query: %v", err)
	}

	baseQuery := baseQueryBuilder.String()

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery, filterArgs).Scan(&totalData)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get total survey")
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery, filterArgs)
	defer rows.Close()

	if err != nil {
		log.Error().Err(err).Msg("Failed to query get many survey")
		return nil, 0, err
	}

	results, err := pgx.CollectRows(
		rows,
		pgx.RowToStructByName[domain.SurveyResponseNested],
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to collect get many survey results")
		return nil, 0, err
	}

	return results, totalData, nil
}
