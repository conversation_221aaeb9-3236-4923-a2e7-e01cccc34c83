package postgres

import (
	"api/domain"
	"api/utils"
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

type jobQueueRepository struct {
	db *pgxpool.Pool
}

func NewJobQueueRepository(db *pgxpool.Pool) *jobQueueRepository {
	return &jobQueueRepository{db}
}

func (r *jobQueueRepository) CreateJobQueue(
	ctx context.Context,
	uID string,
	j *domain.FaceAgingJobQueue,
) (*domain.FaceAgingJobQueue, error) {
	query := `
	INSERT INTO job_queue (
        skin_analyze_id,
		payload,
		created_by
	) VALUES (
		@skin_analyze_id,
		@payload,
		@created_by
	) RETURNING *`

	args := pgx.StrictNamedArgs{
		"skin_analyze_id": j.<PERSON>nalyze<PERSON>,
		"payload":         j.Payload,
		"created_by":      uID,
	}

	rows, err := r.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create job queue")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.FaceAgingJobQueue])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect job")
		customErr := utils.CustomPostgresErr(err)
		return nil, customErr
	}

	return result, nil
}

func (r *jobQueueRepository) GetJobQueueByID(
	ctx context.Context,
	id string,
) (*domain.FaceAgingJobQueue, error) {
	query := `
    SELECT
        *
    FROM job_queue
    WHERE id = @id
    `
	args := pgx.StrictNamedArgs{
		"id": id,
	}

	rows, err := r.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get job queue by id")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.FaceAgingJobQueue])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect job")
		customErr := utils.CustomPostgresErr(err)
		return nil, customErr
	}

	return result, nil
}

func (r *jobQueueRepository) ProcessNewJobQueue(
	ctx context.Context,
) (*domain.FaceAgingJobQueue, error) {
	query := `
    UPDATE job_queue
    SET status = 'processing'
    WHERE id = (
        SELECT id
        FROM job_queue
        WHERE status = @status AND retry_count < 3
        ORDER BY created_at
        FOR UPDATE SKIP LOCKED
        LIMIT 1
    )
    RETURNING *
    `
	args := pgx.StrictNamedArgs{
		"status": domain.JobQueueStatusQueued,
	}

	rows, err := r.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to process job queue")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.FaceAgingJobQueue])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect job")
		customErr := utils.CustomPostgresErr(err)
		return nil, customErr
	}

	return result, nil
}

func (r *jobQueueRepository) UpdateJobQueueByID(
	ctx context.Context,
	id string,
	updateJob *domain.FaceAgingJobQueue,
) (*domain.FaceAgingJobQueue, error) {
	query := `
    UPDATE job_queue
    SET
        status = @status,
        payload = @payload,
        result = @result,
        error_message = @error_message,
        retry_count = @retry_count,
        updated_at = (EXTRACT(epoch FROM now()) * 1000::numeric)
    WHERE id = @id
    RETURNING *
    `
	args := pgx.StrictNamedArgs{
		"id":            id,
		"status":        updateJob.Status,
		"payload":       updateJob.Payload,
		"result":        updateJob.Result,
		"error_message": updateJob.ErrorMessage,
		"retry_count":   updateJob.RetryCount,
	}

	rows, err := r.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to update job queue")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.FaceAgingJobQueue])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect job")
		customErr := utils.CustomPostgresErr(err)
		return nil, customErr
	}

	return result, nil
}

func (r *jobQueueRepository) DeleteJobQueueByID(
	ctx context.Context,
	id string,
) (*domain.FaceAgingJobQueue, error) {
	query := `
    DELETE FROM job_queue
    WHERE id = @id
    RETURNING *
    `
	args := pgx.StrictNamedArgs{
		"id": id,
	}

	rows, err := r.db.Query(ctx, query, args)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete job queue")
		return nil, err
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[domain.FaceAgingJobQueue])
	if err != nil {
		log.Error().Err(err).Msg("Failed to collect job")
		customErr := utils.CustomPostgresErr(err)
		return nil, customErr
	}

	return result, nil
}

func (r *jobQueueRepository) GetProcessingJobQueueCount(
	ctx context.Context,
) (*int, error) {
	query := `
    SELECT COUNT(*) FROM job_queue
    WHERE status = @status
    `
	args := pgx.StrictNamedArgs{
		"status": domain.JobQueueStatusProcessing,
	}

	var count int
	err := r.db.QueryRow(ctx, query, args).Scan(&count)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete job queue")
		return nil, err
	}

	return &count, nil
}
