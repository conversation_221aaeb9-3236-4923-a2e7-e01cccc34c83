package postgres

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"api/domain"
	"api/utils"
)

type treatmentIntervalRepository struct {
	db *pgxpool.Pool
}

func NewTreatmentIntervalRepository(
	db *pgxpool.Pool,
) *treatmentIntervalRepository {
	return &treatmentIntervalRepository{db}
}

func (repo *treatmentIntervalRepository) Create(
	ctx context.Context,
	data *domain.TreatmentInterval,
) error {
	duplicateQuery := `
	SELECT
	id,
	days,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_intervals
	WHERE days = $1
	`

	err := repo.db.QueryRow(
		ctx,
		duplicateQuery,
		data.Days,
	).
		Scan(
			&data.ID,
			&data.Days,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil && err != pgx.ErrNoRows {
		return err
	}

	if data.ID != "" {
		return nil
	}

	insertQuery := `
	INSERT INTO treatment_intervals (days)
	VALUES ($1)
	RETURNING
	id,
	days,
	created_at,
	created_by,
	updated_at,
	updated_by
	`

	err = repo.db.QueryRow(
		ctx,
		insertQuery,
		data.Days,
	).
		Scan(
			&data.ID,
			&data.Days,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil {
		return err
	}

	return nil
}

func (repo *treatmentIntervalRepository) GetByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentInterval, error) {
	query := `
	SELECT
	id,
	days,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_intervals
	WHERE id = $1
	`

	var data domain.TreatmentInterval

	err := repo.db.QueryRow(
		ctx,
		query,
		&id,
	).
		Scan(
			&data.ID,
			&data.Days,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, err
	}

	return &data, nil
}

func (repo *treatmentIntervalRepository) UpdateByID(
	ctx context.Context,
	existingData *domain.TreatmentInterval,
	newData *domain.TreatmentInterval,
) error {
	query := `
	UPDATE treatment_intervals
	SET
	days = $1,
	updated_at = extract(epoch FROM now()) * 1000
	WHERE id = $2
	RETURNING
	id,
	days,
	created_at,
	created_by,
	updated_at,
	updated_by
	`

	err := repo.db.QueryRow(
		ctx,
		query,
		newData.Days,
		newData.ID,
	).
		Scan(
			&existingData.ID,
			&existingData.Days,
			&existingData.CreatedAt,
			&existingData.CreatedBy,
			&existingData.UpdatedAt,
			&existingData.UpdatedBy,
		)

	if err != nil {
		return err
	}

	return nil
}

func (repo *treatmentIntervalRepository) DeleteByID(
	ctx context.Context,
	id *string,
) (*domain.TreatmentInterval, error) {
	query := `
	DELETE
	FROM treatment_intervals
	WHERE id = $1
	RETURNING
	id,
	days,
	created_at,
	created_by,
	updated_at,
	updated_by
	`

	var data domain.TreatmentInterval

	err := repo.db.QueryRow(
		ctx,
		query,
		&id,
	).
		Scan(
			&data.ID,
			&data.Days,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}

		return nil, utils.CustomPostgresErr(err)
	}

	return &data, nil
}

func (repo *treatmentIntervalRepository) GetMany(
	ctx context.Context,
	filter *domain.TreatmentIntervalFilter,
) ([]domain.TreatmentInterval, int, error) {
	baseQuery := `
	SELECT
	id,
	days,
	created_at,
	created_by,
	updated_at,
	updated_by
	FROM treatment_intervals
	`

	countQuery := `SELECT count(*) FROM treatment_intervals`

	paginationClause := filter.Pagination.GetPaginationQuery()
	if paginationClause != nil {
		baseQuery += fmt.Sprintf("%s", *paginationClause)
	}

	var totalData int

	err := repo.db.QueryRow(ctx, countQuery).Scan(&totalData)

	if err != nil {
		return nil, 0, err
	}

	rows, err := repo.db.Query(ctx, baseQuery)
	defer rows.Close()

	if err != nil {
		return nil, 0, err
	}

	var results []domain.TreatmentInterval

	for rows.Next() {
		var data domain.TreatmentInterval

		err = rows.Scan(
			&data.ID,
			&data.Days,
			&data.CreatedAt,
			&data.CreatedBy,
			&data.UpdatedAt,
			&data.UpdatedBy,
		)

		if err != nil {
			return nil, 0, err
		}

		results = append(results, data)
	}

	return results, totalData, nil
}
