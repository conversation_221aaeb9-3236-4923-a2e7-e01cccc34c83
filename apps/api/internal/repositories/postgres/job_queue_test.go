package postgres_test

import (
	"api/domain"
	"api/internal/repositories/postgres"
	"api/utils"
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/go-faker/faker/v4"
)

func TestJobQueueRepository(t *testing.T) {
	dbPool := utils.GetTestDBPool(t)
	ctx := context.Background()

	password := "test-password"
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
	user := &domain.User{
		Name:        "Test User",
		Email:       email,
		Role:        domain.Admin,
		PhoneNumber: faker.Phonenumber(),
		Password:    &password,
	}

	user, err := createTestUser(t, dbPool, user)
	require.NoError(t, err, "Error in creating user")

	// TODO(Ravi): Create a mock skin analyze
	dummySA := domain.SkinAnalyze{}
	err = faker.FakeData(&dummySA)
	require.NoError(t, err)
	dummySA.OperatorID = user.ID
	createSA, err := createTestSkinAnalyze(t, dbPool, &dummySA)
	require.NoError(t, err)

	t.Cleanup(func() {
		cleanupSkinAnalyze := `
        DELETE
        FROM skin_analyzes
        WHERE id = @id`
		args := pgx.NamedArgs{"id": createSA.ID}
		err = utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupSkinAnalyze,
			args,
		)
		require.NoErrorf(t, err, "Error cleanup: %v", err)

		cleanupUser := `
        delete
        from users
        where id = @id
        `
		args = pgx.NamedArgs{"id": user.ID}
		err = utils.CleanupTestDummyData(
			ctx,
			dbPool,
			cleanupUser,
			args,
		)
		require.NoErrorf(t, err, "Error cleanup: %v", err)

		dbPool.Close()
	})

	repo := postgres.NewJobQueueRepository(dbPool)

	t.Run("Test CRUD JobQueue", func(t *testing.T) {
		var createdJob domain.FaceAgingJobQueue

		t.Run("Test Create Job Queue", func(t *testing.T) {
			job := domain.FaceAgingJobQueue{
				SkinAnalyzeID: createSA.ID,
				Payload: domain.FaceAgingConcernMLRequest{
					ImagePath: "path/to/image.jpg",
					MaskPath:  nil,
					Concerns: []domain.FaceAgingConcernDetailMLRequest{
						{
							Concern: domain.FaceAgingConcernWrinkle,
							Areas:   []domain.FaceAgingArea{domain.FaceAgingAreaUpper, domain.FaceAgingAreaMid},
						},
						{
							Concern: domain.FaceAgingConcernPore,
							Areas:   []domain.FaceAgingArea{domain.FaceAgingAreaLower},
						},
					},
				},
			}

			jobCreated, err := repo.CreateJobQueue(ctx, user.ID, &job)
			require.NoError(t, err, "Failed to create job")
			require.NotNil(t, jobCreated, "Job should not be nil")

			assert.NotEmpty(t, jobCreated.ID, "ID should not be empty")
			assert.Equal(t, domain.JobQueueStatusQueued, jobCreated.Status)
			assert.Equal(t, job.Payload, jobCreated.Payload)
			assert.Equal(t, createSA.ID, jobCreated.SkinAnalyzeID)

			createdJob = *jobCreated
		})

		t.Run("Test Get Job Queue by ID", func(t *testing.T) {
			job, err := repo.GetJobQueueByID(ctx, createdJob.ID)
			require.NoError(t, err, "Failed to get job by ID")
			require.NotNil(t, job, "Job should not be nil")
			require.NotEmpty(t, job.ID, "ID should not be empty")
			assert.Equal(t, domain.JobQueueStatusQueued, createdJob.Status)
		})

		t.Run("Test Process New Job", func(t *testing.T) {
			processedJob, err := repo.ProcessNewJobQueue(ctx)
			require.NoError(t, err, "Failed to process new job")
			require.NotNil(t, processedJob, "Job should not be nil")
			assert.Equal(t, createdJob.ID, processedJob.ID)
			assert.Equal(t, domain.JobQueueStatusProcessing, processedJob.Status)

			createdJob = *processedJob
		})

		t.Run("Test Count Processed Job", func(t *testing.T) {
			count, err := repo.GetProcessingJobQueueCount(ctx)
			require.NoError(t, err, "Failed to process new job")
			require.NotNil(t, count, "Job should not be nil")
			assert.GreaterOrEqual(t, *count, 1)
		})

		t.Run("Test Update Job by ID", func(t *testing.T) {
			updateJob := createdJob
			updateJob.Status = domain.JobQueueStatusQueued
			updatedJob, err := repo.UpdateJobQueueByID(ctx, createdJob.ID, &updateJob)
			require.NoError(t, err, "Failed to update job by id")
			require.NotNil(t, updatedJob, "Job should not be nil")
			assert.Equal(t, createdJob.ID, updatedJob.ID)
			assert.Equal(t, domain.JobQueueStatusQueued, updatedJob.Status)
		})

		t.Run("Test Delete Job by ID", func(t *testing.T) {
			deletedJob, err := repo.DeleteJobQueueByID(ctx, createdJob.ID)
			require.NoError(t, err, "Failed to update job by id")
			require.NotNil(t, deletedJob, "Job should not be nil")
			assert.Equal(t, createdJob.ID, deletedJob.ID)
			assert.Equal(t, domain.JobQueueStatusQueued, deletedJob.Status)
		})

		t.Cleanup(func() {
			cleanupJob := `
            DELETE
            FROM job_queue
            WHERE id = @id
            `
			args := pgx.NamedArgs{
				"id": createdJob.ID,
			}
			err = utils.CleanupTestDummyData(
				ctx,
				dbPool,
				cleanupJob,
				args,
			)
			require.NoError(t, err)
		})
	})
}
