package rest_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestUserSurveyRest(test *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.UserSurveyService)
		handler     = &rest.UserSurveyHandler{mockService}
	)

	e.Validator = utils.NewCustomValidator()

	reqBody := domain.UserSurveyRequest{
		UserID: &utils.DummyID,
		Results: domain.UserSurveyResultJSON{
			{
				ID:       "1",
				Question: "Masalah Kulit apa yang ingin diselesaikan?",
				Answers:  []string{"Wrinkle", "Dark spot", "Acne"},
				Category: domain.Contraindication,
			},
		},
	}

	sharedData := domain.UserSurvey{
		UserID: &utils.DummyID,
		Results: domain.UserSurveyResultJSON{
			{
				ID:       "1",
				Question: "Masalah Kulit apa yang ingin diselesaikan?",
				Answers:  []string{"Wrinkle", "Dark spot", "Acne"},
				Category: domain.Contraindication,
			},
		},
	}

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(test, err)

	test.Run("Create API", func(t *testing.T) {
		svc := mockService.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.UserSurveyRequest"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/user-survey",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusCreated, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Error Create service")).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/user-survey",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPost,
				"/user-survey",
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	test.Run("GetByID API", func(t *testing.T) {
		svc := mockService.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/user-survey/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/user-survey/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("GetMany API", func(t *testing.T) {
		svc := mockService.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.UserSurveyFilter"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(
					[]domain.UserSurvey{sharedData},
					1,
					nil,
				).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/user-survey?page=1&page_size=10",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, 0, fmt.Errorf("Error GetMany service")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/user-survey?page=1&page_size=10",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})
	})
}
