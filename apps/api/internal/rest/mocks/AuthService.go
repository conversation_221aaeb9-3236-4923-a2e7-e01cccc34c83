// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewAuthService creates a new instance of AuthService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAuthService(t interface {
	mock.TestingT
	Cleanup(func())
}) *AuthService {
	mock := &AuthService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// AuthService is an autogenerated mock type for the AuthService type
type AuthService struct {
	mock.Mock
}

type AuthService_Expecter struct {
	mock *mock.Mock
}

func (_m *AuthService) EXPECT() *AuthService_Expecter {
	return &AuthService_Expecter{mock: &_m.Mock}
}

// GenerateToken provides a mock function for the type AuthService
func (_mock *AuthService) GenerateToken(ctx context.Context, request *domain.LoginRequest) (*string, *string, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for GenerateToken")
	}

	var r0 *string
	var r1 *string
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.LoginRequest) (*string, *string, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.LoginRequest) *string); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.LoginRequest) *string); ok {
		r1 = returnFunc(ctx, request)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*string)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.LoginRequest) error); ok {
		r2 = returnFunc(ctx, request)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// AuthService_GenerateToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateToken'
type AuthService_GenerateToken_Call struct {
	*mock.Call
}

// GenerateToken is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *AuthService_Expecter) GenerateToken(ctx interface{}, request interface{}) *AuthService_GenerateToken_Call {
	return &AuthService_GenerateToken_Call{Call: _e.mock.On("GenerateToken", ctx, request)}
}

func (_c *AuthService_GenerateToken_Call) Run(run func(ctx context.Context, request *domain.LoginRequest)) *AuthService_GenerateToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.LoginRequest))
	})
	return _c
}

func (_c *AuthService_GenerateToken_Call) Return(s *string, s1 *string, err error) *AuthService_GenerateToken_Call {
	_c.Call.Return(s, s1, err)
	return _c
}

func (_c *AuthService_GenerateToken_Call) RunAndReturn(run func(ctx context.Context, request *domain.LoginRequest) (*string, *string, error)) *AuthService_GenerateToken_Call {
	_c.Call.Return(run)
	return _c
}

// RefreshToken provides a mock function for the type AuthService
func (_mock *AuthService) RefreshToken(ctx context.Context, request *domain.RefreshRequest) (*string, *string, error) {
	ret := _mock.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for RefreshToken")
	}

	var r0 *string
	var r1 *string
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.RefreshRequest) (*string, *string, error)); ok {
		return returnFunc(ctx, request)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *domain.RefreshRequest) *string); ok {
		r0 = returnFunc(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *domain.RefreshRequest) *string); ok {
		r1 = returnFunc(ctx, request)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*string)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *domain.RefreshRequest) error); ok {
		r2 = returnFunc(ctx, request)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// AuthService_RefreshToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefreshToken'
type AuthService_RefreshToken_Call struct {
	*mock.Call
}

// RefreshToken is a helper method to define mock.On call
//   - ctx
//   - request
func (_e *AuthService_Expecter) RefreshToken(ctx interface{}, request interface{}) *AuthService_RefreshToken_Call {
	return &AuthService_RefreshToken_Call{Call: _e.mock.On("RefreshToken", ctx, request)}
}

func (_c *AuthService_RefreshToken_Call) Run(run func(ctx context.Context, request *domain.RefreshRequest)) *AuthService_RefreshToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*domain.RefreshRequest))
	})
	return _c
}

func (_c *AuthService_RefreshToken_Call) Return(s *string, s1 *string, err error) *AuthService_RefreshToken_Call {
	_c.Call.Return(s, s1, err)
	return _c
}

func (_c *AuthService_RefreshToken_Call) RunAndReturn(run func(ctx context.Context, request *domain.RefreshRequest) (*string, *string, error)) *AuthService_RefreshToken_Call {
	_c.Call.Return(run)
	return _c
}
