// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewFaceAgingService creates a new instance of FaceAgingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFaceAgingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *FaceAgingService {
	mock := &FaceAgingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// FaceAgingService is an autogenerated mock type for the FaceAgingService type
type FaceAgingService struct {
	mock.Mock
}

type FaceAgingService_Expecter struct {
	mock *mock.Mock
}

func (_m *FaceAgingService) EXPECT() *FaceAgingService_Expecter {
	return &FaceAgingService_Expecter{mock: &_m.<PERSON>ck}
}

// FaceAgingJobCallback provides a mock function for the type FaceAgingService
func (_mock *FaceAgingService) FaceAgingJobCallback(ctx context.Context, id string, data domain.FaceAgingConcernMLResponse) error {
	ret := _mock.Called(ctx, id, data)

	if len(ret) == 0 {
		panic("no return value specified for FaceAgingJobCallback")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, domain.FaceAgingConcernMLResponse) error); ok {
		r0 = returnFunc(ctx, id, data)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// FaceAgingService_FaceAgingJobCallback_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FaceAgingJobCallback'
type FaceAgingService_FaceAgingJobCallback_Call struct {
	*mock.Call
}

// FaceAgingJobCallback is a helper method to define mock.On call
//   - ctx
//   - id
//   - data
func (_e *FaceAgingService_Expecter) FaceAgingJobCallback(ctx interface{}, id interface{}, data interface{}) *FaceAgingService_FaceAgingJobCallback_Call {
	return &FaceAgingService_FaceAgingJobCallback_Call{Call: _e.mock.On("FaceAgingJobCallback", ctx, id, data)}
}

func (_c *FaceAgingService_FaceAgingJobCallback_Call) Run(run func(ctx context.Context, id string, data domain.FaceAgingConcernMLResponse)) *FaceAgingService_FaceAgingJobCallback_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(domain.FaceAgingConcernMLResponse))
	})
	return _c
}

func (_c *FaceAgingService_FaceAgingJobCallback_Call) Return(err error) *FaceAgingService_FaceAgingJobCallback_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *FaceAgingService_FaceAgingJobCallback_Call) RunAndReturn(run func(ctx context.Context, id string, data domain.FaceAgingConcernMLResponse) error) *FaceAgingService_FaceAgingJobCallback_Call {
	_c.Call.Return(run)
	return _c
}

// FaceAgingJobStatusByID provides a mock function for the type FaceAgingService
func (_mock *FaceAgingService) FaceAgingJobStatusByID(ctx context.Context, id string) (*domain.FaceAgingJobQueue, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FaceAgingJobStatusByID")
	}

	var r0 *domain.FaceAgingJobQueue
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.FaceAgingJobQueue, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.FaceAgingJobQueue); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingJobQueue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// FaceAgingService_FaceAgingJobStatusByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FaceAgingJobStatusByID'
type FaceAgingService_FaceAgingJobStatusByID_Call struct {
	*mock.Call
}

// FaceAgingJobStatusByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *FaceAgingService_Expecter) FaceAgingJobStatusByID(ctx interface{}, id interface{}) *FaceAgingService_FaceAgingJobStatusByID_Call {
	return &FaceAgingService_FaceAgingJobStatusByID_Call{Call: _e.mock.On("FaceAgingJobStatusByID", ctx, id)}
}

func (_c *FaceAgingService_FaceAgingJobStatusByID_Call) Run(run func(ctx context.Context, id string)) *FaceAgingService_FaceAgingJobStatusByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *FaceAgingService_FaceAgingJobStatusByID_Call) Return(faceAgingJobQueue *domain.FaceAgingJobQueue, err error) *FaceAgingService_FaceAgingJobStatusByID_Call {
	_c.Call.Return(faceAgingJobQueue, err)
	return _c
}

func (_c *FaceAgingService_FaceAgingJobStatusByID_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.FaceAgingJobQueue, error)) *FaceAgingService_FaceAgingJobStatusByID_Call {
	_c.Call.Return(run)
	return _c
}

// FaceAgingWithConcern provides a mock function for the type FaceAgingService
func (_mock *FaceAgingService) FaceAgingWithConcern(ctx context.Context, id string, data *domain.FaceAgingConcernRequest) (*domain.FaceAgingJobQueue, error) {
	ret := _mock.Called(ctx, id, data)

	if len(ret) == 0 {
		panic("no return value specified for FaceAgingWithConcern")
	}

	var r0 *domain.FaceAgingJobQueue
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingConcernRequest) (*domain.FaceAgingJobQueue, error)); ok {
		return returnFunc(ctx, id, data)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingConcernRequest) *domain.FaceAgingJobQueue); ok {
		r0 = returnFunc(ctx, id, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingJobQueue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *domain.FaceAgingConcernRequest) error); ok {
		r1 = returnFunc(ctx, id, data)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// FaceAgingService_FaceAgingWithConcern_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FaceAgingWithConcern'
type FaceAgingService_FaceAgingWithConcern_Call struct {
	*mock.Call
}

// FaceAgingWithConcern is a helper method to define mock.On call
//   - ctx
//   - id
//   - data
func (_e *FaceAgingService_Expecter) FaceAgingWithConcern(ctx interface{}, id interface{}, data interface{}) *FaceAgingService_FaceAgingWithConcern_Call {
	return &FaceAgingService_FaceAgingWithConcern_Call{Call: _e.mock.On("FaceAgingWithConcern", ctx, id, data)}
}

func (_c *FaceAgingService_FaceAgingWithConcern_Call) Run(run func(ctx context.Context, id string, data *domain.FaceAgingConcernRequest)) *FaceAgingService_FaceAgingWithConcern_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*domain.FaceAgingConcernRequest))
	})
	return _c
}

func (_c *FaceAgingService_FaceAgingWithConcern_Call) Return(faceAgingJobQueue *domain.FaceAgingJobQueue, err error) *FaceAgingService_FaceAgingWithConcern_Call {
	_c.Call.Return(faceAgingJobQueue, err)
	return _c
}

func (_c *FaceAgingService_FaceAgingWithConcern_Call) RunAndReturn(run func(ctx context.Context, id string, data *domain.FaceAgingConcernRequest) (*domain.FaceAgingJobQueue, error)) *FaceAgingService_FaceAgingWithConcern_Call {
	_c.Call.Return(run)
	return _c
}
