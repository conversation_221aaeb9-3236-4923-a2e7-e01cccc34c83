// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMediaService creates a new instance of MediaService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMediaService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MediaService {
	mock := &MediaService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MediaService is an autogenerated mock type for the MediaService type
type MediaService struct {
	mock.Mock
}

type MediaService_Expecter struct {
	mock *mock.Mock
}

func (_m *MediaService) EXPECT() *MediaService_Expecter {
	return &MediaService_Expecter{mock: &_m.Mock}
}

// UploadS3 provides a mock function for the type MediaService
func (_mock *MediaService) UploadS3(ctx context.Context, filename string) (*string, *string, error) {
	ret := _mock.Called(ctx, filename)

	if len(ret) == 0 {
		panic("no return value specified for UploadS3")
	}

	var r0 *string
	var r1 *string
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*string, *string, error)); ok {
		return returnFunc(ctx, filename)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *string); ok {
		r0 = returnFunc(ctx, filename)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) *string); ok {
		r1 = returnFunc(ctx, filename)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*string)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, string) error); ok {
		r2 = returnFunc(ctx, filename)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MediaService_UploadS3_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadS3'
type MediaService_UploadS3_Call struct {
	*mock.Call
}

// UploadS3 is a helper method to define mock.On call
//   - ctx
//   - filename
func (_e *MediaService_Expecter) UploadS3(ctx interface{}, filename interface{}) *MediaService_UploadS3_Call {
	return &MediaService_UploadS3_Call{Call: _e.mock.On("UploadS3", ctx, filename)}
}

func (_c *MediaService_UploadS3_Call) Run(run func(ctx context.Context, filename string)) *MediaService_UploadS3_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MediaService_UploadS3_Call) Return(s *string, s1 *string, err error) *MediaService_UploadS3_Call {
	_c.Call.Return(s, s1, err)
	return _c
}

func (_c *MediaService_UploadS3_Call) RunAndReturn(run func(ctx context.Context, filename string) (*string, *string, error)) *MediaService_UploadS3_Call {
	_c.Call.Return(run)
	return _c
}
