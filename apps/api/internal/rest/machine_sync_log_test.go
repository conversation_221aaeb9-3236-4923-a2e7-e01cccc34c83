package rest_test

import (
	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestMachineSyncLogHandler(t *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.MachineSyncLogService)
		handler     = &rest.MachineSyncLogHandler{mockService}
	)
	e.Validator = utils.NewCustomValidator()

	log := &domain.MachineSyncLog{
		Data: json.RawMessage(`{
            "folder_name": "john doe",
            "images": ["rgb.jpg", "pl_texture.jpg"],
            "pdf": "customer_report.pdf",
            "status": "success",
            "message": null
        }`),
	}

	reqBody := domain.CreateMachineSyncLog{
		Data: json.RawMessage(`{
            "folder_name": "john doe",
            "images": ["rgb.jpg", "pl_texture.jpg"],
            "pdf": "customer_report.pdf",
            "status": "success",
            "message": null
        }`),
	}

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(t, err)

	t.Run("Create machine sync log", func(t *testing.T) {
		t.Run("Success", func(t *testing.T) {
			mockService.On(
				"CreateMachineSyncLog",
				mock.Anything,
				mock.AnythingOfType("*domain.MachineSyncLog"),
			).
				Return(log, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/machine-sync-log",
				bytes.NewReader(reqBytes),
			)
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.CreateMachineSyncLog(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusCreated, rec.Code)
			mockService.AssertExpectations(t)
		})
	})

	t.Run("Get many machine sync log", func(t *testing.T) {
		mockService.On(
			"GetManyMachineSyncLog",
			mock.Anything,
			mock.AnythingOfType("*domain.MachineSyncLogFilter"),
		).
			Return([]*domain.MachineSyncLog{log}, &domain.MachineSyncLogStatCount{}, 1, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodGet,
			"/machine-sync-log?page=1&page_size=10",
			nil,
		)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/machine-sync-log")
		c.SetParamNames("page", "page_size")
		c.SetParamValues("1", "10")

		err := handler.GetManyMachineSyncLogs(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var response domain.PaginationStatsResponse[domain.MachineSyncLog, domain.MachineSyncLogStatCount]
		err = json.NewDecoder(rec.Body).Decode(&response)
		assert.NoError(t, err)
		assert.Equal(t, 1, response.Data.TotalData)
		assert.Equal(t, 1, len(response.Data.Content))
		mockService.AssertExpectations(t)
	})
}
