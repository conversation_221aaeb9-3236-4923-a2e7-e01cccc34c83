package rest_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestParameterSkinEvaluationRest(t *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.ParameterSkinEvaluationService)
		handler     = &rest.ParameterSkinEvaluationHandler{mockService}
	)

	e.Validator = utils.NewCustomValidator()

	var (
		reqBody = domain.ParameterSkinEvaluationRequest{
			Name:       "Test Parameter",
			LowerPoint: 0,
			UpperPoint: 100,
		}

		sharedData = domain.ParameterSkinEvaluation{
			Name:       reqBody.Name,
			LowerPoint: reqBody.LowerPoint,
			UpperPoint: reqBody.UpperPoint,
		}
	)

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(t, err)

	t.Run("GetByID API", func(t *testing.T) {
		svc := mockService.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/parameter-skin-evaluation/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/parameter-skin-evaluation/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	t.Run("UpdateByID API", func(t *testing.T) {
		svc := mockService.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
			mock.AnythingOfType("*domain.ParameterSkinEvaluationRequest"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/parameter-skin-evaluation/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(
					nil,
					fmt.Errorf("Data is not found"),
				).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/parameter-skin-evaluation/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	t.Run("GetMany API", func(t *testing.T) {
		svc := mockService.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.ParameterSkinEvaluationFilter"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(
					[]domain.ParameterSkinEvaluation{sharedData},
					1,
					nil,
				).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/parameter-skin-evaluation?page=1&page_size=10",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Success with filter category IDs", func(t *testing.T) {
			svc.
				Return(
					[]domain.ParameterSkinEvaluation{sharedData},
					1,
					nil,
				).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/parameter-skin-evaluation?page=1&page_size=10&category_ids=3fa85f64-5717-4562-b3fc-2c963f66afa6&category_ids=3fa85f64-5717-4562-b3fc-2c963f66afa6",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Success with filter types", func(t *testing.T) {
			svc.
				Return(
					[]domain.ParameterSkinEvaluation{sharedData},
					1,
					nil,
				).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/parameter-skin-evaluation?page=1&page_size=10&types=treatment&types=product",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, 0, fmt.Errorf("Error GetMany service")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/parameter-skin-evaluation?page=1&page_size=10",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})
	})
}
