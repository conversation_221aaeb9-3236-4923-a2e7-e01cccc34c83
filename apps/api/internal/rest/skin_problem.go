package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type SkinProblemService interface {
	Create(
		ctx context.Context,
		request *domain.SkinProblemRequest,
	) (*domain.SkinProblem, []domain.SkinProblemIndication, error)
	GetByID(ctx context.Context, id *string) (*domain.SkinProblemResponse, error)
	UpdateByID(
		ctx context.Context,
		id *string,
		request *domain.SkinProblemRequest,
	) (*domain.SkinProblem, []domain.SkinProblemIndication, error)
	DeleteByID(ctx context.Context, id *string) (*domain.SkinProblem, error)
	GetMany(
		ctx context.Context,
		filter *domain.SkinProblemFilter,
	) ([]domain.SkinProblemResponse, int, error)
}

type SkinProblemHandler struct {
	Service SkinProblemService
}

func NewSkinProblemHandler(
	e *echo.Group,
	service SkinProblemService,
) {
	handler := &SkinProblemHandler{service}

	e.POST("/skin-problem", handler.Create)
	e.GET("/skin-problem/:id", handler.GetByID)
	e.PUT("/skin-problem/:id", handler.UpdateByID)
	e.DELETE("/skin-problem/:id", handler.DeleteByID)
	e.GET("/skin-problem", handler.GetMany)
}

func prepareSkinProblemCreateAndUpdateResponse(
	skinProblemData *domain.SkinProblem,
	skinProblemIndicationData []domain.SkinProblemIndication,
) domain.SkinProblemResponse {
	response := domain.SkinProblemResponse{
		ID:                     skinProblemData.ID,
		Name:                   skinProblemData.Name,
		SkinProblemIndications: skinProblemIndicationData,
		Timestamp:              skinProblemData.Timestamp,
	}

	return response
}

// @router /api/v1/skin-problem [post]
// @tags skin-problem
// @summary Create new skin problem (for treatment-product)
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.SkinProblemRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.SkinProblemResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemHandler) Create(c echo.Context) (err error) {
	var request *domain.SkinProblemRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	skinProblemData, skinProblemIndicationData, err := handler.Service.Create(
		ctx,
		request,
	)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Create data failed"),
			},
		)
	}

	response := prepareSkinProblemCreateAndUpdateResponse(
		skinProblemData,
		skinProblemIndicationData,
	)

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.SkinProblemResponse]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    response,
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/skin-problem/{id} [get]
// @tags skin-problem
// @summary Get skin problem detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Skin Problem ID"
// @success 200 {object} domain.SingleResponse[domain.SkinProblemResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	response, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinProblemResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *response,
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/skin-problem/{id} [put]
// @tags skin-problem
// @summary Update skin problem detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Skin Problem ID"
// @param request body domain.SkinProblemRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.SkinProblemResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	var request *domain.SkinProblemRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding request: " + err.Error(),
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	skinProblemData, skinProblemIndicationData, err := handler.Service.UpdateByID(
		ctx,
		&id,
		request,
	)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Update data failed"),
			},
		)
	}

	response := prepareSkinProblemCreateAndUpdateResponse(
		skinProblemData,
		skinProblemIndicationData,
	)

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinProblemResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    response,
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/skin-problem/{id} [delete]
// @tags skin-problem
// @summary Delete skin problem detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Skin Problem ID"
// @success 200 {object} domain.SingleResponse[domain.SkinProblem]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinProblemHandler) DeleteByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.DeleteByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Delete data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SkinProblem]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data deleted successfully",
		},
	)
}

// @router /api/v1/skin-problem [get]
// @tags skin-problem
// @summary Get many skin problem detail
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.SkinProblemFilter true "Skin Problem Filter"
// @success 200 {object} domain.PaginationResponse[domain.SkinProblemResponse]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *SkinProblemHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.SkinProblemFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding filter: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	responseData := domain.PaginationData[domain.SkinProblemResponse]{
		Content:    data,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.SkinProblemResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    responseData,
			Message: "Get many data successfully",
		},
	)
}
