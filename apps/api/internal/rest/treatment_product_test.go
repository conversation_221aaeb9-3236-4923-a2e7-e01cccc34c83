package rest_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestTreatmentProductRest(test *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.TreatmentProductService)
		handler     = &rest.TreatmentProductHandler{mockService}
	)

	e.Validator = utils.NewCustomValidator()

	var (
		testItemCode              = "UWU69420"
		testNotes                 = "Test notes from doctor"
		mediaUrl                  = "https://example.com/s3-object"
		thumbnailUrl              = "https://example.com/s3-object"
		durationTopRecommendation = time.Now().UnixNano()

		reqBody = domain.TreatmentProductRequest{
			ItemCode:                  testItemCode,
			Name:                      "Test Treatment Product",
			Type:                      domain.TreatmentType,
			Description:               "Dummy data test",
			Price:                     69420,
			CategoryIDs:               []string{utils.DummyID},
			IntervalID:                &utils.DummyID,
			ConcernIDs:                []string{utils.DummyID},
			Notes:                     &testNotes,
			Quantity:                  69,
			MediaUrl:                  &mediaUrl,
			ThumbnailUrl:              &thumbnailUrl,
			IsTopRecommendation:       true,
			DurationTopRecommendation: &durationTopRecommendation,
			SurveyQuestions: []domain.TreatmentProductSurveyQuestionInput{
				{
					SurveyQuestionID: utils.DummyID,
					SelectedAnswer:   1,
				},
			},
		}

		surveyQuestions = []domain.TreatmentProductSurveyQuestion{
			{
				Question: "Apakah hidup anda tenang?",
				Answers: []domain.SurveyAnswer{
					{Title: "Ya"},
					{Title: "Tidak"},
				},
				SelectedAnswer: 1,
				QuestionOrder:  0,
			},
		}

		category = domain.TreatmentCategory{Name: "Test treatment product category"}
		interval = domain.TreatmentInterval{Days: 69420}

		sharedSupplementaryData = domain.TreatmentProductSupplementaryData{
			Category: []domain.TreatmentCategory{category},
			Interval: &interval,
			Concern: []domain.SkinProblem{
				{Name: "test acne"},
				{Name: "test pore"},
				{Name: "test wrinkle"},
			},
			SurveyQuestions: surveyQuestions,
		}

		sharedData = domain.TreatmentProduct{
			ItemCode:     testItemCode,
			Name:         "Test Treatment Product",
			Type:         domain.TreatmentType,
			Description:  "Dummy data test",
			Price:        69420,
			IntervalID:   &utils.DummyID,
			Notes:        reqBody.Notes,
			MediaUrl:     reqBody.MediaUrl,
			ThumbnailUrl: reqBody.ThumbnailUrl,
		}

		sharedResponse = domain.TreatmentProductResponse{
			ItemCode:                          testItemCode,
			Name:                              "Test Treatment Product",
			Type:                              domain.TreatmentType,
			Description:                       "Dummy data test",
			Price:                             69420,
			MediaUrl:                          reqBody.MediaUrl,
			ThumbnailUrl:                      reqBody.ThumbnailUrl,
			Notes:                             reqBody.Notes,
			TreatmentProductSupplementaryData: sharedSupplementaryData,
		}
	)

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(test, err)

	test.Run("Create API", func(t *testing.T) {
		svc := mockService.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, &sharedSupplementaryData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/treatment-product",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusCreated, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, nil, fmt.Errorf("Error Create service")).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/treatment-product",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPost,
				"/treatment-product",
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	test.Run("GetByID API", func(t *testing.T) {
		svc := mockService.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedResponse, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/treatment-product/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/treatment-product/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("UpdateByID API", func(t *testing.T) {
		svc := mockService.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
			mock.AnythingOfType("*domain.TreatmentProductRequest"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, &sharedSupplementaryData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/treatment-product/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(
					nil,
					nil,
					fmt.Errorf("Data is not found"),
				).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/treatment-product/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("DeleteByID API", func(t *testing.T) {
		svc := mockService.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedData, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/treatment-product/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data is not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/treatment-product/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("GetMany API", func(t *testing.T) {
		svc := mockService.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.TreatmentProductFilter"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(
					[]domain.TreatmentProductGetMany{
						{
							TreatmentProduct: sharedData,
							Category:         []domain.TreatmentCategory{category},
						},
					},
					1,
					nil,
				).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/treatment-product?page=1&page_size=10",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Success with filter category IDs", func(t *testing.T) {
			svc.
				Return(
					[]domain.TreatmentProductGetMany{
						{
							TreatmentProduct: sharedData,
							Category:         []domain.TreatmentCategory{category},
						},
					},
					1,
					nil,
				).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/treatment-product?page=1&page_size=10&category_ids=3fa85f64-5717-4562-b3fc-2c963f66afa6&category_ids=3fa85f64-5717-4562-b3fc-2c963f66afa6",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Success with filter types", func(t *testing.T) {
			svc.
				Return(
					[]domain.TreatmentProductGetMany{
						{
							TreatmentProduct: sharedData,
							Category:         []domain.TreatmentCategory{category},
						},
					},
					1,
					nil,
				).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/treatment-product?page=1&page_size=10&types=treatment&types=product",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, 0, fmt.Errorf("Error GetMany service")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/treatment-product?page=1&page_size=10",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})
	})
}
