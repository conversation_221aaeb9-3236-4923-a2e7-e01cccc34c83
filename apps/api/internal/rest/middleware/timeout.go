package middleware

import (
	"context"
	"os"
	"time"

	"github.com/labstack/echo/v4"
)

func SetRequestContextWithTimeout(defaultTimeout time.Duration) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			app_env := os.Getenv("APP_ENV")

			newDeadline := time.Now().Add(defaultTimeout)
			ctx := c.Request().Context()
			ctx = context.WithValue(ctx, "APP_ENV", app_env)

			if deadline, ok := ctx.Deadline(); !ok || newDeadline.Before(deadline) {
				ctx, cancel := context.WithDeadline(ctx, newDeadline)
				defer cancel()
				c.SetRequest(c.Request().WithContext(ctx))
				return next(c)
			}

			cleanCtx := context.WithoutCancel(ctx)
			newCtx, cancel := context.WithDeadline(cleanCtx, newDeadline)
			defer cancel()

			go func() {
				<-ctx.Done()
				if ctx.Err() != context.DeadlineExceeded {
					cancel()
				}
			}()

			c.SetRequest(c.Request().WithContext(newCtx))
			return next(c)
		}
	}
}
