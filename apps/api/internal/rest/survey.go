package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type SurveyService interface {
	Create(ctx context.Context, data *domain.Survey) (*domain.Survey, error)
	CreateNested(
		ctx context.Context,
		data *domain.SurveyRequestNested,
	) (*domain.SurveyResponseNested, error)
	GetByID(ctx context.Context, id *string) (*domain.Survey, error)
	GetByIDNested(
		ctx context.Context,
		id *string,
	) (*domain.SurveyResponseNested, error)
	UpdateByID(ctx context.Context, input *domain.Survey) (*domain.Survey, error)
	UpdateByIDNested(
		ctx context.Context,
		id *string,
		data *domain.SurveyRequestNested,
	) (*domain.SurveyResponseNested, error)
	DeleteByID(ctx context.Context, id *string) (*domain.Survey, error)
	GetMany(
		ctx context.Context,
		filter *domain.SurveyFilter,
	) ([]domain.Survey, int, error)
	GetManyNested(
		ctx context.Context,
		filter *domain.SurveyFilterNested,
	) ([]domain.SurveyResponseNested, int, error)
}

type SurveyHandler struct {
	Service SurveyService
}

func NewSurveyHandler(e *echo.Group, service SurveyService) {
	handler := &SurveyHandler{service}

	e.POST("/survey", handler.Create)
	e.GET("/survey/:id", handler.GetByID)
	e.PUT("/survey/:id", handler.UpdateByID)
	e.DELETE("/survey/:id", handler.DeleteByID)

	e.POST("/survey/nested", handler.CreateNested)
	e.GET("/survey/nested/:id", handler.GetByIDNested)
	e.PUT("/survey/nested/:id", handler.UpdateByIDNested)
}

func NewPublicSurveyHandler(e *echo.Group, service SurveyService) {
	handler := &SurveyHandler{service}

	e.GET("/survey", handler.GetMany)
	e.GET("/survey/nested", handler.GetManyNested)
}

// @router /api/v1/survey [post]
// @tags survey
// @summary Create new survey
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.SurveyRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.SurveyResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SurveyHandler) Create(c echo.Context) (err error) {
	var request *domain.SurveyRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		if strings.Contains(err.Error(), "ParentQuestion") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Parent question id and answer must be both either not empty or empty",
				},
			)
		}

		if strings.Contains(err.Error(), "Category") &&
			strings.Contains(err.Error(), "oneof") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Category should be either informational or contraindication",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	input := request.ToRepo()

	ctx := c.Request().Context()
	data, err := handler.Service.Create(ctx, &input)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Create data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.SurveyResponse]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/survey/nested [post]
// @tags survey
// @summary Create new survey with child questions
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.SurveyRequestNested true "Request body"
// @success 201 {object} domain.SingleResponse[domain.SurveyResponseNested]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SurveyHandler) CreateNested(c echo.Context) (err error) {
	var request *domain.SurveyRequestNested
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		if strings.Contains(err.Error(), "Category") &&
			strings.Contains(err.Error(), "oneof") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Category should be either informational or contraindication",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.CreateNested(ctx, request)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Create data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.SurveyResponseNested]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/survey/{id} [get]
// @tags survey
// @summary Get survey detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Survey ID"
// @success 200 {object} domain.SingleResponse[domain.SurveyResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SurveyHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SurveyResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/survey/nested/{id} [get]
// @tags survey
// @summary Get survey detail by id with child questions
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Survey ID"
// @success 200 {object} domain.SingleResponse[domain.SurveyResponseNested]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SurveyHandler) GetByIDNested(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.GetByIDNested(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SurveyResponseNested]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/survey/{id} [put]
// @tags survey
// @summary Update survey detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Survey ID"
// @param request body domain.SurveyRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.SurveyResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SurveyHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")

	var request *domain.SurveyRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		if strings.Contains(err.Error(), "ParentQuestion") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Parent question id and answer must be both either not empty or empty",
				},
			)
		}

		if strings.Contains(err.Error(), "Category") &&
			strings.Contains(err.Error(), "oneof") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Category should be either informational or contraindication",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	input := request.ToRepo()
	input.ID = id

	ctx := c.Request().Context()
	data, err := handler.Service.UpdateByID(ctx, &input)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Update data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SurveyResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/survey/nested/{id} [put]
// @tags survey
// @summary Update survey detail by id with child questions
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Survey ID"
// @param request body domain.SurveyRequestNested true "Request body"
// @success 200 {object} domain.SingleResponse[domain.SurveyResponseNested]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SurveyHandler) UpdateByIDNested(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	var request *domain.SurveyRequestNested
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		if strings.Contains(err.Error(), "Category") &&
			strings.Contains(err.Error(), "oneof") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Category should be either informational or contraindication",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.UpdateByIDNested(ctx, &id, request)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Update data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SurveyResponseNested]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/survey/{id} [delete]
// @tags survey
// @summary Delete survey detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Survey ID"
// @success 200 {object} domain.SingleResponse[domain.SurveyResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SurveyHandler) DeleteByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.DeleteByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Delete data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.SurveyResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data deleted successfully",
		},
	)
}

// @router /api/v1/survey [get]
// @tags survey
// @summary Get many survey detail
// @accept json
// @produce json
// @param filter query domain.SurveyFilter true "Survey Filter"
// @success 200 {object} domain.PaginationResponse[domain.SurveyResponse]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *SurveyHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.SurveyFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	responseData := domain.PaginationData[domain.SurveyResponse]{
		Content:    []domain.SurveyResponse{},
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	for _, value := range data {
		response := value.ToResponse()
		responseData.Content = append(responseData.Content, response)
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.SurveyResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    responseData,
			Message: "Get many data successfully",
		},
	)
}

// @router /api/v1/survey/nested [get]
// @tags survey
// @summary Get many survey parent detail with child questions
// @accept json
// @produce json
// @param filter query domain.SurveyFilterNested true "Survey Filter"
// @success 200 {object} domain.PaginationResponse[domain.SurveyResponseNested]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *SurveyHandler) GetManyNested(c echo.Context) (err error) {
	filter := new(domain.SurveyFilterNested)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetManyNested(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	responseData := domain.PaginationData[domain.SurveyResponseNested]{
		Content:    data,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.SurveyResponseNested]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    responseData,
			Message: "Get many data successfully",
		},
	)
}
