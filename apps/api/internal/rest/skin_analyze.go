package rest

import (
	"api/domain"
	"api/utils"
	"context"
	"net/http"
	"time"

	internalMiddleware "api/internal/rest/middleware"

	"github.com/labstack/echo/v4"
)

//go:generate mockery
type SkinAnalyzeService interface {
	CreateSkinAnalyze(ctx context.Context, data *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error)
	GetSkinAnalyzeByID(ctx context.Context, id string) (*domain.SkinAnalyze, error)
	GetManySkinAnalyzes(ctx context.Context, filter *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error)
	GetTopConcernsSkinAnalyze(ctx context.Context, id string) ([]string, error)
}

type SkinAnalyzeHandler struct {
	Service SkinAnalyzeService
}

func NewSkinAnalyzeHandler(
	e *echo.Group,
	service SkinAnalyzeService,
) {
	handler := &SkinAnalyzeHandler{
		Service: service,
	}

	e.GET("/skin-analyze/:id", handler.GetSkinAnalyzeByID)
	e.GET("/skin-analyze", handler.GetManySkinAnalyzes)
}

func NewPublicSkinAnalyzeHandler(
	e *echo.Group,
	service SkinAnalyzeService,
) {
	handler := &SkinAnalyzeHandler{
		Service: service,
	}
	e.GET("/skin-analyze/top-concern/:id", handler.GetTopConcernsSkinAnalyze)

	fileRoutes := e.Group("")

	fileRoutes.Use(internalMiddleware.SetRequestContextWithTimeout(45 * time.Second))
	fileRoutes.POST("/skin-analyze/upload", handler.CreateSkinAnalyze)
}

// @router /api/v1/skin-analyze/upload [post]
// @tags skin-analyze
// @summary Upload skin analyze image
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.SkinAnalyzeUploadRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.SkinAnalyze]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinAnalyzeHandler) CreateSkinAnalyze(c echo.Context) (err error) {
	var request *domain.SkinAnalyzeUploadRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	skinAnalyze, err := handler.Service.CreateSkinAnalyze(ctx, request)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  "error",
				Message: utils.GetErrorMessage(ctx, err, "Failed to create skin analyze"),
			},
		)
	}

	return c.JSON(http.StatusCreated, domain.SingleResponse[domain.SkinAnalyze]{
		Code:    http.StatusCreated,
		Status:  "success",
		Message: "Skin analyze created successfully",
		Data:    *skinAnalyze,
	})
}

// @router /api/v1/skin-analyze/{id} [get]
// @tags skin-analyze
// @summary Get skin analyze by ID
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Skin Analyze ID"
// @success 200 {object} domain.SingleResponse[domain.SkinAnalyze]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinAnalyzeHandler) GetSkinAnalyzeByID(c echo.Context) (err error) {
	id := c.Param("id")
	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	skinAnalyze, err := handler.Service.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  "error",
				Message: utils.GetErrorMessage(ctx, err, "Skin analyze not found"),
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[domain.SkinAnalyze]{
		Code:    http.StatusOK,
		Status:  "success",
		Message: "Skin analyze retrieved successfully",
		Data:    *skinAnalyze,
	})
}

// @router /api/v1/skin-analyze [get]
// @tags skin-analyze
// @summary Get many skin analyzes
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.SkinAnalyzeFilter false "Filter parameters"
// @success 200 {object} domain.PaginationResponse[domain.SkinAnalyze]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinAnalyzeHandler) GetManySkinAnalyzes(c echo.Context) (err error) {
	filter := new(domain.SkinAnalyzeFilter)
	err = c.Bind(filter)
	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	skinAnalyzes, total, err := handler.Service.GetManySkinAnalyzes(ctx, filter)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  "error",
				Message: utils.GetErrorMessage(ctx, err, "Failed to get skin analyzes"),
			},
		)
	}

	res := domain.PaginationData[*domain.SkinAnalyze]{
		Content:    skinAnalyzes,
		TotalData:  total,
		TotalPages: filter.GetTotalPages(total),
		Page:       filter.Page,
		PageSize:   filter.PageSize,
	}

	return c.JSON(http.StatusOK, domain.PaginationResponse[*domain.SkinAnalyze]{
		Code:    http.StatusOK,
		Status:  "success",
		Message: "Skin analyzes retrieved successfully",
		Data:    res,
	})
}

// @router /api/v1/skin-analyze/top-concern/{id} [get]
// @tags skin-analyze
// @summary Get top concern skin analyzes
// @accept json
// @produce json
// @param id path string true "Skin Analyze ID"
// @success 200 {object} domain.SingleResponse[[]string]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
func (handler *SkinAnalyzeHandler) GetTopConcernsSkinAnalyze(c echo.Context) (err error) {
	id := c.Param("id")
	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	topConcerns, err := handler.Service.GetTopConcernsSkinAnalyze(ctx, id)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  "error",
				Message: utils.GetErrorMessage(ctx, err, "Top concerns skin analyze not found"),
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[[]string]{
		Code:    http.StatusOK,
		Status:  "success",
		Message: "Top concerns skin analyze retrieved successfully",
		Data:    topConcerns,
	})
}
