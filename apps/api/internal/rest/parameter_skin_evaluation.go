package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type ParameterSkinEvaluationService interface {
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.ParameterSkinEvaluation, error)
	UpdateByID(
		ctx context.Context,
		id *string,
		request *domain.ParameterSkinEvaluationRequest,
	) (*domain.ParameterSkinEvaluation, error)
	GetMany(
		ctx context.Context,
		filter *domain.ParameterSkinEvaluationFilter,
	) ([]domain.ParameterSkinEvaluation, int, error)
}

type ParameterSkinEvaluationHandler struct {
	Service ParameterSkinEvaluationService
}

func NewParameterSkinEvaluationHandler(
	e *echo.Group,
	service ParameterSkinEvaluationService,
) {
	handler := &ParameterSkinEvaluationHandler{service}

	e.GET("/parameter-skin-evaluation/:id", handler.GetByID)
	e.PUT("/parameter-skin-evaluation/:id", handler.UpdateByID)
}

func NewPublicParameterSkinEvaluationHandler(
	e *echo.Group,
	service ParameterSkinEvaluationService,
) {
	handler := &ParameterSkinEvaluationHandler{service}

	e.GET("/parameter-skin-evaluation", handler.GetMany)
}

// @router /api/v1/parameter-skin-evaluation/{id} [get]
// @tags parameter-skin-evaluation
// @summary Get parameter skin evaluation detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Parameter Skin Evaluation ID"
// @success 200 {object} domain.SingleResponse[domain.ParameterSkinEvaluation]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *ParameterSkinEvaluationHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.ParameterSkinEvaluation]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/parameter-skin-evaluation/{id} [put]
// @tags parameter-skin-evaluation
// @summary Update parameter skin evaluation detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Parameter Skin Evaluation ID"
// @param request body domain.ParameterSkinEvaluationRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.ParameterSkinEvaluation]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *ParameterSkinEvaluationHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	var request *domain.ParameterSkinEvaluationRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		if strings.Contains(err.Error(), "gtfield") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Lower point must be greater than upper point",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.UpdateByID(ctx, &id, request)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Update data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.ParameterSkinEvaluation]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/parameter-skin-evaluation [get]
// @tags parameter-skin-evaluation
// @summary Get many parameter skin evaluation detail
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.ParameterSkinEvaluationFilter true "Parameter Skin Evaluation Filter"
// @success 200 {object} domain.PaginationResponse[domain.ParameterSkinEvaluation]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *ParameterSkinEvaluationHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.ParameterSkinEvaluationFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Failed binding filter: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	responseData := domain.PaginationData[domain.ParameterSkinEvaluation]{
		Content:    data,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.ParameterSkinEvaluation]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    responseData,
			Message: "Get many data successfully",
		},
	)
}
