package rest_test

import (
	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-faker/faker/v4"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestFaceAgingHandler(t *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.FaceAgingService)
		handler     = &rest.FaceAgingHandler{mockService}
	)
	e.Validator = utils.NewCustomValidator()

	t.Run("Success face aging with concern", func(t *testing.T) {
		faceAgingReq := &domain.FaceAgingConcernRequest{
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaLower,
					},
				},
			},
			IsBeautify: true,
		}
		reqBytes, err := json.<PERSON>(faceAgingReq)
		assert.NoError(t, err)
		skinAnalyzeID := "123"

		faceAgingRes := domain.FaceAgingJobQueue{}
		err = faker.FakeData(&faceAgingRes)
		require.NoError(t, err)
		mockService.On(
			"FaceAgingWithConcern",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("*domain.FaceAgingConcernRequest"),
		).
			Return(&faceAgingRes, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodPost,
			fmt.Sprintf("/face-aging/concerns/%s", skinAnalyzeID),
			bytes.NewBuffer(reqBytes),
		)
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/face-aging/concerns/:id")
		c.SetParamNames("id")
		c.SetParamValues(skinAnalyzeID)

		err = handler.FaceAgingWithConcern(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusAccepted, rec.Code)
		mockService.AssertExpectations(t)
	})

	t.Run("Success get face aging status", func(t *testing.T) {
		skinAnalyzeID := faker.UUIDHyphenated()

		faceAgingRes := domain.FaceAgingJobQueue{}
		err := faker.FakeData(&faceAgingRes)
		require.NoError(t, err)
		mockService.On(
			"FaceAgingJobStatusByID",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(&faceAgingRes, nil).
			Once()

		req := httptest.NewRequest(
			http.MethodGet,
			fmt.Sprintf("/face-aging/status/%s", skinAnalyzeID),
			nil,
		)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/face-aging/status/:id")
		c.SetParamNames("id")
		c.SetParamValues(skinAnalyzeID)

		err = handler.FaceAgingJobStatusByID(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		mockService.AssertExpectations(t)
	})

	t.Run("Success face aging job callback", func(t *testing.T) {
		skinAnalyzeID := faker.UUIDHyphenated()

		faceAgingRes := domain.FaceAgingConcernMLResponse{}
		err := faker.FakeData(&faceAgingRes)
		require.NoError(t, err)
		reqBytes, err := json.Marshal(faceAgingRes)
		assert.NoError(t, err)

		faceAgingJobRes := domain.FaceAgingJobQueue{}
		err = faker.FakeData(&faceAgingJobRes)
		require.NoError(t, err)
		mockService.On(
			"FaceAgingJobCallback",
			mock.Anything,
			mock.AnythingOfType("string"),
			faceAgingRes,
		).
			Return(nil).
			Once()

		req := httptest.NewRequest(
			http.MethodPost,
			fmt.Sprintf("/face-aging/callback/%s", skinAnalyzeID),
			bytes.NewBuffer(reqBytes),
		)
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetPath("/face-aging/callback/:id")
		c.SetParamNames("id")
		c.SetParamValues(skinAnalyzeID)

		err = handler.FaceAgingJobCallback(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		mockService.AssertExpectations(t)
	})
}
