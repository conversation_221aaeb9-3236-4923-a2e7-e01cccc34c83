package rest

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type UserSurveyService interface {
	Create(
		ctx context.Context,
		request *domain.UserSurveyRequest,
	) (*domain.UserSurvey, error)
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.UserSurvey, error)
	GetMany(
		ctx context.Context,
		filter *domain.UserSurveyFilter,
	) ([]domain.UserSurvey, int, error)
}

type UserSurveyHandler struct {
	Service UserSurveyService
}

func NewUserSurveyHandler(e *echo.Group, service UserSurveyService) {
	handler := &UserSurveyHandler{service}

	e.GET("/user-survey/:id", handler.GetByID)
	e.GET("/user-survey", handler.GetMany)
}

func NewPublicUserSurveyHandler(e *echo.Group, service UserSurveyService) {
	handler := &UserSurveyHandler{service}

	e.POST("/user-survey", handler.Create)
}

// @router /api/v1/user-survey [post]
// @tags user-survey
// @summary Create new user survey
// @accept json
// @produce json
// @param request body domain.UserSurveyRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.UserSurvey]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *UserSurveyHandler) Create(c echo.Context) (err error) {
	var request *domain.UserSurveyRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.Create(ctx, request)

	if err != nil {
		if strings.Contains(err.Error(), "user_surveys_user_id_fkey") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:   http.StatusNotFound,
					Status: utils.ErrorMsg,
					Message: fmt.Sprintf(
						"User with id %s is not found",
						*request.UserID,
					),
				},
			)
		}

		if strings.Contains(err.Error(), "user_surveys_skin_analyze_id_fkey") {
			return c.JSON(
				http.StatusNotFound,
				domain.SingleResponse[domain.Empty]{
					Code:   http.StatusNotFound,
					Status: utils.ErrorMsg,
					Message: fmt.Sprintf(
						"Skin analyze with id %s is not found",
						*request.SkinAnalyzeID,
					),
				},
			)
		}

		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Create data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.UserSurvey]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/user-survey/{id} [get]
// @tags user-survey
// @summary Get user survey detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "User Survey ID"
// @success 200 {object} domain.SingleResponse[domain.UserSurvey]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *UserSurveyHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	// prevent nil pointer dereference error.
	if data == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get data failed: data is nil",
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.UserSurvey]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/user-survey [get]
// @tags user-survey
// @summary Get many user survey detail
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.UserSurveyFilter true "User Survey Filter"
// @success 200 {object} domain.PaginationResponse[domain.UserSurvey]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *UserSurveyHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.UserSurveyFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		if strings.Contains(err.Error(), "uuid4") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Invalid uuid4 format",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	response := domain.PaginationData[domain.UserSurvey]{
		Content:    data,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.UserSurvey]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    response,
			Message: "Get many data successfully",
		},
	)
}
