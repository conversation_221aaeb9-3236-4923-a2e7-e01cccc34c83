package rest

import (
	"api/domain"
	internalMiddleware "api/internal/rest/middleware"
	"api/utils"
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

type SummaryService interface {
	GetSummary(ctx context.Context, id string) (*domain.SummaryResponse, error)
}

type Summa<PERSON>Handler struct {
	Service SummaryService
}

func NewSummaryHandler(e *echo.Group, service SummaryService) {
	handler := &SummaryHandler{
		Service: service,
	}

	summaryRoutes := e.Group("")
	summaryRoutes.Use(internalMiddleware.SetRequestContextWithTimeout(45 * time.Second))

	summaryRoutes.GET("/summary/:id", handler.GetSummary)
}

// @router /api/v1/summary/{id} [get]
// @tags summary
// @summary Get summary by ID
// @accept json
// @produce json
// @param id path string true "Skin Analyze ID"
// @success 200 {object} domain.SingleResponse[domain.SummaryResponse]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
func (handler *SummaryHandler) GetSummary(c echo.Context) (err error) {
	id := c.Param("id")
	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  "error",
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	summary, err := handler.Service.GetSummary(ctx, id)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  "error",
				Message: utils.GetErrorMessage(ctx, err, "Summary not found"),
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[domain.SummaryResponse]{
		Code:    http.StatusOK,
		Status:  "success",
		Message: "Summary retrieved successfully",
		Data:    *summary,
	})
}
