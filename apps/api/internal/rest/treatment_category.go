package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type TreatmentCategoryService interface {
	Create(
		ctx context.Context,
		data *domain.TreatmentCategory,
	) (*domain.TreatmentCategory, error)
	GetByID(ctx context.Context, id *string) (*domain.TreatmentCategory, error)
	UpdateByID(
		ctx context.Context,
		input *domain.TreatmentCategory,
	) (*domain.TreatmentCategory, error)
	DeleteByID(ctx context.Context, id *string) (*domain.TreatmentCategory, error)
	GetMany(
		ctx context.Context,
		filter *domain.TreatmentCategoryFilter,
	) ([]domain.TreatmentCategory, int, error)
}

type TreatmentCategoryHandler struct {
	Service TreatmentCategoryService
}

func NewTreatmentCategoryHandler(
	e *echo.Group,
	service TreatmentCategoryService,
) {
	handler := &TreatmentCategoryHandler{service}

	e.POST("/treatment-category", handler.Create)
	e.GET("/treatment-category/:id", handler.GetByID)
	e.PUT("/treatment-category/:id", handler.UpdateByID)
	e.DELETE("/treatment-category/:id", handler.DeleteByID)
	e.GET("/treatment-category", handler.GetMany)
}

// @router /api/v1/treatment-category [post]
// @tags treatment-category
// @summary Create new treatment category
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.TreatmentCategoryRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.TreatmentCategoryResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentCategoryHandler) Create(c echo.Context) (err error) {
	var request *domain.TreatmentCategoryRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	input := request.ToRepo()

	ctx := c.Request().Context()
	data, err := handler.Service.Create(ctx, &input)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Create data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.TreatmentCategoryResponse]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/treatment-category/{id} [get]
// @tags treatment-category
// @summary Get treatment category detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Category ID"
// @success 200 {object} domain.SingleResponse[domain.TreatmentCategoryResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentCategoryHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentCategoryResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/treatment-category/{id} [put]
// @tags treatment-category
// @summary Update treatment category detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Category ID"
// @param request body domain.TreatmentCategoryRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.TreatmentCategoryResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentCategoryHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")

	var request *domain.TreatmentCategoryRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	input := request.ToRepo()
	input.ID = id

	ctx := c.Request().Context()
	data, err := handler.Service.UpdateByID(ctx, &input)

	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return c.JSON(
				http.StatusConflict,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: "Update data failed: duplicate category name",
				},
			)
		}

		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Update data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentCategoryResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/treatment-category/{id} [delete]
// @tags treatment-category
// @summary Delete treatment category detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Category ID"
// @success 200 {object} domain.SingleResponse[domain.TreatmentCategoryResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentCategoryHandler) DeleteByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.DeleteByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Delete data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentCategoryResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data deleted successfully",
		},
	)
}

// @router /api/v1/treatment-category [get]
// @tags treatment-category
// @summary Get many treatment category detail
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.TreatmentCategoryFilter true "Treatment Category Filter"
// @success 200 {object} domain.PaginationResponse[domain.TreatmentCategoryResponse]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *TreatmentCategoryHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.TreatmentCategoryFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	responseData := domain.PaginationData[domain.TreatmentCategoryResponse]{
		Content:    []domain.TreatmentCategoryResponse{},
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	for _, value := range data {
		response := value.ToResponse()
		responseData.Content = append(responseData.Content, response)
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.TreatmentCategoryResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    responseData,
			Message: "Get many data successfully",
		},
	)
}
