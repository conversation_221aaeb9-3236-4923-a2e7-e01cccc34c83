package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type TreatmentProductService interface {
	Create(ctx context.Context, request *domain.TreatmentProductRequest) (
		*domain.TreatmentProduct,
		*domain.TreatmentProductSupplementaryData,
		error,
	)
	GetByID(ctx context.Context, id *string) (
		*domain.TreatmentProductResponse,
		error,
	)
	UpdateByID(
		ctx context.Context,
		id *string,
		request *domain.TreatmentProductRequest,
	) (*domain.TreatmentProduct, *domain.TreatmentProductSupplementaryData, error)
	DeleteByID(ctx context.Context, id *string) (
		*domain.TreatmentProduct,
		error,
	)
	GetMany(
		ctx context.Context,
		filter *domain.TreatmentProductFilter,
	) ([]domain.TreatmentProductGetMany, int, error)
}

type TreatmentProductHandler struct {
	Service TreatmentProductService
}

func NewTreatmentProductHandler(
	e *echo.Group,
	service TreatmentProductService,
) {
	handler := &TreatmentProductHandler{service}

	e.POST("/treatment-product", handler.Create)
	e.GET("/treatment-product/:id", handler.GetByID)
	e.PUT("/treatment-product/:id", handler.UpdateByID)
	e.DELETE("/treatment-product/:id", handler.DeleteByID)
}

func NewPublicTreatmentProductHandler(
	e *echo.Group,
	service TreatmentProductService,
) {
	handler := &TreatmentProductHandler{service}

	e.GET("/treatment-product", handler.GetMany)
}

func prepareCreateAndUpdateResponse(
	primaryData *domain.TreatmentProduct,
	supplementaryData *domain.TreatmentProductSupplementaryData,
) domain.TreatmentProductResponse {
	response := domain.TreatmentProductResponse{
		ID:                        primaryData.ID,
		ItemCode:                  primaryData.ItemCode,
		Name:                      primaryData.Name,
		Type:                      primaryData.Type,
		Description:               primaryData.Description,
		Price:                     primaryData.Price,
		MediaUrl:                  primaryData.MediaUrl,
		ThumbnailUrl:              primaryData.ThumbnailUrl,
		Notes:                     primaryData.Notes,
		Quantity:                  primaryData.Quantity,
		IsTopRecommendation:       primaryData.IsTopRecommendation,
		DurationTopRecommendation: primaryData.DurationTopRecommendation,
		Timestamp:                 primaryData.Timestamp,
		TreatmentProductSupplementaryData: domain.TreatmentProductSupplementaryData{
			Category:        supplementaryData.Category,
			Interval:        supplementaryData.Interval,
			Concern:         supplementaryData.Concern,
			SurveyQuestions: supplementaryData.SurveyQuestions,
		},
	}

	return response
}

// @router /api/v1/treatment-product [post]
// @tags treatment-product
// @summary Create new treatment or product
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.TreatmentProductRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.TreatmentProductResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentProductHandler) Create(c echo.Context) (err error) {
	var request *domain.TreatmentProductRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		if strings.Contains(err.Error(), "TopRecommendation") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "is_top_recommendation and duration_top_recommendation either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	primaryData, supplementaryData, err := handler.Service.Create(ctx, request)

	if err != nil {
		if strings.Contains(err.Error(), "selected answer") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Create data failed"),
			},
		)
	}

	// prevent nil pointer dereference error.
	if primaryData == nil || supplementaryData == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Create data failed: primary or supplementary data is nil",
			},
		)
	}

	response := prepareCreateAndUpdateResponse(primaryData, supplementaryData)

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.TreatmentProductResponse]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    response,
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/treatment-product/{id} [get]
// @tags treatment-product
// @summary Get treatment or product detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Product ID"
// @success 200 {object} domain.SingleResponse[domain.TreatmentProductResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentProductHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	// prevent nil pointer dereference error.
	if data == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Get data failed: data is nil",
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentProductResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/treatment-product/{id} [put]
// @tags treatment-product
// @summary Update treatment or product detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Product ID"
// @param request body domain.TreatmentProductRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.TreatmentProductResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentProductHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	var request *domain.TreatmentProductRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(request); err != nil {
		if strings.Contains(err.Error(), "TopRecommendation") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "is_top_recommendation and duration_top_recommendation either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	updatedData, supplementaryData, err := handler.Service.UpdateByID(
		ctx,
		&id,
		request,
	)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Update data failed"),
			},
		)
	}

	// prevent nil pointer dereference error.
	if updatedData == nil || supplementaryData == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Update data failed: data is nil",
			},
		)
	}

	response := prepareCreateAndUpdateResponse(updatedData, supplementaryData)

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentProductResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    response,
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/treatment-product/{id} [delete]
// @tags treatment-product
// @summary Delete treatment or product detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Product ID"
// @success 200 {object} domain.SingleResponse[domain.TreatmentProduct]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentProductHandler) DeleteByID(c echo.Context) (err error) {
	id := c.Param("id")

	if id == "" {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid ID",
			},
		)
	}

	ctx := c.Request().Context()
	data, err := handler.Service.DeleteByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Delete data failed"),
			},
		)
	}

	// prevent nil pointer dereference error.
	if data == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Delete data failed: data is nil",
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentProduct]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    *data,
			Message: "Data deleted successfully",
		},
	)
}

// @router /api/v1/treatment-product [get]
// @tags treatment-product
// @summary Get many treatment or product detail
// @accept json
// @produce json
// @param filter query domain.TreatmentProductFilter true "Treatment Product Filter"
// @success 200 {object} domain.PaginationResponse[domain.TreatmentProductGetMany]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *TreatmentProductHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.TreatmentProductFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Error binding filter: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		if strings.Contains(err.Error(), "Types") &&
			strings.Contains(err.Error(), "oneof") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Invalid types filter, should be either treatment or product",
				},
			)
		}

		if strings.Contains(err.Error(), "CategoryIDs") &&
			strings.Contains(err.Error(), "uuid4") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Invalid uuid format for category IDs filter",
				},
			)
		}

		if strings.Contains(err.Error(), "SortColumn") &&
			strings.Contains(err.Error(), "oneof") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Invalid sort column name",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	response := domain.PaginationData[domain.TreatmentProductGetMany]{
		Content:    data,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.TreatmentProductGetMany]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    response,
			Message: "Get many data successfully",
		},
	)
}
