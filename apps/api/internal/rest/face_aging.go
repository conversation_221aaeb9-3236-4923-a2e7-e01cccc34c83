package rest

import (
	"api/domain"
	"api/utils"
	"context"
	"net/http"

	"github.com/labstack/echo/v4"
)

//go:generate mockery
type FaceAgingService interface {
	FaceAgingWithConcern(
		ctx context.Context,
		id string,
		data *domain.FaceAgingConcernRequest,
	) (*domain.FaceAgingJobQueue, error)
	FaceAgingJobStatusByID(
		ctx context.Context,
		id string,
	) (*domain.FaceAgingJobQueue, error)
	FaceAgingJobCallback(
		ctx context.Context,
		id string,
		data domain.FaceAgingConcernMLResponse,
	) error
}

type FaceAgingHandler struct {
	Service FaceAgingService
}

func NewFaceAgingHandler(
	e *echo.Group,
	service FaceAgingService,
) {
	handler := &FaceAgingHandler{
		Service: service,
	}

	e.POST("/face-aging/concerns/:id", handler.FaceAgingWithConcern)
	e.GET("/face-aging/status/:id", handler.FaceAgingJobStatusByID)
}

func NewPublicFaceAgingHandler(
	e *echo.Group,
	service FaceAgingService,
) {
	handler := &FaceAgingHandler{
		Service: service,
	}

	e.POST("/face-aging/callback/:id", handler.FaceAgingJobCallback)
}

// @router /api/v1/face-aging/concerns/{id} [post]
// @tags face-aging
// @security BearerAuth
// @summary Face aging with concern
// @accept json
// @produce json
// @param id path string true "Skin analyze ID"
// @param request body domain.FaceAgingConcernRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.FaceAgingJobQueue]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *FaceAgingHandler) FaceAgingWithConcern(c echo.Context) (err error) {
	var request *domain.FaceAgingConcernRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Message: "Invalid request",
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	skinAnalyzeID := c.Param("id")
	ctx := c.Request().Context()

	response, err := handler.Service.FaceAgingWithConcern(ctx, skinAnalyzeID, request)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Failed to generate face aging with concern"),
			},
		)
	}

	return c.JSON(http.StatusAccepted, domain.SingleResponse[domain.FaceAgingJobQueue]{
		Code:    http.StatusAccepted,
		Message: "Job created successfully",
		Data:    *response,
		Status:  "success",
	})
}

// @router /api/v1/face-aging/status/{id} [get]
// @tags face-aging
// @security BearerAuth
// @summary Face aging with concern
// @accept json
// @produce json
// @param id path string true "Job id"
// @success 200 {object} domain.SingleResponse[domain.FaceAgingJobQueue]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *FaceAgingHandler) FaceAgingJobStatusByID(c echo.Context) (err error) {
	var request domain.FaceAgingJobStatusRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Message: "Invalid request",
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()

	response, err := handler.Service.FaceAgingJobStatusByID(ctx, request.ID)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Failed to generate face aging with concern"),
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[domain.FaceAgingJobQueue]{
		Code:    http.StatusOK,
		Message: "Generated image successfully",
		Data:    *response,
		Status:  "success",
	})
}

// @router /api/v1/face-aging/callback/{id} [post]
// @tags face-aging
// @summary Face aging with concern
// @accept json
// @produce json
// @param id path string true "Job ID"
// @param request body domain.FaceAgingConcernMLResponse true "Request body"
// @success 200 {object} domain.SingleResponse[domain.FaceAgingJobQueue]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
// @failure 404 {object} domain.SingleResponse[domain.Empty]
// @failure 500 {object} domain.SingleResponse[domain.Empty]
func (handler *FaceAgingHandler) FaceAgingJobCallback(c echo.Context) (err error) {
	var request domain.FaceAgingJobCallbackRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Message: "Invalid request",
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()

	err = handler.Service.FaceAgingJobCallback(ctx, request.ID, request.FaceAgingConcernMLResponse)
	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Failed to generate face aging with concern"),
			},
		)
	}

	return c.JSON(http.StatusOK, domain.SingleResponse[domain.Empty]{
		Code:    http.StatusOK,
		Message: "Callback face aging",
		Status:  "success",
	})
}
