package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type AuthService interface {
	GenerateToken(
		ctx context.Context,
		request *domain.LoginRequest,
	) (*string, *string, error)
	RefreshToken(
		ctx context.Context,
		request *domain.RefreshRequest,
	) (*string, *string, error)
}

type AuthHandler struct {
	Service AuthService
}

func NewAuthHandler(e *echo.Group, service AuthService) {
	handler := &AuthHandler{service}

	e.POST("/auth/login", handler.Login)
	e.POST("/auth/refresh", handler.Refresh)
}

// @router /api/v1/auth/login [post]
// @tags auth
// @summary Login with user account
// @accept json
// @produce json
// @param request body domain.LoginRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.LoginResponse]
// @success 400 {object} domain.SingleResponse[domain.Empty]
func (handler *AuthHandler) Login(c echo.Context) (err error) {
	var request *domain.LoginRequest
	err = c.Bind(&request)
	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	token, refreshToken, err := handler.Service.GenerateToken(ctx, request)

	if err != nil {
		if strings.Contains(err.Error(), "Unauthorized") {
			return c.JSON(
				http.StatusUnauthorized,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusUnauthorized,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Login failed"),
			},
		)
	}

	if token == nil || refreshToken == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Login failed: token is nil",
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.LoginResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    domain.LoginResponse{Token: *token, RefreshToken: *refreshToken},
			Message: "Login successfully",
		},
	)
}

// @router /api/v1/auth/refresh [post]
// @tags auth
// @summary Get token based on refresh token
// @accept json
// @produce json
// @param request body domain.RefreshRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.LoginResponse]
// @success 400 {object} domain.SingleResponse[domain.Empty]
func (h *AuthHandler) Refresh(c echo.Context) (err error) {
	var request *domain.RefreshRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	token, refreshToken, err := h.Service.RefreshToken(ctx, request)

	if err != nil {
		if strings.Contains(err.Error(), "Unauthorized") {
			return c.JSON(
				http.StatusUnauthorized,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusUnauthorized,
					Status:  utils.ErrorMsg,
					Message: err.Error(),
				},
			)
		}

		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Refresh failed"),
			},
		)
	}

	if token == nil || refreshToken == nil {
		return c.JSON(
			http.StatusInternalServerError,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusInternalServerError,
				Status:  utils.ErrorMsg,
				Message: "Refresh failed: token is nil",
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.LoginResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    domain.LoginResponse{Token: *token, RefreshToken: *refreshToken},
			Message: "Refresh successfully",
		},
	)
}
