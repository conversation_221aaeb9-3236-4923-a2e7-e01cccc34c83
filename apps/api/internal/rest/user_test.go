package rest_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestUserRest(test *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.UserService)
		handler     = &rest.UserHandler{mockService}
	)

	e.Validator = utils.NewCustomValidator()

	var (
		password = "test-password"
		reqBody  = domain.UserRequest{
			Name:        "Test User",
			Email:       "<EMAIL>",
			Role:        domain.Admin,
			PhoneNumber: "08123456789",
			Password:    &password,
		}

		sharedData = domain.User{
			Name:        "Test User",
			Email:       "<EMAIL>",
			Role:        domain.Admin,
			PhoneNumber: "08123456789",
		}
	)

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(test, err)

	test.Run("Create API", func(t *testing.T) {
		svc := mockService.EXPECT().
			Create(
				mock.Anything,
				mock.AnythingOfType("*domain.User"),
			)

		t.Run("Success", func(t *testing.T) {
			svc.Return(&sharedData, nil).Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/user",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusCreated, rec.Code)
		})

		t.Run("Failed duplicate email", func(t *testing.T) {
			svc.Return(nil, fmt.Errorf("Email already exist")).Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/user",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusConflict, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.Return(nil, fmt.Errorf("internal server error")).Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/user",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPost,
				"/user",
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})

		t.Run("Failed missing required fields", func(t *testing.T) {
			invalidReq := reqBody
			invalidReq.Email = "" // Missing required field

			invalidReqBytes, err := json.Marshal(invalidReq)
			require.NoError(t, err)

			req := httptest.NewRequest(
				http.MethodPost,
				"/user",
				bytes.NewReader(invalidReqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err = handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	test.Run("GetByID API", func(t *testing.T) {
		svc := mockService.EXPECT().
			GetByID(
				mock.Anything,
				mock.AnythingOfType("*string"),
			)

		t.Run("Success", func(t *testing.T) {
			svc.Return(&sharedData, nil).Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/user/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.Return(nil, fmt.Errorf("Data not found")).Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/user/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("UpdateByID API", func(t *testing.T) {
		svc := mockService.EXPECT().
			UpdateByID(
				mock.Anything,
				mock.AnythingOfType("*domain.User"),
			)

		t.Run("Success", func(t *testing.T) {
			svc.Return(&sharedData, nil).Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.Return(nil, fmt.Errorf("Data not found")).Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.Return(nil, fmt.Errorf("internal server error")).Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v", utils.DummyID),
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	test.Run("DeleteByID API", func(t *testing.T) {
		svc := mockService.EXPECT().
			DeleteByID(
				mock.Anything,
				mock.AnythingOfType("*string"),
			)

		t.Run("Success", func(t *testing.T) {
			svc.Return(&sharedData, nil).Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/user/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.Return(nil, fmt.Errorf("Data not found")).Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/user/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("GetMany API", func(t *testing.T) {
		t.Run("Success", func(t *testing.T) {
			mockService.EXPECT().
				GetMany(
					mock.Anything,
					mock.AnythingOfType("*domain.UserFilter"),
				).Return(&domain.PaginationData[domain.UserResponse]{
				Content: []domain.UserResponse{
					{
						ID:          utils.DummyID,
						Name:        "Test User",
						Email:       "<EMAIL>",
						Role:        domain.Admin,
						PhoneNumber: "08123456789",
					},
				},
				TotalData: 1,
			}, nil).Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/user",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			mockService.EXPECT().
				GetMany(
					mock.Anything,
					mock.AnythingOfType("*domain.UserFilter"),
				).Return(nil, fmt.Errorf("internal server error")).Once()

			req := httptest.NewRequest(
				http.MethodGet,
				"/user",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodGet,
				"/user?page=invalid",
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	test.Run("UpdatePassword API", func(t *testing.T) {
		svc := mockService.EXPECT().
			UpdatePassword(
				mock.Anything,
				mock.AnythingOfType("*string"),
				mock.AnythingOfType("*string"),
			)

		passwordReq := domain.UpdatePasswordRequest{
			Password: "new-password",
		}

		passwordReqBytes, err := json.Marshal(passwordReq)
		require.NoError(test, err)

		t.Run("Success", func(t *testing.T) {
			svc.Return(nil).Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v/update-password", utils.DummyID),
				bytes.NewReader(passwordReqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdatePassword(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.Return(fmt.Errorf("Data not found")).Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v/update-password", utils.DummyID),
				bytes.NewReader(passwordReqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdatePassword(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.Return(fmt.Errorf("internal server error")).Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v/update-password", utils.DummyID),
				bytes.NewReader(passwordReqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdatePassword(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/user/%v/update-password", utils.DummyID),
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdatePassword(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})
}
