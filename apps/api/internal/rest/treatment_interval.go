package rest

import (
	"context"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"api/domain"
	"api/utils"
)

//go:generate mockery
type TreatmentIntervalService interface {
	Create(
		ctx context.Context,
		data *domain.TreatmentInterval,
	) (*domain.TreatmentInterval, error)
	GetByID(
		ctx context.Context,
		id *string,
	) (*domain.TreatmentInterval, error)
	UpdateByID(
		ctx context.Context,
		input *domain.TreatmentInterval,
	) (*domain.TreatmentInterval, error)
	DeleteByID(ctx context.Context, id *string) (*domain.TreatmentInterval, error)
	GetMany(
		ctx context.Context,
		filter *domain.TreatmentIntervalFilter,
	) ([]domain.TreatmentInterval, int, error)
}

type TreatmentIntervalHandler struct {
	Service TreatmentIntervalService
}

func NewTreatmentIntervalHandler(
	e *echo.Group,
	service TreatmentIntervalService,
) {
	handler := &TreatmentIntervalHandler{service}

	e.POST("/treatment-interval", handler.Create)
	e.GET("/treatment-interval/:id", handler.GetByID)
	e.PUT("/treatment-interval/:id", handler.UpdateByID)
	e.DELETE("/treatment-interval/:id", handler.DeleteByID)
	e.GET("/treatment-interval", handler.GetMany)
}

// @router /api/v1/treatment-interval [post]
// @tags treatment-interval
// @summary Create new treatment interval
// @security BearerAuth
// @accept json
// @produce json
// @param request body domain.TreatmentIntervalRequest true "Request body"
// @success 201 {object} domain.SingleResponse[domain.TreatmentIntervalResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentIntervalHandler) Create(c echo.Context) (err error) {
	var request *domain.TreatmentIntervalRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	input := request.ToRepo()

	ctx := c.Request().Context()
	data, err := handler.Service.Create(ctx, &input)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Create data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusCreated,
		domain.SingleResponse[domain.TreatmentIntervalResponse]{
			Code:    http.StatusCreated,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data created successfully",
		},
	)
}

// @router /api/v1/treatment-interval/{id} [get]
// @tags treatment-interval
// @summary Get treatment interval detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Interval ID"
// @success 200 {object} domain.SingleResponse[domain.TreatmentIntervalResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentIntervalHandler) GetByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.GetByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentIntervalResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Get data successfully",
		},
	)
}

// @router /api/v1/treatment-interval/{id} [put]
// @tags treatment-interval
// @summary Update treatment interval detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Interval ID"
// @param request body domain.TreatmentIntervalRequest true "Request body"
// @success 200 {object} domain.SingleResponse[domain.TreatmentIntervalResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentIntervalHandler) UpdateByID(c echo.Context) (err error) {
	id := c.Param("id")

	var request *domain.TreatmentIntervalRequest
	err = c.Bind(&request)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err = c.Validate(request); err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	input := request.ToRepo()
	input.ID = id

	ctx := c.Request().Context()
	data, err := handler.Service.UpdateByID(ctx, &input)

	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return c.JSON(
				http.StatusConflict,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusNotFound,
					Status:  utils.ErrorMsg,
					Message: "Update data failed: duplicate days",
				},
			)
		}

		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Update data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentIntervalResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data updated successfully",
		},
	)
}

// @router /api/v1/treatment-interval/{id} [delete]
// @tags treatment-interval
// @summary Delete treatment interval detail by id
// @security BearerAuth
// @accept json
// @produce json
// @param id path string true "Treatment Interval ID"
// @success 200 {object} domain.SingleResponse[domain.TreatmentIntervalResponse]
// @failure 400 {object} domain.SingleResponse[domain.Empty]
func (handler *TreatmentIntervalHandler) DeleteByID(c echo.Context) (err error) {
	id := c.Param("id")

	ctx := c.Request().Context()
	data, err := handler.Service.DeleteByID(ctx, &id)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Delete data failed"),
			},
		)
	}

	return c.JSON(
		http.StatusOK,
		domain.SingleResponse[domain.TreatmentIntervalResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    data.ToResponse(),
			Message: "Data deleted successfully",
		},
	)
}

// @router /api/v1/treatment-interval [get]
// @tags treatment-interval
// @summary Get many treatment interval detail
// @security BearerAuth
// @accept json
// @produce json
// @param filter query domain.TreatmentIntervalFilter true "Treatment Interval Filter"
// @success 200 {object} domain.PaginationResponse[domain.TreatmentIntervalResponse]
// @failure 400 {object} domain.PaginationResponse[domain.Empty]
func (handler *TreatmentIntervalHandler) GetMany(c echo.Context) (err error) {
	filter := new(domain.TreatmentIntervalFilter)
	err = c.Bind(filter)

	if err != nil {
		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid request: " + err.Error(),
			},
		)
	}

	if err := c.Validate(filter); err != nil {
		if strings.Contains(err.Error(), "Page") {
			return c.JSON(
				http.StatusBadRequest,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusBadRequest,
					Status:  utils.ErrorMsg,
					Message: "Page and page size must be both either not empty or empty",
				},
			)
		}

		return c.JSON(
			http.StatusBadRequest,
			domain.SingleResponse[domain.Empty]{
				Code:    http.StatusBadRequest,
				Status:  utils.ErrorMsg,
				Message: "Invalid filter: " + err.Error(),
			},
		)
	}

	ctx := c.Request().Context()
	data, totalData, err := handler.Service.GetMany(ctx, filter)

	if err != nil {
		return c.JSON(
			utils.GetStatusCode(err),
			domain.SingleResponse[domain.Empty]{
				Code:    utils.GetStatusCode(err),
				Status:  utils.ErrorMsg,
				Message: utils.GetErrorMessage(ctx, err, "Get many data failed"),
			},
		)
	}

	responseData := domain.PaginationData[domain.TreatmentIntervalResponse]{
		Content:    []domain.TreatmentIntervalResponse{},
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalData:  totalData,
		TotalPages: filter.GetTotalPages(totalData),
	}

	for _, value := range data {
		response := value.ToResponse()
		responseData.Content = append(responseData.Content, response)
	}

	return c.JSON(
		http.StatusOK,
		domain.PaginationResponse[domain.TreatmentIntervalResponse]{
			Code:    http.StatusOK,
			Status:  utils.SuccessMsg,
			Data:    responseData,
			Message: "Get many data successfully",
		},
	)
}
