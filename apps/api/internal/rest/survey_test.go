package rest_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"api/domain"
	"api/internal/rest"
	"api/internal/rest/mocks"
	"api/utils"
)

func TestSurveyRest(test *testing.T) {
	var (
		e           = echo.New()
		mockService = new(mocks.SurveyService)
		handler     = &rest.SurveyHandler{mockService}
	)

	e.Validator = utils.NewCustomValidator()

	reqBody := domain.SurveyRequest{
		Description: "Test create survey data",
		Question:    "Apakah test nya berhasil?",
		Answers: []domain.SurveyAnswer{
			{
				Title: "Ya",
			},
			{
				Title: "Tidak",
			},
		},
		Type:     "single_full",
		Category: domain.Informational,
	}

	reqBytes, err := json.Marshal(reqBody)
	require.NoError(test, err)

	sharedData := reqBody.ToRepo()

	sharedRequestNested := domain.SurveyRequestNested{
		Description: sharedData.Description,
		Question:    sharedData.Question,
		Answers:     sharedData.Answers,
		Type:        sharedData.Type,
		Category:    sharedData.Category,
		ChildQuestions: []domain.SurveyRequestChildQuestion{
			{
				ParentQuestionAnswer: 0,
				Description:          "Test child question",
				Question:             "Apakah test child question nya berhasil?",
				Answers:              sharedData.Answers,
				Type:                 domain.SingleFull,
				QuestionOrder:        0,
				Category:             domain.Contraindication,
			},
		},
	}

	reqBytesNested, err := json.Marshal(sharedRequestNested)

	sharedResponseNested := domain.SurveyResponseNested{
		SurveyResponse: sharedData.ToResponse(),
		ChildQuestions: []domain.SurveyResponse{},
	}

	test.Run("Create API", func(t *testing.T) {
		svc := mockService.On(
			"Create",
			mock.Anything,
			mock.AnythingOfType("*domain.Survey"),
		)

		t.Run("Success", func(t *testing.T) {
			output := reqBody.ToRepo()
			svc.
				Return(&output, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/survey",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusCreated, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Error Create service")).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/survey",
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPost,
				"/survey",
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	test.Run("CreateNested API", func(t *testing.T) {
		svc := mockService.On(
			"CreateNested",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyRequestNested"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedResponseNested, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/survey/nested",
				bytes.NewReader(reqBytesNested),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.CreateNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusCreated, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Error Create service")).
				Once()

			req := httptest.NewRequest(
				http.MethodPost,
				"/survey/nested",
				bytes.NewReader(reqBytesNested),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.CreateNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})

		t.Run("Failed invalid request", func(t *testing.T) {
			req := httptest.NewRequest(
				http.MethodPost,
				"/survey",
				bytes.NewReader([]byte(`invalid-json`)),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.Create(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})
	})

	test.Run("GetByID API", func(t *testing.T) {
		svc := mockService.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			output := reqBody.ToRepo()
			svc.
				Return(&output, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("GetByIDNested API", func(t *testing.T) {
		svc := mockService.On(
			"GetByIDNested",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedResponseNested, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetByIDNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				nil,
			)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetByIDNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("UpdateByID API", func(t *testing.T) {
		svc := mockService.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*domain.Survey"),
		)

		t.Run("Success", func(t *testing.T) {
			output := reqBody.ToRepo()
			svc.
				Return(&output, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(
					nil,
					fmt.Errorf("Data is not found"),
				).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				bytes.NewReader(reqBytes),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.UpdateByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("UpdateByIDNested API", func(t *testing.T) {
		svc := mockService.On(
			"UpdateByIDNested",
			mock.Anything,
			mock.AnythingOfType("*string"),
			mock.AnythingOfType("*domain.SurveyRequestNested"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return(&sharedResponseNested, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/survey/nested/%v", utils.DummyID),
				bytes.NewReader(reqBytesNested),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)

			err := handler.UpdateByIDNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(
					nil,
					fmt.Errorf("Data is not found"),
				).
				Once()

			req := httptest.NewRequest(
				http.MethodPut,
				fmt.Sprintf("/survey/nested/%v", utils.DummyID),
				bytes.NewReader(reqBytesNested),
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(utils.DummyID)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.UpdateByIDNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("DeleteByID API", func(t *testing.T) {
		svc := mockService.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		)

		t.Run("Success", func(t *testing.T) {
			output := reqBody.ToRepo()
			svc.
				Return(&output, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed id not found", func(t *testing.T) {
			svc.
				Return(nil, fmt.Errorf("Data is not found")).
				Once()

			req := httptest.NewRequest(
				http.MethodDelete,
				fmt.Sprintf("/survey/%v", utils.DummyID),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.DeleteByID(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	test.Run("GetMany API", func(t *testing.T) {
		svc := mockService.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyFilter"),
		)

		t.Run("Success", func(t *testing.T) {
			output := reqBody.ToRepo()
			svc.
				Return([]domain.Survey{output}, 1, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey?page=1&page_size=10"),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, 0, fmt.Errorf("Error GetMany service")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey?page=1&page_size=10"),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetMany(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})
	})

	test.Run("GetManyNested API", func(t *testing.T) {
		svc := mockService.On(
			"GetManyNested",
			mock.Anything,
			mock.AnythingOfType("*domain.SurveyFilterNested"),
		)

		t.Run("Success", func(t *testing.T) {
			svc.
				Return([]domain.SurveyResponseNested{sharedResponseNested}, 1, nil).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey/nested?page=1&page_size=10"),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			err := handler.GetManyNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("Failed internal server error", func(t *testing.T) {
			svc.
				Return(nil, 0, fmt.Errorf("Error GetMany service")).
				Once()

			req := httptest.NewRequest(
				http.MethodGet,
				fmt.Sprintf("/survey/nested?page=1&page_size=10"),
				nil,
			)

			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			setContext := context.WithValue(c.Request().Context(), "APP_ENV", "local")
			c.SetRequest(c.Request().WithContext(setContext))

			err := handler.GetManyNested(c)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusInternalServerError, rec.Code)
		})
	})
}
