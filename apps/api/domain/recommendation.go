package domain

type RecommendationConcern struct {
	Name  string `json:"name"`
	Label string `json:"label"`
	Score int    `json:"score"`
}

type RecommendationTreatmentMLRequest struct {
	SkinAnalyze   SkinAnalyze                `json:"skin_analyze"`
	TopConcern    []RecommendationConcern    `json:"top_concern"`
	ConcernAnswer ConcernAnswer              `json:"concern_answer"`
	ConcernGroups []*ConcernGroup            `json:"concern_groups"`
	Treatments    *[]TreatmentProductGetMany `json:"treatments"`
}

type RecommendationTreatmentMLResponse struct {
	Text  string      `json:"text"`
	Video string      `json:"video"`
	Data  []Treatment `json:"data"`
}

type GetRecommendationMLRequest struct {
	SASkinConcerns          []string                          `json:"sa_skin_concerns"`
	SurveySkinConcerns      []string                          `json:"survey_skin_concerns"`
	TreatmentIntervals      []RecommendationTreatmentInterval `json:"treatment_intervals"`
	SurveyContraindications []SurveyContraindication          `json:"survey_contraindications"`
}

type RecommendationTreatmentInterval struct {
	Name string `json:"name"`
	Days int    `json:"days"`
}

type SurveyContraindication struct {
	Question string   `json:"question"`
	Answers  []string `json:"answers"`
}

type RecommendedTreatment struct {
	ID                  string   `json:"id"`
	ItemCode            string   `json:"item_code"`
	Name                string   `json:"name"`
	MediaURL            *string  `json:"media_url"`
	ThumbnailURL        *string  `json:"thumbnail_url"`
	Description         string   `json:"description"`
	Categories          []string `json:"categories"`
	Price               int64    `json:"price"`
	Quantity            int      `json:"quantity"`
	TotalScore          float64  `json:"total_score"`
	SolvedConcerns      []string `json:"solved_concerns"`
	IsTopRecommendation bool     `json:"is_top_recommendation"`
}

type RecommendationResponse struct {
	Summary    *string                `json:"summary"`
	Treatments []RecommendedTreatment `json:"treatments"`
}
