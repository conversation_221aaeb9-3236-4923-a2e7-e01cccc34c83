package domain

import (
	"fmt"
	"os"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/rs/zerolog/log"
)

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Password string `json:"password" validate:"required,min=8"`
}

type LoginResponse struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refresh_token"`
}

type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

type JWTClaim struct {
	ID   string   `json:"id" validate:"required,uuid4" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	Role UserRole `json:"role" validate:"required" example:"admin"`
	jwt.RegisteredClaims
}

type RefreshClaim struct {
	ID string `json:"id" validate:"required,uuid4" example:"3fa85f64-5717-4562-b3fc-2c963f66afa6"`
	jwt.RegisteredClaims
}

func GenerateToken(user User) (string, string, error) {
	secret := os.Getenv("SECRET_KEY")
	if secret == "" {
		return "", "", fmt.Errorf("SECRET_KEY environment variable is not set")
	}

	claim := JWTClaim{
		ID:   user.ID,
		Role: user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Minute * 15)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claim)
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		log.Error().Err(err).Msg("Error signing token")
		return "", "", err
	}

	refreshClaims := RefreshClaim{
		ID: user.ID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	rt, err := refreshToken.SignedString([]byte(secret))
	if err != nil {
		log.Error().Err(err).Msg("Error signing refresh token")
		return "", "", err
	}

	return tokenString, rt, nil
}

func VerifyToken(t string) (*RefreshClaim, error) {
	secret := []byte(os.Getenv("SECRET_KEY"))

	token, err := jwt.ParseWithClaims(t, &RefreshClaim{}, func(token *jwt.Token) (any, error) {
		return secret, nil
	})
	if err != nil {
		log.Error().Err(err).Msg("Error signing refresh token")
		return nil, err
	}

	claims, ok := token.Claims.(*RefreshClaim)
	if !ok {
		log.Error().Msg("Error getting claim")
		return nil, err
	}

	return claims, nil
}
