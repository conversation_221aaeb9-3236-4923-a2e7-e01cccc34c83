package domain

type SkinAnalyze struct {
	ID             string   `json:"id"`
	OperatorID     string   `json:"operator_id"`
	Name           string   `json:"name"`
	ActualAge      *int     `json:"actual_age"`
	InputDate      string   `json:"input_date"`
	PhoneNumber    *string  `json:"phone_number"`
	EvaluationRate *int     `json:"evaluation_rate"`
	SkinAge        *int     `json:"skin_age"`
	SkinCondition  *string  `json:"skin_condition"`
	RGBPore        int      `json:"rgb_pore"`
	RGBSpot        int      `json:"rgb_spot"`
	RGBWrinkle     int      `json:"rgb_wrinkle"`
	PLTexture      *int     `json:"pl_texture"`
	UVPorphyrin    *int     `json:"uv_porphyrin"`
	UVPigmentation *int     `json:"uv_pigmentation"`
	UVMoisture     *int     `json:"uv_moisture"`
	SensitiveArea  *int     `json:"sensitive_area"`
	BrownArea      *int     `json:"brown_area"`
	UVDamage       *int     `json:"uv_damage"`
	Suggestion     *string  `json:"suggestion"`
	PathImages     []string `json:"path_images"`
	PathPDF        string   `json:"path_pdf"`
	Timestamp
}

type SkinAnalyzeFilter struct {
	OperatorID string `json:"operator_id" query:"operator_id"`
	Name       string `json:"name" query:"name"`
	SortColumn string `json:"sort_column" query:"sort_column" validate:"required_with=SortOrder,omitempty,oneof=id name created_at updated_at"`
	SortOrder  string `json:"sort_order" query:"sort_order" validate:"required_with=SortColumn,omitempty,oneof=asc desc"`
	Pagination
}

type SkinAnalyzeUploadRequest struct {
	Images     []string `json:"images"`
	Pdf        string   `json:"pdf"`
	OperatorID string   `json:"operator_id"`
}

type SkinAnalyzeMLUploadResponse struct {
	Data *SkinAnalyze `json:"data"`
}
