package domain

type JobQueueStatus string

const (
	JobQueueStatusQueued     JobQueueStatus = "queued"
	JobQueueStatusProcessing JobQueueStatus = "processing"
	JobQueueStatusCompleted  JobQueueStatus = "completed"
	JobQueueStatusFailed     JobQueueStatus = "failed"
)

type FaceAgingJobQueue struct {
	ID            string                    `json:"id"`
	SkinAnalyzeID string                    `json:"skin_analyze_id"`
	Status        JobQueueStatus            `json:"status"`
	Payload       FaceAgingConcernMLRequest `json:"payload"`
	Result        *FaceAgingConcernResponse `json:"response"`
	ErrorMessage  *string                   `json:"error_message"`
	RetryCount    int                       `json:"retry_count"`
	CreatedBy     string                    `json:"created_by"`
	CreatedAt     int64                     `json:"created_at"`
	UpdatedAt     int64                     `json:"updated_at"`
}
