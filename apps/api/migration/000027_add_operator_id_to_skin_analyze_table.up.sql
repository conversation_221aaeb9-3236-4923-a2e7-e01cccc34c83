BEGIN;

DO $$
DECLARE
    v_user_id UUID;
B<PERSON>IN
    SELECT id INTO v_user_id FROM users WHERE email = '<EMAIL>';

    IF v_user_id IS NULL THEN
        ALTER TABLE IF EXISTS skin_analyzes
        ADD COLUMN IF NOT EXISTS operator_id UUID NOT NULL,
        ADD CONSTRAINT skin_analyzes_operator_id_fkey FOREIGN KEY (operator_id) REFERENCES users(id);

    ELSE
        ALTER TABLE IF EXISTS skin_analyzes
        ADD COLUMN IF NOT EXISTS operator_id UUID NULL,
        ADD CONSTRAINT skin_analyzes_operator_id_fkey FOREIGN KEY (operator_id) REFERENCES users(id);

        UPDA<PERSON> skin_analyzes
        SET operator_id = v_user_id;

        ALTER TABLE skin_analyzes
        ALTER COLUMN operator_id SET NOT NULL;
    END IF;

END $$;

COMMIT;
