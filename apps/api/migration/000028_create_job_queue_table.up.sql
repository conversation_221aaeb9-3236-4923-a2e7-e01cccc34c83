CREATE TABLE job_queue (
    id UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    skin_analyze_id UUID NOT NULL REFERENCES skin_analyzes(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'queued',
    payload JSONB NOT NULL,
    result JSONB,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_by UUID NULL REFERENCES users(id),
    created_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric),
    updated_at BIGINT DEFAULT (EXTRACT(epoch FROM now()) * 1000::numeric)
);

-- An index to make polling for new jobs extremely fast
CREATE INDEX idx_job_queue_status ON job_queue (status) WHERE status = 'queued';

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_job_queue_created_at ON job_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_job_queue_updated_at ON job_queue(updated_at);
