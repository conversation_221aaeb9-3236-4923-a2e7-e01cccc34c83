package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	echoSwagger "github.com/swaggo/echo-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/labstack/echo/otelecho"

	"api/domain"
	awsRepo "api/internal/repositories/aws"
	httpRepo "api/internal/repositories/http"
	"api/internal/repositories/postgres"
	"api/internal/rest"
	"api/service"
	"api/utils"

	apiConfig "api/config"
	internalMiddleware "api/internal/rest/middleware"

	_ "api/docs"
)

const (
	DEFAULT_TIMEOUT = 5 * time.Second
	LOCAL           = "local"
)

// @title Aizer API
// @version 1.0
// @description This is the API documentation for the Aizer application.
// @host localhost:8080
// @BasePath /
// @schemes http
// @securityDefinitions.apiKey BearerAuth
// @in header
// @name Authorization
// @description Format value: Bearer xxx
func main() {
	apiConfig.LoadEnv()
	log.Logger = log.With().Caller().Logger()

	dbPool, err := utils.SetupDatabase()
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to setup database: %v", err)
	}

	defer dbPool.Close()

	app_env := os.Getenv("APP_ENV")

	e := setupEcho()

	if app_env == LOCAL {
		e.GET("/swagger/*", echoSwagger.WrapHandler)
	}

	e.GET("/", func(c echo.Context) error {
		return c.JSON(http.StatusOK, domain.SingleResponse[time.Time]{
			Message: "All is well!",
			Data:    time.Now(),
			Status:  "success",
			Code:    http.StatusOK,
		})
	})

	swaggerDocExpansion := echoSwagger.DocExpansion("none")
	e.GET("/docs/*", echoSwagger.EchoWrapHandler(
		swaggerDocExpansion,
	))

	apiV1 := e.Group("/api/v1")

	adminAccess := apiV1.Group(
		"",
		utils.JWTConfig(),
		utils.RequireRoles(domain.Admin),
	)

	adminAndBranchAccess := apiV1.Group(
		"",
		utils.JWTConfig(),
		utils.RequireRoles(domain.Admin, domain.Branch),
	)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt)
	defer stop()

	awsConfig, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		log.Fatal().Err(err).Msgf("unable to load AWS SDK config, %v", err)
	}

	presigner := awsRepo.NewPresigner(awsConfig)

	s3Client := s3.NewFromConfig(awsConfig)
	s3Uploader := manager.NewUploader(s3Client)
	s3Action := awsRepo.NewUploader(s3Client, s3Uploader)

	authRepo := postgres.NewAuthRepository(dbPool)
	userRepo := postgres.NewUserRepository(dbPool)
	userSurveyRepo := postgres.NewUserSurveyRepository(dbPool)
	surveyRepo := postgres.NewSurveyRepository(dbPool)
	parameterSkinEvaluationRepo := postgres.NewParameterSkinEvaluationRepository(
		dbPool,
	)
	treatmentCategoryRepo := postgres.NewTreatmentCategoryRepository(dbPool)
	skinProblemRepo := postgres.NewSkinProblemRepository(dbPool)
	skinProblemIndicationRepo := postgres.NewSkinProblemIndicationRepository(dbPool)
	treatmentIntervalRepo := postgres.NewTreatmentIntervalRepository(dbPool)
	treatmentProductRepo := postgres.NewTreatmentProductRepository(dbPool)
	skinAnalyzeRepo := postgres.NewSkinAnalyzeRepository(dbPool)
	concernAnswerRepo := postgres.NewConcernAnswerRepository(dbPool)
	concernGroupRepo := postgres.NewConcernGroupRepository(dbPool)
	machineSyncLogRepo := postgres.NewMachineSyncLogRepository(dbPool)
	jobQueueRepo := postgres.NewJobQueueRepository(dbPool)

	skinAnalyzeHttp := httpRepo.NewSkinAnalyzeHttp()
	// faceAgingHttp := httpRepo.NewFaceAgingHttp()
	skinDetectionHttp := httpRepo.NewSkinDetectionHttp()
	recommendationHttp := httpRepo.NewRecommendationHttp()
	summaryHttp := httpRepo.NewSummaryHttp()

	authService := service.NewAuthService(authRepo, userRepo)
	mediaService := service.NewMediaService(presigner)
	userService := service.NewUserService(userRepo, s3Action)
	userSurveyService := service.NewUserSurveyService(userSurveyRepo)
	surveyService := service.NewSurveyService(surveyRepo, presigner)
	parameterSkinEvaluationService := service.NewParameterSkinEvaluationService(
		parameterSkinEvaluationRepo,
	)
	treatmentCategoryService := service.NewTreatmentCategoryService(
		treatmentCategoryRepo,
	)
	skinProblemService := service.NewSkinProblemService(skinProblemRepo)
	treatmentIntervalService := service.NewTreatmentIntervalService(
		treatmentIntervalRepo,
	)
	treatmentProductService := service.NewTreatmentProductService(
		treatmentProductRepo,
		presigner,
	)
	skinAnalyzeService := service.NewSkinAnalyzeService(
		skinAnalyzeRepo,
		skinProblemRepo,
		machineSyncLogRepo,
		skinAnalyzeHttp,
		presigner,
	)
	skinProblemIndicationService := service.NewSkinProblemIndicationService(
		skinProblemIndicationRepo,
	)
	faceAgingService := service.NewFaceAgingService(
		skinAnalyzeRepo,
		presigner,
		jobQueueRepo,
	)
	skinDetectionService := service.NewSkinDetectionService(
		skinAnalyzeRepo,
		skinDetectionHttp,
		presigner,
	)
	recommendationService := service.NewRecommendationService(
		skinAnalyzeRepo,
		concernAnswerRepo,
		concernGroupRepo,
		treatmentProductRepo,
		userSurveyRepo,
		recommendationHttp,
		presigner,
		skinProblemRepo,
	)
	summaryService := service.NewSummaryService(summaryHttp, skinAnalyzeRepo, userSurveyRepo, skinProblemRepo)
	machineSyncLogService := service.NewMachineSyncLogService(
		machineSyncLogRepo,
	)

	rest.NewAuthHandler(apiV1, authService)
	rest.NewMediaHandler(apiV1, mediaService)
	rest.NewUserHandler(adminAccess, userService)
	rest.NewPublicUserHandler(apiV1, userService)
	rest.NewUserSurveyHandler(adminAccess, userSurveyService)
	rest.NewPublicUserSurveyHandler(apiV1, userSurveyService)
	rest.NewSurveyHandler(adminAccess, surveyService)
	rest.NewPublicSurveyHandler(apiV1, surveyService)
	rest.NewParameterSkinEvaluationHandler(
		adminAccess,
		parameterSkinEvaluationService,
	)
	rest.NewPublicParameterSkinEvaluationHandler(
		adminAndBranchAccess,
		parameterSkinEvaluationService,
	)
	rest.NewTreatmentCategoryHandler(adminAccess, treatmentCategoryService)
	rest.NewSkinProblemHandler(adminAccess, skinProblemService)
	rest.NewSkinProblemIndicationHandler(adminAccess, skinProblemIndicationService)
	rest.NewTreatmentIntervalHandler(adminAccess, treatmentIntervalService)
	rest.NewTreatmentProductHandler(adminAccess, treatmentProductService)
	rest.NewPublicTreatmentProductHandler(apiV1, treatmentProductService)
	rest.NewSkinAnalyzeHandler(adminAndBranchAccess, skinAnalyzeService)
	rest.NewPublicSkinAnalyzeHandler(apiV1, skinAnalyzeService)
	rest.NewFaceAgingHandler(adminAndBranchAccess, faceAgingService)
	rest.NewPublicFaceAgingHandler(apiV1, faceAgingService)
	rest.NewSkinDetectionHandler(apiV1, skinDetectionService)
	rest.NewRecommendationHandler(apiV1, recommendationService)
	rest.NewSummaryHandler(apiV1, summaryService)
	rest.NewMachineSyncLogHandler(apiV1, machineSyncLogService)

	startServer(ctx, e)
}

func setupEcho() *echo.Echo {
	e := echo.New()

	e.Use(otelecho.Middleware("aizer-api"))
	e.Use(middleware.Recover())
	e.Use(internalMiddleware.CORSConfig())
	e.Use(internalMiddleware.SetRequestContextWithTimeout(DEFAULT_TIMEOUT))

	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	e.Use(middleware.RequestLoggerWithConfig(middleware.RequestLoggerConfig{
		LogURI:    true,
		LogStatus: true,
		LogValuesFunc: func(c echo.Context, v middleware.RequestLoggerValues) error {
			logger.Info().
				Str("URI", v.URI).
				Int("Status", v.Status).
				Msg("Request")

			return nil
		},
	}))

	e.Validator = utils.NewCustomValidator()

	return e
}

func startServer(ctx context.Context, e *echo.Echo) {
	// Get host from environment variable, default to 127.0.0.1 if not set
	host := os.Getenv("HOST")
	if host == "" {
		host = "127.0.0.1"
	}

	// Get port from environment variable, default to 8080 if not set
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	// Server address and port to listen on
	serverAddr := fmt.Sprintf("%s:%s", host, port)

	go func() {
		e.Logger.Infof("Server starting on %s", serverAddr)
		if err := e.Start(serverAddr); err != nil && err != http.ErrServerClosed {
			e.Logger.Fatal("shutting down the server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server with a timeout of 10 seconds.
	<-ctx.Done()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := e.Shutdown(ctx); err != nil {
		e.Logger.Fatal(err)
	}
}
