package seed

import (
	"context"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"api/domain"
)

func SeedSurveyQuestions(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding survey questions...")

	var (
		totalCount         int
		allParentQuestions []domain.Survey
		allChildQuestions  []domain.Survey

		staticParentQuestions, staticChildQuestions   = surveyQuestionStaticData()
		dynamicParentQuestions, dynamicChildQuestions = surveyQuestionDynamicData()
	)

	ctx := context.Background()
	err := dbPool.
		QueryRow(ctx, "SELECT count(*) as total FROM survey_questions").
		Scan(&totalCount)

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding survey questions already exist...")
		return
	}

	for i := 0; i < len(staticParentQuestions); i++ {
		allParentQuestions = append(allParentQuestions, staticParentQuestions[i])
	}

	for i := 0; i < len(staticChildQuestions); i++ {
		allChildQuestions = append(allChildQuestions, staticChildQuestions[i])
	}

	for i := 0; i < len(dynamicParentQuestions); i++ {
		allParentQuestions = append(allParentQuestions, dynamicParentQuestions[i])
	}

	for i := 0; i < len(dynamicChildQuestions); i++ {
		allChildQuestions = append(allChildQuestions, dynamicChildQuestions[i])
	}

	parentRowSrc := pgx.CopyFromSlice(
		len(allParentQuestions),
		func(i int) ([]any, error) {
			return []any{
				allParentQuestions[i].ID,
				allParentQuestions[i].Description,
				allParentQuestions[i].Question,
				allParentQuestions[i].Answers,
				allParentQuestions[i].IsMultiple,
				allParentQuestions[i].Type,
				allParentQuestions[i].QuestionOrder,
				allParentQuestions[i].IsStatic,
				allParentQuestions[i].Category,
			}, nil
		},
	)

	childRowSrc := pgx.CopyFromSlice(
		len(allChildQuestions),
		func(i int) ([]any, error) {
			return []any{
				allChildQuestions[i].ID,
				allChildQuestions[i].ParentQuestionID,
				allChildQuestions[i].ParentQuestionAnswer,
				allChildQuestions[i].Description,
				allChildQuestions[i].Question,
				allChildQuestions[i].Answers,
				allChildQuestions[i].IsMultiple,
				allChildQuestions[i].Type,
				allChildQuestions[i].QuestionOrder,
				allChildQuestions[i].IsStatic,
				allChildQuestions[i].Category,
			}, nil
		},
	)

	parentCount, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"survey_questions"},
		[]string{
			"id",
			"description",
			"question",
			"answers",
			"is_multiple",
			"type",
			"question_order",
			"is_static",
			"category",
		},
		parentRowSrc,
	)

	if err != nil {
		log.Printf("[SEEDER]: Error insert parent questions: %v\n", err)
		return
	}

	childCount, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"survey_questions"},
		[]string{
			"id",
			"parent_question_id",
			"parent_question_answer",
			"description",
			"question",
			"answers",
			"is_multiple",
			"type",
			"question_order",
			"is_static",
			"category",
		},
		childRowSrc,
	)

	if err != nil {
		log.Printf("[SEEDER]: Error insert child questions: %v\n", err)
		return
	}

	log.Printf(
		"[SEEDER]: Seeding %d parent and %d child survey questions succeed...",
		parentCount,
		childCount,
	)
}
