package seed

import (
	"api/domain"
)

func surveyQuestionDynamicData() ([]domain.Survey, []domain.Survey) {
	var (
		firstParentQuestionID      = "614204d0-761a-4c38-887a-78f408b35b62"
		secondParentQuestionID     = "9d68eb3c-9783-4dae-88d4-ca8b03a59157"
		thirdParentQuestionID      = "9dc3fcbf-dbb4-44e3-ac95-94bd737cbe43"
		fourthParentQuestionID     = "a538b2b9-9f76-4862-8ce6-09f64cc74920"
		fifthParentQuestionID      = "cdcdc205-e7c0-4000-acb1-abb2d017c507"
		sixthParentQuestionID      = "2984abc2-8319-4aa0-a280-074e77c41883"
		seventhParentQuestionID    = "c7b48c3a-f7a7-443b-9ff2-38adb918defd"
		eighthParentQuestionID     = "07724c0d-1262-42fd-949e-e7aca79152e7"
		ninthParentQuestionID      = "76853884-7d24-437e-9e21-49a298bca4b9"
		tenthParentQuestionID      = "40ed04c6-0759-4ab9-90ec-af22ed55e90b"
		eleventhParentQuestionID   = "a5462568-96cb-4d62-8bc3-6ea19fc5a4e5"
		twelfthParentQuestionID    = "a4d5b5b9-50b1-4068-9b79-9682117f962a"
		thirteenthParentQuestionID = "664f12fe-3f94-4a4a-a2c8-1983433c4d6d"
		fourteenthParentQuestionID = "7843c1b8-59b0-4d17-bcb7-30be8b47ea32"
		fifteenthParentQuestionID  = "d1aae3a3-03c6-4c98-acd2-e02277da6c50"
		sixteenthParentQuestionID  = "0f428c82-f817-4b0b-bf47-1074c63943f0"
	)

	parentQuestions := []domain.Survey{
		{
			ID:                   firstParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui produk yang digunakan dan kebiasaan perawatan sehari-hari.",
			Question:             "What skincare products are you currently using?",
			Answers: []domain.SurveyAnswer{
				{Title: "Just facial cleanser", Description: nil, ImageUrl: nil},
				{Title: "I have already used serum and moisturizer", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 6,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   secondParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Menggali faktor eksternal yang dapat memengaruhi kondisi kulit.",
			Question:             "Are you frequently exposed to sunlight or pollution?",
			Answers: []domain.SurveyAnswer{
				{
					Title:       "Yes, I often do outdoor activities",
					Description: nil,
					ImageUrl:    nil,
				},
				{
					Title:       "No, I spend more time indoors with air conditioning",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 7,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   thirdParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk menyesuaikan rekomendasi produk dengan kebutuhan pelanggan.",
			Question:             "Do you have any specific preferences for skincare products, such as natural ingredients or fragrance-free?",
			Answers: []domain.SurveyAnswer{
				{Title: "I prefer products with natural ingredients", Description: nil, ImageUrl: nil},
				{
					Title:       "I don’t mind, as long as it’s effective",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 8,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   fourthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you have a salmon allergy?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{
					Title:       "No, I haven't experienced any allergy issues so far",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 9,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fifthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you have a nut allergy?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{
					Title:       "No, I haven't experienced any allergy issues so far",
					Description: nil,
					ImageUrl:    nil,
				},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 10,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   sixthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Are you currently taking any hormonal medications (e.g., birth control or hormone therapy)?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 11,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   seventhParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Are you currently taking any antibiotics?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 12,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   eighthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Are you currently taking any blood thinners (within the past 3 months)?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 13,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   sixteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Are you currently taking Isotretinoin?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 14,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   ninthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Are you currently breastfeeding?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 15,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   tenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you have a history of autoimmune disease?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 16,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   eleventhParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you currently have uncontrolled diabetes?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 17,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   twelfthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you have a history of high cholesterol?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 18,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   thirteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you have a history of cancer?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 19,
			IsStatic:      false,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fourteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you have a history of high blood pressure?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 20,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   fifteenthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "Do you have a healthy eating pattern?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 21,
			IsStatic:      false,
			Category:      domain.Informational,
		},
	}

	childQuestions := []domain.Survey{
		{
			ID:                   "b07691c5-b4a8-47ee-b76a-6da7bed30750",
			ParentQuestionID:     &firstParentQuestionID,
			ParentQuestionAnswer: ToPointer(1),
			Description:          "Untuk mengetahui produk yang digunakan dan kebiasaan perawatan sehari-hari.",
			Question:             "Are you using any active ingredients? (e.g., AHA, BHA, Retinol)",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 0,
			IsStatic:      false,
			Category:      domain.Informational,
		},
		{
			ID:                   "cadfb289-fed7-4c3c-84d3-8b2c05bc54dd",
			ParentQuestionID:     &fifteenthParentQuestionID,
			ParentQuestionAnswer: ToPointer(1),
			Description:          "Untuk mengetahui apakah ada kondisi medis yang perlu dipertimbangkan.",
			Question:             "What kind of unhealthy food do you eat?",
			Answers: []domain.SurveyAnswer{
				{Title: "Sweet Food", Description: nil, ImageUrl: nil},
				{Title: "Spicy Food", Description: nil, ImageUrl: nil},
				{Title: "High-Protein Food", Description: nil, ImageUrl: nil},
				{Title: "Nuts", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 0,
			IsStatic:      false,
			Category:      domain.Informational,
		},
	}

	return parentQuestions, childQuestions
}

func surveyQuestionStaticData() ([]domain.Survey, []domain.Survey) {
	var (
		firstParentQuestionID  = "1f04ea29-b556-4793-80ca-f0314f49917d"
		secondParentQuestionID = "2097dda1-544a-4d7a-89c6-6314bcefa592"
		thirdParentQuestionID  = "959ac8b0-b1a7-4da6-beaf-70063c83fc53"
		fourthParentQuestionID = "94e22537-8ff3-4702-ac26-87c0a60139cb"
		fifthParentQuestionID  = "cef51648-1f44-43dd-944f-a677d37f8db4"
		sixthParentQuestionID  = "4864e913-0c7f-458f-9b19-dd091ccb254e"
	)

	parentQuestions := []domain.Survey{
		{
			ID:                   firstParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk melanjutkan pada rekomendasi treatment atau tidak.",
			Question:             "Are you currently pregnant or undergoing cancer treatment (chemotherapy, immunotherapy, or radiotherapy)?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 0,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   secondParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Please select your skin type",
			Answers: []domain.SurveyAnswer{
				{Title: "Normal", Description: nil, ImageUrl: nil},
				{Title: "Combination", Description: nil, ImageUrl: nil},
				{Title: "Oily", Description: nil, ImageUrl: nil},
				{Title: "Dehydration", Description: nil, ImageUrl: nil},
				{Title: "Sensitive", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 1,
			IsStatic:      true,
			Category:      domain.Informational,
		},
		{
			ID:                   thirdParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Do you have any specific skin concerns you would like to address?",
			Answers: []domain.SurveyAnswer{
				{Title: "Wrinkle", Description: nil, ImageUrl: nil},
				{Title: "Acne", Description: nil, ImageUrl: nil},
				{Title: "Pores", Description: nil, ImageUrl: nil},
				{Title: "Scar", Description: nil, ImageUrl: nil},
				{Title: "Redness", Description: nil, ImageUrl: nil},
				{Title: "Pigmentation", Description: nil, ImageUrl: nil},
				{Title: "No Concern", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 2,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fourthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Do you have any specific concerns?",
			Answers: []domain.SurveyAnswer{
				{Title: "Milia / Wartz", Description: nil, ImageUrl: nil},
				{Title: "Chubby Cheeks / Double Chin", Description: nil, ImageUrl: nil},
				{Title: "Dark Lips", Description: nil, ImageUrl: nil},
				{Title: "Dark Circle", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 3,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   fifthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Do you have any other goals?",
			Answers: []domain.SurveyAnswer{
				{Title: "Lifting", Description: nil, ImageUrl: nil},
				{Title: "Contouring", Description: nil, ImageUrl: nil},
				{Title: "Enhancement & Filling", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 4,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   sixthParentQuestionID,
			ParentQuestionID:     nil,
			ParentQuestionAnswer: nil,
			Description:          "Untuk menyesuaikan rekomendasi produk dengan kebutuhan pelanggan.",
			Question:             "Have you undergone any treatments in the past 6 months?",
			Answers: []domain.SurveyAnswer{
				{Title: "Yes, I have undergone treatment", Description: nil, ImageUrl: nil},
				{Title: "No", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SingleFull,
			QuestionOrder: 5,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
	}

	secondChildQuestions := []domain.Survey{
		{
			ID:                   "93246a58-b465-438f-a6af-d6d8c9ad5de7",
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(0),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Please Select The Area",
			Answers: []domain.SurveyAnswer{
				{Title: "Forehead - Nose", Description: nil, ImageUrl: nil},
				{Title: "Nose - Mouth", Description: nil, ImageUrl: nil},
				{Title: "Mouth - Chin", Description: nil, ImageUrl: nil},
				{Title: "Entire Face Area", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 0,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   "bc89319e-08aa-4b87-b266-ae06f56ea263",
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(1),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Please Select The Area",
			Answers: []domain.SurveyAnswer{
				{Title: "Forehead - Nose", Description: nil, ImageUrl: nil},
				{Title: "Nose - Mouth", Description: nil, ImageUrl: nil},
				{Title: "Mouth - Chin", Description: nil, ImageUrl: nil},
				{Title: "Entire Face Area", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 1,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   "20c125b2-996a-4741-b450-e08d7e59c2ee",
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(2),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Please Select The Area",
			Answers: []domain.SurveyAnswer{
				{Title: "Forehead - Nose", Description: nil, ImageUrl: nil},
				{Title: "Nose - Mouth", Description: nil, ImageUrl: nil},
				{Title: "Mouth - Chin", Description: nil, ImageUrl: nil},
				{Title: "Entire Face Area", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 2,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   "1cdf39e8-63fa-45c5-869b-7baf3dc16380",
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(3),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Please Select The Area",
			Answers: []domain.SurveyAnswer{
				{Title: "Forehead - Nose", Description: nil, ImageUrl: nil},
				{Title: "Nose - Mouth", Description: nil, ImageUrl: nil},
				{Title: "Mouth - Chin", Description: nil, ImageUrl: nil},
				{Title: "Entire Face Area", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 3,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   "bc074122-be2f-4fd2-b3dd-953b7ae451af",
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(4),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Please Select The Area",
			Answers: []domain.SurveyAnswer{
				{Title: "Forehead - Nose", Description: nil, ImageUrl: nil},
				{Title: "Nose - Mouth", Description: nil, ImageUrl: nil},
				{Title: "Mouth - Chin", Description: nil, ImageUrl: nil},
				{Title: "Entire Face Area", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 4,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
		{
			ID:                   "e72a5c44-5a35-4aef-9cc6-926692686aac",
			ParentQuestionID:     &thirdParentQuestionID,
			ParentQuestionAnswer: ToPointer(5),
			Description:          "Bertujuan untuk memahami jenis dan masalah kulit pelanggan.",
			Question:             "Please Select The Area",
			Answers: []domain.SurveyAnswer{
				{Title: "Forehead - Nose", Description: nil, ImageUrl: nil},
				{Title: "Nose - Mouth", Description: nil, ImageUrl: nil},
				{Title: "Mouth - Chin", Description: nil, ImageUrl: nil},
				{Title: "Entire Face Area", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    true,
			Type:          domain.MultipleFull,
			QuestionOrder: 5,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
	}

	fourthChildQuestion := []domain.Survey{
		{
			ID:                   "7ca84344-52f7-4e8b-a95b-1ad1412f585a",
			ParentQuestionID:     &sixthParentQuestionID,
			ParentQuestionAnswer: ToPointer(0),
			Description:          "Untuk menyesuaikan rekomendasi produk dengan kebutuhan pelanggan.",
			Question:             "What treatments have you undergone and when did you have them?",
			Answers: []domain.SurveyAnswer{
				{Title: "treatment-interval", Description: nil, ImageUrl: nil},
			},
			IsMultiple:    false,
			Type:          domain.SurveyDropdown,
			QuestionOrder: 0,
			IsStatic:      true,
			Category:      domain.Contraindication,
		},
	}

	childQuestions := append(secondChildQuestions, fourthChildQuestion...)

	return parentQuestions, childQuestions
}
