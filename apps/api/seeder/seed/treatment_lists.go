package seed

import (
	"context"
	"log"
	"slices"
	"strconv"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"api/domain"
	"api/utils"
)

func getCategoryIDs(
	ctx context.Context,
	dbPool *pgxpool.Pool,
	categoryNames []string,
) []string {
	var names []string

	for i := 0; i < len(categoryNames); i++ {
		name := cases.
			Title(language.English).
			String(categoryNames[i])

		names = append(names, name)
	}

	categoryRows, err := dbPool.Query(
		ctx,
		`
		SELECT id FROM treatment_categories WHERE name = any ($1)
		ORDER BY
		array_position($1, treatment_categories.name)
		`,
		names,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed running category query for %s: %v",
			categoryNames,
			err,
		)
		return nil
	}

	categoryIDs, err := pgx.CollectRows(
		categoryRows,
		func(row pgx.CollectableRow) (string, error) {
			var id string
			err := row.Scan(&id)
			return id, err
		},
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed to get category results for %s: %v",
			categoryNames,
			err,
		)
		return nil
	}

	return categoryIDs
}

func getIntervalID(
	ctx context.Context,
	dbPool *pgxpool.Pool,
	intervalDays int,
) *string {
	var intervalID *string

	err := dbPool.QueryRow(
		ctx,
		"SELECT id FROM treatment_intervals WHERE days = $1",
		intervalDays,
	).Scan(&intervalID)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed searching interval %d: %v",
			intervalDays,
			err,
		)
	}

	return intervalID
}

func getIndicationIDs(
	ctx context.Context,
	dbPool *pgxpool.Pool,
	indicationNames []string,
) []string {
	var names []string

	for i := 0; i < len(indicationNames); i++ {
		name := cases.
			Title(language.English).
			String(indicationNames[i])

		names = append(names, name)
	}

	rows, err := dbPool.Query(
		ctx,
		`
		SELECT id FROM skin_problems WHERE name = any ($1)
		ORDER BY
		array_position($1, skin_problems.name)
		`,
		names,
	)

	indicationID, err := pgx.CollectRows(
		rows,
		func(row pgx.CollectableRow) (string, error) {
			var id string
			err := row.Scan(&id)
			return id, err
		},
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed searching indication %v: %v",
			indicationNames,
			err,
		)
	}

	return indicationID
}

func getSurveyData(
	ctx context.Context,
	dbPool *pgxpool.Pool,
	surveyQuestions []string,
	itemCode string,
) []domain.TreatmentProductSurveyQuestion {
	var (
		inputIDs []string
		data     domain.TreatmentProductSurveyQuestionJSON

		questionOrder = 0
	)

	// surveyQuestions is expected to have question id and question index answer.
	// This is to sanitize invalid format.
	for bundle := range slices.Chunk(surveyQuestions, 2) {
		var (
			id          = bundle[0]
			indexAnswer = bundle[1]
		)

		if id == "" {
			log.Printf(
				"[WARNING]: SKIP question order %d in item code %s: survey question id is empty",
				questionOrder,
				itemCode,
			)
			continue
		}

		if indexAnswer == "" {
			log.Printf(
				"[WARNING]: SKIP question order %d in item code %s: survey question index answer is empty",
				questionOrder,
				itemCode,
			)
			continue
		}

		selectedAnswer, err := strconv.Atoi(indexAnswer)

		if err != nil {
			log.Printf(
				"[ERROR]: Failed parsing index selected answer for survey question id %s",
				id,
			)
			continue
		}

		inputIDs = append(inputIDs, id)

		data = append(
			data,
			domain.TreatmentProductSurveyQuestion{
				ID:             id,
				SelectedAnswer: selectedAnswer,
			},
		)

		questionOrder++
	}

	rows, err := dbPool.Query(
		ctx,
		"SELECT id FROM survey_questions WHERE id = any ($1)",
		inputIDs,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed searching survey question ids %v: %v",
			inputIDs,
			err,
		)
		return nil
	}

	selectedIDs, err := pgx.CollectRows(
		rows,
		func(row pgx.CollectableRow) (string, error) {
			var id string
			err := row.Scan(&id)
			return id, err
		},
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed collecting survey question data %v: %v",
			inputIDs,
			err,
		)
		return nil
	}

	if len(selectedIDs) != len(inputIDs) {
		notFoundIDs := utils.DiffIDs(inputIDs, selectedIDs)

		log.Printf(
			"[WARNING]: Survey IDs not found: %v",
			strings.Join(notFoundIDs, ", "),
		)

		for i := 0; i < len(notFoundIDs); i++ {
			// Remove not found IDs.
			if data[i].ID == notFoundIDs[i] {
				data = slices.Delete(data, i, i+1)
			}
		}
	}

	return data
}

func SeedTreatmentLists(dbPool *pgxpool.Pool) {
	log.Println("[SEEDER]: Seeding treatment lists...")

	ctx := context.Background()
	var totalCount int

	err := dbPool.QueryRow(
		ctx,
		"SELECT COUNT(*) as total FROM treatment_products WHERE type = 'treatment'",
	).Scan(&totalCount)

	if err != nil {
		log.Println("[ERROR]: Failed scanning row: ", err)
		return
	}

	if totalCount > 0 {
		log.Println("[SEEDER]: Seeding treatment lists already exist...")
		return
	}

	filePath := "./seeder/assets/treatment_lists.csv"
	contents := readCsv(filePath, ',')

	if contents == nil {
		log.Println("[SEEDER]: No treatment lists to seed")
		return
	}

	treatmentsData := make([]domain.TreatmentProduct, len(contents)-1)

	type treatmentSurvey struct {
		TreatmentProductID string `json:"treatment_product_id"`
		domain.TreatmentProductSurveyQuestion
	}

	var (
		treatmentSurveyQuestions []treatmentSurvey
		treatmentIndicationsData []domain.TreatmentProductIndication
		treatmentCategoriesData  []domain.TreatmentProductCategory
	)

	for i := 0; i < len(contents); i++ {
		if i == 0 {
			continue // Skip header row
		}

		var (
			id                   string
			nullableMediaUrl     *string
			nullableThumbnailUrl *string
			nullableNotes        *string
		)

		var (
			contentType          = domain.TreatmentType
			itemCode             = contents[i][0]
			name                 = contents[i][1]
			description          = contents[i][2]
			category             = contents[i][3:6]
			mediaUrl             = contents[i][6]
			thumbnailUrl         = contents[i][7]
			concern              = contents[i][8:14]
			notes                = contents[i][14]
			interval             = contents[i][15]
			price                = contents[i][16]
			quantity             = contents[i][17]
			surveyQuestionsInput = contents[i][18:]
		)

		if mediaUrl != "" {
			nullableMediaUrl = &mediaUrl
		}

		if thumbnailUrl != "" {
			nullableThumbnailUrl = &thumbnailUrl
		}

		if notes != "" {
			nullableNotes = &notes
		}

		finalPrice, err := strconv.ParseInt(price, 10, 64)
		if err != nil {
			log.Printf(
				"[ERROR]: Failed parsing price for item code %s: %v",
				itemCode,
				err,
			)
			continue
		}

		finalQuantity, err := strconv.Atoi(quantity)
		if err != nil {
			log.Printf(
				"[ERROR]: Failed parsing quantity for item code %s: %v",
				itemCode,
				err,
			)
			continue
		}

		err = dbPool.QueryRow(ctx, "SELECT gen_random_uuid()").Scan(&id)
		if err != nil {
			log.Printf(
				"[ERROR]: Failed generate id for item code %s: %v",
				itemCode,
				err,
			)
		}

		categoryIDs := getCategoryIDs(ctx, dbPool, category)

		for j := 0; j < len(categoryIDs); j++ {
			treatmentCategoriesData = append(
				treatmentCategoriesData,
				domain.TreatmentProductCategory{
					TreatmentProductID:  id,
					TreatmentCategoryID: categoryIDs[j],
					CategoryOrder:       j,
				},
			)
		}

		intervalDays := parseTreatmentInterval(interval)

		if intervalDays == nil {
			log.Printf(
				"[WARNING]: SKIP item code %s: cannot parse interval %s",
				itemCode,
				interval,
			)
			continue
		}

		intervalID := getIntervalID(ctx, dbPool, *intervalDays)

		if intervalID == nil {
			log.Printf(
				"[WARNING]: SKIP item code %s: interval id for %s is not found",
				itemCode,
				interval,
			)
			continue
		}

		indicationIDs := getIndicationIDs(ctx, dbPool, concern)

		for j := 0; j < len(indicationIDs); j++ {
			treatmentIndicationsData = append(
				treatmentIndicationsData,
				domain.TreatmentProductIndication{
					TreatmentProductID:    id,
					TreatmentIndicationID: indicationIDs[j],
					IndicationOrder:       j,
				},
			)
		}

		surveyData := getSurveyData(
			ctx,
			dbPool,
			surveyQuestionsInput,
			itemCode,
		)

		for j := 0; j < len(surveyData); j++ {
			treatmentSurveyQuestions = append(
				treatmentSurveyQuestions,
				treatmentSurvey{
					TreatmentProductID:             id,
					TreatmentProductSurveyQuestion: surveyData[j],
				},
			)
		}

		treatmentsData[i-1] = domain.TreatmentProduct{
			ID:           id,
			Type:         contentType,
			ItemCode:     itemCode,
			Name:         name,
			Description:  description,
			Price:        finalPrice,
			MediaUrl:     nullableMediaUrl,
			ThumbnailUrl: nullableThumbnailUrl,
			Notes:        nullableNotes,
			Quantity:     finalQuantity,
			IntervalID:   intervalID,
		}
	}

	treatmentRowSrc := pgx.CopyFromSlice(
		len(treatmentsData),
		func(i int) ([]any, error) {
			return []any{
				treatmentsData[i].ID,
				treatmentsData[i].ItemCode,
				treatmentsData[i].Name,
				treatmentsData[i].Type,
				treatmentsData[i].Description,
				treatmentsData[i].IntervalID,
				treatmentsData[i].Price,
				treatmentsData[i].MediaUrl,
				treatmentsData[i].ThumbnailUrl,
				treatmentsData[i].Notes,
				treatmentsData[i].Quantity,
			}, nil
		},
	)

	treatmentCategoryRowSrc := pgx.CopyFromSlice(
		len(treatmentCategoriesData),
		func(i int) ([]any, error) {
			return []any{
				treatmentCategoriesData[i].TreatmentProductID,
				treatmentCategoriesData[i].TreatmentCategoryID,
				treatmentCategoriesData[i].CategoryOrder,
			}, nil
		},
	)

	treatmentIndicationRowSrc := pgx.CopyFromSlice(
		len(treatmentIndicationsData),
		func(i int) ([]any, error) {
			return []any{
				treatmentIndicationsData[i].TreatmentProductID,
				treatmentIndicationsData[i].TreatmentIndicationID,
				treatmentIndicationsData[i].IndicationOrder,
			}, nil
		},
	)

	treatmentSurveyRowSrc := pgx.CopyFromSlice(
		len(treatmentSurveyQuestions),
		func(i int) ([]any, error) {
			return []any{
				treatmentSurveyQuestions[i].TreatmentProductID,
				treatmentSurveyQuestions[i].ID,
				treatmentSurveyQuestions[i].SelectedAnswer,
			}, nil
		},
	)

	count, err := dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products"},
		[]string{
			"id",
			"item_code",
			"name",
			"type",
			"description",
			"interval_id",
			"price",
			"media_url",
			"thumbnail_url",
			"notes",
			"quantity",
		},
		treatmentRowSrc,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed CopyFrom in treatment list, here's why: %v",
			err,
		)
		return
	}

	_, err = dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products_categories"},
		[]string{
			"treatment_product_id",
			"treatment_category_id",
			"category_order",
		},
		treatmentCategoryRowSrc,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed CopyFrom in treatment list category, here's why: %v",
			err,
		)
		return
	}

	_, err = dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products_indications"},
		[]string{
			"treatment_product_id",
			"treatment_indication_id",
			"indication_order",
		},
		treatmentIndicationRowSrc,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed CopyFrom in treatment list indication, here's why: %v",
			err,
		)
		return
	}

	_, err = dbPool.CopyFrom(
		ctx,
		pgx.Identifier{"treatment_products_surveys"},
		[]string{
			"treatment_product_id",
			"survey_id",
			"selected_answer",
		},
		treatmentSurveyRowSrc,
	)

	if err != nil {
		log.Printf(
			"[ERROR]: Failed CopyFrom in treatment list survey, here's why: %v",
			err,
		)
		return
	}

	log.Printf(
		"[SEEDER]: Seeding %d treatment lists succeed...",
		count,
	)
}
