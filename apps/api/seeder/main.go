package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/rs/zerolog/log"

	awsRepo "api/internal/repositories/aws"
	seeder "api/seeder/seed"
	"api/service"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"
)

func main() {
	loadEnv()
	dbPool, err := setupDatabase()
	if err != nil {
		fmt.Printf("failed to set up database: %v \n", err)
		return
	}

	defer dbPool.Close()

	ctx := context.Background()

	awsConfig, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		log.Fatal().Err(err).Msgf("unable to load AWS SDK config, %v", err)
	}

	s3Client := s3.NewFromConfig(awsConfig)
	s3Uploader := manager.NewUploader(s3Client)
	s3Action := awsRepo.NewUploader(s3Client, s3Uploader)

	if err := Seed(dbPool, s3Action); err != nil {
		log.Fatal().Err(err).Msg("Failed to seed database")
		return
	}

	log.Info().Msg("Database seeding completed successfully")
}

func Seed(dbPool *pgxpool.Pool, a service.S3Action) error {
	var timings []TimingResult
	totalStart := time.Now()

	// measure execution time
	timings = append(timings, measureExecutionTime("SeedUsers", func() {
		seeder.SeedUsers(dbPool, a)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatments", func() {
		seeder.SeedTreatments(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedConcernGroups", func() {
		seeder.SeedConcernGroups(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedConcernAnswers", func() {
		seeder.SeedConcernAnswers(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedParameterSkinEvaluations", func() {
		seeder.SeedParameterSkinEvaluations(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSurveyQuestions", func() {
		seeder.SeedSurveyQuestions(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatmentCategories", func() {
		seeder.SeedTreatmentCategories(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSkinProblemIndications", func() {
		seeder.SeedSkinProblemIndications(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSkinProblems", func() {
		seeder.SeedSkinProblems(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedSkinProblemGroups", func() {
		seeder.SeedSkinProblemGroups(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatmentIntervals", func() {
		seeder.SeedTreatmentIntervals(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedTreatmentLists", func() {
		seeder.SeedTreatmentLists(dbPool)
	}))

	timings = append(timings, measureExecutionTime("SeedProductLists", func() {
		seeder.SeedProductLists(dbPool)
	}))

	// timings = append(timings, measureExecutionTime("SeedSkinAnalyzes", func() {
	// 	seeder.SeedSkinAnalyzes(dbPool)
	// }))

	totalDuration := time.Since(totalStart)

	// show the duration results
	fmt.Println("\nSeeder Execution Times:")
	fmt.Println("------------------------")
	for _, timing := range timings {
		fmt.Printf("%s: %v ms\n", timing.Operation, timing.Duration.Milliseconds())
	}
	fmt.Printf("\nTotal Execution Time: %v ms\n", totalDuration.Milliseconds())

	return nil
}

type TimingResult struct {
	Operation string
	Duration  time.Duration
}

// measureExecutionTime
func measureExecutionTime(operation string, fn func()) TimingResult {
	start := time.Now()
	fn()
	duration := time.Since(start)
	return TimingResult{
		Operation: operation,
		Duration:  duration,
	}
}

// loadEnv loads environment variables from a .env file, logging if not found
func loadEnv() {
	if err := godotenv.Load(".env"); err != nil {
		log.Info().Msg("No .env file found, reading configuration from environment variables")
	}
}

// setupDatabase initializes and verifies the database connection
func setupDatabase() (*pgxpool.Pool, error) {
	dbURL := os.Getenv("DATABASE_URL")
	config, err := pgxpool.ParseConfig(dbURL)
	if err != nil {
		return nil, fmt.Errorf("unable to parse database URL: %w", err)
	}

	dbPool, err := pgxpool.NewWithConfig(context.Background(), config)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to database: %w", err)
	}

	if err := dbPool.Ping(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return dbPool, nil
}
