// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"context"
	"io"

	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	mock "github.com/stretchr/testify/mock"
)

// NewS3Action creates a new instance of S3Action. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewS3Action(t interface {
	mock.TestingT
	Cleanup(func())
}) *S3Action {
	mock := &S3Action{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// S3Action is an autogenerated mock type for the S3Action type
type S3Action struct {
	mock.Mock
}

type S3Action_Expecter struct {
	mock *mock.Mock
}

func (_m *S3Action) EXPECT() *S3Action_Expecter {
	return &S3Action_Expecter{mock: &_m.<PERSON>ck}
}

// CreateFolder provides a mock function for the type S3Action
func (_mock *S3Action) CreateFolder(ctx context.Context, bucketName string, folderName string) error {
	ret := _mock.Called(ctx, bucketName, folderName)

	if len(ret) == 0 {
		panic("no return value specified for CreateFolder")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = returnFunc(ctx, bucketName, folderName)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// S3Action_CreateFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFolder'
type S3Action_CreateFolder_Call struct {
	*mock.Call
}

// CreateFolder is a helper method to define mock.On call
//   - ctx
//   - bucketName
//   - folderName
func (_e *S3Action_Expecter) CreateFolder(ctx interface{}, bucketName interface{}, folderName interface{}) *S3Action_CreateFolder_Call {
	return &S3Action_CreateFolder_Call{Call: _e.mock.On("CreateFolder", ctx, bucketName, folderName)}
}

func (_c *S3Action_CreateFolder_Call) Run(run func(ctx context.Context, bucketName string, folderName string)) *S3Action_CreateFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *S3Action_CreateFolder_Call) Return(err error) *S3Action_CreateFolder_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *S3Action_CreateFolder_Call) RunAndReturn(run func(ctx context.Context, bucketName string, folderName string) error) *S3Action_CreateFolder_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFolder provides a mock function for the type S3Action
func (_mock *S3Action) DeleteFolder(ctx context.Context, bucketName string, folderName string) error {
	ret := _mock.Called(ctx, bucketName, folderName)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFolder")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = returnFunc(ctx, bucketName, folderName)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// S3Action_DeleteFolder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFolder'
type S3Action_DeleteFolder_Call struct {
	*mock.Call
}

// DeleteFolder is a helper method to define mock.On call
//   - ctx
//   - bucketName
//   - folderName
func (_e *S3Action_Expecter) DeleteFolder(ctx interface{}, bucketName interface{}, folderName interface{}) *S3Action_DeleteFolder_Call {
	return &S3Action_DeleteFolder_Call{Call: _e.mock.On("DeleteFolder", ctx, bucketName, folderName)}
}

func (_c *S3Action_DeleteFolder_Call) Run(run func(ctx context.Context, bucketName string, folderName string)) *S3Action_DeleteFolder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *S3Action_DeleteFolder_Call) Return(err error) *S3Action_DeleteFolder_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *S3Action_DeleteFolder_Call) RunAndReturn(run func(ctx context.Context, bucketName string, folderName string) error) *S3Action_DeleteFolder_Call {
	_c.Call.Return(run)
	return _c
}

// UploadS3Object provides a mock function for the type S3Action
func (_mock *S3Action) UploadS3Object(ctx context.Context, bucketName string, objectKey string, file io.Reader) (*manager.UploadOutput, error) {
	ret := _mock.Called(ctx, bucketName, objectKey, file)

	if len(ret) == 0 {
		panic("no return value specified for UploadS3Object")
	}

	var r0 *manager.UploadOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, io.Reader) (*manager.UploadOutput, error)); ok {
		return returnFunc(ctx, bucketName, objectKey, file)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, io.Reader) *manager.UploadOutput); ok {
		r0 = returnFunc(ctx, bucketName, objectKey, file)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*manager.UploadOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, io.Reader) error); ok {
		r1 = returnFunc(ctx, bucketName, objectKey, file)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// S3Action_UploadS3Object_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadS3Object'
type S3Action_UploadS3Object_Call struct {
	*mock.Call
}

// UploadS3Object is a helper method to define mock.On call
//   - ctx
//   - bucketName
//   - objectKey
//   - file
func (_e *S3Action_Expecter) UploadS3Object(ctx interface{}, bucketName interface{}, objectKey interface{}, file interface{}) *S3Action_UploadS3Object_Call {
	return &S3Action_UploadS3Object_Call{Call: _e.mock.On("UploadS3Object", ctx, bucketName, objectKey, file)}
}

func (_c *S3Action_UploadS3Object_Call) Run(run func(ctx context.Context, bucketName string, objectKey string, file io.Reader)) *S3Action_UploadS3Object_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(io.Reader))
	})
	return _c
}

func (_c *S3Action_UploadS3Object_Call) Return(uploadOutput *manager.UploadOutput, err error) *S3Action_UploadS3Object_Call {
	_c.Call.Return(uploadOutput, err)
	return _c
}

func (_c *S3Action_UploadS3Object_Call) RunAndReturn(run func(ctx context.Context, bucketName string, objectKey string, file io.Reader) (*manager.UploadOutput, error)) *S3Action_UploadS3Object_Call {
	_c.Call.Return(run)
	return _c
}
