// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package mocks

import (
	"api/domain"
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewJobQueueRepository creates a new instance of JobQueueRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewJobQueueRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *JobQueueRepository {
	mock := &JobQueueRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// JobQueueRepository is an autogenerated mock type for the JobQueueRepository type
type JobQueueRepository struct {
	mock.Mock
}

type JobQueueRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *JobQueueRepository) EXPECT() *JobQueueRepository_Expecter {
	return &JobQueueRepository_Expecter{mock: &_m.Mock}
}

// CreateJobQueue provides a mock function for the type JobQueueRepository
func (_mock *JobQueueRepository) CreateJobQueue(ctx context.Context, uID string, job *domain.FaceAgingJobQueue) (*domain.FaceAgingJobQueue, error) {
	ret := _mock.Called(ctx, uID, job)

	if len(ret) == 0 {
		panic("no return value specified for CreateJobQueue")
	}

	var r0 *domain.FaceAgingJobQueue
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingJobQueue) (*domain.FaceAgingJobQueue, error)); ok {
		return returnFunc(ctx, uID, job)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingJobQueue) *domain.FaceAgingJobQueue); ok {
		r0 = returnFunc(ctx, uID, job)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingJobQueue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *domain.FaceAgingJobQueue) error); ok {
		r1 = returnFunc(ctx, uID, job)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// JobQueueRepository_CreateJobQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateJobQueue'
type JobQueueRepository_CreateJobQueue_Call struct {
	*mock.Call
}

// CreateJobQueue is a helper method to define mock.On call
//   - ctx
//   - uID
//   - job
func (_e *JobQueueRepository_Expecter) CreateJobQueue(ctx interface{}, uID interface{}, job interface{}) *JobQueueRepository_CreateJobQueue_Call {
	return &JobQueueRepository_CreateJobQueue_Call{Call: _e.mock.On("CreateJobQueue", ctx, uID, job)}
}

func (_c *JobQueueRepository_CreateJobQueue_Call) Run(run func(ctx context.Context, uID string, job *domain.FaceAgingJobQueue)) *JobQueueRepository_CreateJobQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*domain.FaceAgingJobQueue))
	})
	return _c
}

func (_c *JobQueueRepository_CreateJobQueue_Call) Return(faceAgingJobQueue *domain.FaceAgingJobQueue, err error) *JobQueueRepository_CreateJobQueue_Call {
	_c.Call.Return(faceAgingJobQueue, err)
	return _c
}

func (_c *JobQueueRepository_CreateJobQueue_Call) RunAndReturn(run func(ctx context.Context, uID string, job *domain.FaceAgingJobQueue) (*domain.FaceAgingJobQueue, error)) *JobQueueRepository_CreateJobQueue_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteJobQueueByID provides a mock function for the type JobQueueRepository
func (_mock *JobQueueRepository) DeleteJobQueueByID(ctx context.Context, id string) (*domain.FaceAgingJobQueue, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteJobQueueByID")
	}

	var r0 *domain.FaceAgingJobQueue
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.FaceAgingJobQueue, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.FaceAgingJobQueue); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingJobQueue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// JobQueueRepository_DeleteJobQueueByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteJobQueueByID'
type JobQueueRepository_DeleteJobQueueByID_Call struct {
	*mock.Call
}

// DeleteJobQueueByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *JobQueueRepository_Expecter) DeleteJobQueueByID(ctx interface{}, id interface{}) *JobQueueRepository_DeleteJobQueueByID_Call {
	return &JobQueueRepository_DeleteJobQueueByID_Call{Call: _e.mock.On("DeleteJobQueueByID", ctx, id)}
}

func (_c *JobQueueRepository_DeleteJobQueueByID_Call) Run(run func(ctx context.Context, id string)) *JobQueueRepository_DeleteJobQueueByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *JobQueueRepository_DeleteJobQueueByID_Call) Return(faceAgingJobQueue *domain.FaceAgingJobQueue, err error) *JobQueueRepository_DeleteJobQueueByID_Call {
	_c.Call.Return(faceAgingJobQueue, err)
	return _c
}

func (_c *JobQueueRepository_DeleteJobQueueByID_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.FaceAgingJobQueue, error)) *JobQueueRepository_DeleteJobQueueByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetJobQueueByID provides a mock function for the type JobQueueRepository
func (_mock *JobQueueRepository) GetJobQueueByID(ctx context.Context, id string) (*domain.FaceAgingJobQueue, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetJobQueueByID")
	}

	var r0 *domain.FaceAgingJobQueue
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*domain.FaceAgingJobQueue, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *domain.FaceAgingJobQueue); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingJobQueue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// JobQueueRepository_GetJobQueueByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetJobQueueByID'
type JobQueueRepository_GetJobQueueByID_Call struct {
	*mock.Call
}

// GetJobQueueByID is a helper method to define mock.On call
//   - ctx
//   - id
func (_e *JobQueueRepository_Expecter) GetJobQueueByID(ctx interface{}, id interface{}) *JobQueueRepository_GetJobQueueByID_Call {
	return &JobQueueRepository_GetJobQueueByID_Call{Call: _e.mock.On("GetJobQueueByID", ctx, id)}
}

func (_c *JobQueueRepository_GetJobQueueByID_Call) Run(run func(ctx context.Context, id string)) *JobQueueRepository_GetJobQueueByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *JobQueueRepository_GetJobQueueByID_Call) Return(faceAgingJobQueue *domain.FaceAgingJobQueue, err error) *JobQueueRepository_GetJobQueueByID_Call {
	_c.Call.Return(faceAgingJobQueue, err)
	return _c
}

func (_c *JobQueueRepository_GetJobQueueByID_Call) RunAndReturn(run func(ctx context.Context, id string) (*domain.FaceAgingJobQueue, error)) *JobQueueRepository_GetJobQueueByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetProcessingJobQueueCount provides a mock function for the type JobQueueRepository
func (_mock *JobQueueRepository) GetProcessingJobQueueCount(ctx context.Context) (*int, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetProcessingJobQueueCount")
	}

	var r0 *int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*int, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *int); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*int)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// JobQueueRepository_GetProcessingJobQueueCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProcessingJobQueueCount'
type JobQueueRepository_GetProcessingJobQueueCount_Call struct {
	*mock.Call
}

// GetProcessingJobQueueCount is a helper method to define mock.On call
//   - ctx
func (_e *JobQueueRepository_Expecter) GetProcessingJobQueueCount(ctx interface{}) *JobQueueRepository_GetProcessingJobQueueCount_Call {
	return &JobQueueRepository_GetProcessingJobQueueCount_Call{Call: _e.mock.On("GetProcessingJobQueueCount", ctx)}
}

func (_c *JobQueueRepository_GetProcessingJobQueueCount_Call) Run(run func(ctx context.Context)) *JobQueueRepository_GetProcessingJobQueueCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *JobQueueRepository_GetProcessingJobQueueCount_Call) Return(n *int, err error) *JobQueueRepository_GetProcessingJobQueueCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *JobQueueRepository_GetProcessingJobQueueCount_Call) RunAndReturn(run func(ctx context.Context) (*int, error)) *JobQueueRepository_GetProcessingJobQueueCount_Call {
	_c.Call.Return(run)
	return _c
}

// ProcessNewJobQueue provides a mock function for the type JobQueueRepository
func (_mock *JobQueueRepository) ProcessNewJobQueue(ctx context.Context) (*domain.FaceAgingJobQueue, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ProcessNewJobQueue")
	}

	var r0 *domain.FaceAgingJobQueue
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*domain.FaceAgingJobQueue, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *domain.FaceAgingJobQueue); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingJobQueue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// JobQueueRepository_ProcessNewJobQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessNewJobQueue'
type JobQueueRepository_ProcessNewJobQueue_Call struct {
	*mock.Call
}

// ProcessNewJobQueue is a helper method to define mock.On call
//   - ctx
func (_e *JobQueueRepository_Expecter) ProcessNewJobQueue(ctx interface{}) *JobQueueRepository_ProcessNewJobQueue_Call {
	return &JobQueueRepository_ProcessNewJobQueue_Call{Call: _e.mock.On("ProcessNewJobQueue", ctx)}
}

func (_c *JobQueueRepository_ProcessNewJobQueue_Call) Run(run func(ctx context.Context)) *JobQueueRepository_ProcessNewJobQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *JobQueueRepository_ProcessNewJobQueue_Call) Return(faceAgingJobQueue *domain.FaceAgingJobQueue, err error) *JobQueueRepository_ProcessNewJobQueue_Call {
	_c.Call.Return(faceAgingJobQueue, err)
	return _c
}

func (_c *JobQueueRepository_ProcessNewJobQueue_Call) RunAndReturn(run func(ctx context.Context) (*domain.FaceAgingJobQueue, error)) *JobQueueRepository_ProcessNewJobQueue_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateJobQueueByID provides a mock function for the type JobQueueRepository
func (_mock *JobQueueRepository) UpdateJobQueueByID(ctx context.Context, id string, updateJob *domain.FaceAgingJobQueue) (*domain.FaceAgingJobQueue, error) {
	ret := _mock.Called(ctx, id, updateJob)

	if len(ret) == 0 {
		panic("no return value specified for UpdateJobQueueByID")
	}

	var r0 *domain.FaceAgingJobQueue
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingJobQueue) (*domain.FaceAgingJobQueue, error)); ok {
		return returnFunc(ctx, id, updateJob)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *domain.FaceAgingJobQueue) *domain.FaceAgingJobQueue); ok {
		r0 = returnFunc(ctx, id, updateJob)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.FaceAgingJobQueue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *domain.FaceAgingJobQueue) error); ok {
		r1 = returnFunc(ctx, id, updateJob)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// JobQueueRepository_UpdateJobQueueByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateJobQueueByID'
type JobQueueRepository_UpdateJobQueueByID_Call struct {
	*mock.Call
}

// UpdateJobQueueByID is a helper method to define mock.On call
//   - ctx
//   - id
//   - updateJob
func (_e *JobQueueRepository_Expecter) UpdateJobQueueByID(ctx interface{}, id interface{}, updateJob interface{}) *JobQueueRepository_UpdateJobQueueByID_Call {
	return &JobQueueRepository_UpdateJobQueueByID_Call{Call: _e.mock.On("UpdateJobQueueByID", ctx, id, updateJob)}
}

func (_c *JobQueueRepository_UpdateJobQueueByID_Call) Run(run func(ctx context.Context, id string, updateJob *domain.FaceAgingJobQueue)) *JobQueueRepository_UpdateJobQueueByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*domain.FaceAgingJobQueue))
	})
	return _c
}

func (_c *JobQueueRepository_UpdateJobQueueByID_Call) Return(faceAgingJobQueue *domain.FaceAgingJobQueue, err error) *JobQueueRepository_UpdateJobQueueByID_Call {
	_c.Call.Return(faceAgingJobQueue, err)
	return _c
}

func (_c *JobQueueRepository_UpdateJobQueueByID_Call) RunAndReturn(run func(ctx context.Context, id string, updateJob *domain.FaceAgingJobQueue) (*domain.FaceAgingJobQueue, error)) *JobQueueRepository_UpdateJobQueueByID_Call {
	_c.Call.Return(run)
	return _c
}
