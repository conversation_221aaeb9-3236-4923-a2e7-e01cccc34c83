package service

import (
	"api/domain"
	"context"
	"sort"

	"api/utils"

	"github.com/rs/zerolog/log"
)

type SummaryHttp interface {
	GetSummary(ctx context.Context, data *domain.GetSummaryMLRequest) (*domain.SummaryResponse, error)
}

type summaryService struct {
	summaryHttp SummaryHttp
	saRepo SkinAnalyzeRepository
	usRepo UserSurveyRepository
	skinProblemRepo SkinProblemRepository
}

func NewSummaryService(summaryHttp SummaryHttp, saRepo SkinAnalyzeRepository, usRepo UserSurveyRepository, skinProblemRepo SkinProblemRepository) *summaryService {
	return &summaryService{summaryHttp, saRepo, usRepo, skinProblemRepo}
}


func (s *summaryService) GetSummary(
	ctx context.Context,
	id string,
) (*domain.SummaryResponse, error) {
	skinAnalyze, err := s.saRepo.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("failed to get skin analyze by id")
		return nil, err
	}

	_, topConcerns := utils.GetTopConcernAndKey(skinAnalyze)


	userSurveys, _, err := s.usRepo.GetMany(ctx, &domain.UserSurveyFilter{
		SkinAnalyzeID: id,
	})

	if err != nil {
		log.Error().Err(err).Msg("failed to get user surveys")
		return nil, err
	}

	var userSurvey *domain.UserSurvey
	if len(userSurveys) > 0 {
		// Sort user surveys by UpdatedAt in descending order to get the latest one
		sort.Slice(userSurveys, func(i, j int) bool {
			return userSurveys[i].UpdatedAt > userSurveys[j].UpdatedAt
		})
		userSurvey = &userSurveys[0]
	}

	skinProblems, _, err := s.skinProblemRepo.GetMany(ctx, &domain.SkinProblemFilter{
		Pagination: domain.Pagination{
			Page: 1,
			PageSize: 100,
		},
	})
	if err != nil {
		log.Error().Err(err).Msg("failed to get skin problems")
		return nil, err
	}

	topConcern := utils.MappingUserConcern(topConcerns, skinProblems)
    userConcern := utils.ExtractConcernAnswersFromUserSurvey(userSurvey)

	mlRequest := domain.GetSummaryMLRequest{
		TopConcern: topConcern,
		UserConcern: &userConcern,
	}

	summary, err := s.summaryHttp.GetSummary(ctx, &mlRequest)
	if err != nil {
		log.Error().Err(err).Msg("failed to get summary")
		return nil, err
	}

	return summary, nil
}
