package service

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/rs/zerolog/log"
)

type mediaService struct {
	presigner Presigner
}

func NewMediaService(
	presigner Presigner,
) *mediaService {
	return &mediaService{presigner}
}

func (service *mediaService) UploadS3(
	ctx context.Context,
	filename string,
) (*string, *string, error) {
	var (
		bucketName = os.Getenv("AWS_S3_BUCKET")

		objectKey = fmt.Sprintf(
			"media/%s/%s",
			time.Now().Format("2006-01-02_15-04-05"),
			filename,
		)
	)

	signedRequest, err := service.presigner.PutObject(
		ctx,
		bucketName,
		objectKey,
		3600,
	)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get presigned URL")
		return nil, nil, err
	}

	return &objectKey, &signedRequest.URL, nil
}
