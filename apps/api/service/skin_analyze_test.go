package service_test

import (
	"api/domain"
	"api/service"
	"api/service/mocks"
	"api/utils"
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
)

func TestSkinAnalyzeService(t *testing.T) {
	var (
		ctx                 = context.Background()
		mockRepo            = new(mocks.SkinAnalyzeRepository)
		mockSkinProblemRepo = new(mocks.SkinProblemRepository)
		mockLogRepo         = new(mocks.MachineSyncLogRepository)
		mockHttp            = new(mocks.SkinAnalyzeHttp)
		mockPresigner       = new(mocks.Presigner)
		svc                 = service.NewSkinAnalyzeService(mockRepo, mockSkinProblemRepo, mockLogRepo, mockHttp, mockPresigner)
	)

	actualAge := 30
	phoneNumber := "1234567890"
	evaluationRate := 5
	skinAge := 30
	skinCondition := "Good"
	plTexture := 75
	uvPorphyrin := 80
	uvPigmentation := 60
	uvMoisture := 70
	sensitiveArea := 70
	brownArea := 80
	uvDamage := 90
	suggestion := "Use sunscreen"
	skinAnalyze := &domain.SkinAnalyze{
		Name:           "John Doe",
		ActualAge:      &actualAge,
		InputDate:      "2023-10-01",
		PhoneNumber:    &phoneNumber,
		EvaluationRate: &evaluationRate,
		SkinAge:        &skinAge,
		SkinCondition:  &skinCondition,
		RGBPore:        75,
		RGBSpot:        50,
		RGBWrinkle:     70,
		PLTexture:      &plTexture,
		UVPorphyrin:    &uvPorphyrin,
		UVPigmentation: &uvPigmentation,
		UVMoisture:     &uvMoisture,
		SensitiveArea:  &sensitiveArea,
		BrownArea:      &brownArea,
		UVDamage:       &uvDamage,
		Suggestion:     &suggestion,
		PathImages:     []string{"image1.jpg", "image2.jpg"},
		PathPDF:        "/path/to/pdf",
	}

	t.Run("Create skin analyze", func(t *testing.T) {
		t.Run("Success", func(t *testing.T) {
			oi := "uuid"
			inputData := &domain.SkinAnalyzeUploadRequest{
				Images:     []string{"image1.jpg", "image2.jpg"},
				Pdf:        "/path/to/pdf",
				OperatorID: oi,
			}

			mockHttp.On(
				"UploadImage",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinAnalyzeUploadRequest"),
			).
				Return(skinAnalyze, nil).
				Once()

			callSA := *skinAnalyze
			callSA.OperatorID = oi
			mockRepo.On(
				"CreateSkinAnalyze",
				mock.Anything,
				&callSA,
			).
				Return(skinAnalyze, nil).
				Once()

			mockLogRepo.On(
				"CreateMachineSyncLog",
				mock.Anything,
				mock.AnythingOfType("*domain.MachineSyncLog"),
			).
				Return(nil, nil).
				Once()

			data, err := svc.CreateSkinAnalyze(
				ctx,
				inputData,
			)

			assert.NoError(t, err)
			assert.NotNil(t, data)
			mockRepo.AssertExpectations(t)
			mockLogRepo.AssertExpectations(t)
			mockHttp.AssertExpectations(t)
		})

		t.Run("Error on log create", func(t *testing.T) {
			mockHttp.On(
				"UploadImage",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinAnalyzeUploadRequest"),
			).
				Return(skinAnalyze, nil).
				Once()

			mockRepo.On(
				"CreateSkinAnalyze",
				mock.Anything,
				mock.AnythingOfType("*domain.SkinAnalyze"),
			).
				Return(skinAnalyze, nil).
				Once()

			mockLogRepo.On(
				"CreateMachineSyncLog",
				mock.Anything,
				mock.AnythingOfType("*domain.MachineSyncLog"),
			).
				Return(nil, fmt.Errorf("Error")).
				Once()

			inputData := &domain.SkinAnalyzeUploadRequest{
				Images: []string{"image1.jpg", "image2.jpg"},
				Pdf:    "/path/to/pdf",
			}
			data, err := svc.CreateSkinAnalyze(
				ctx,
				inputData,
			)

			assert.NoError(t, err)
			assert.NotNil(t, data)
			mockRepo.AssertExpectations(t)
			mockLogRepo.AssertExpectations(t)
			mockHttp.AssertExpectations(t)
		})
	})

	t.Run("Success get skin analyze by id", func(t *testing.T) {
		mockRepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			mock.AnythingOfType("string"),
		).
			Return(skinAnalyze, nil).
			Once()

		presignedHttpRequest := v4.PresignedHTTPRequest{
			URL:    "https://example.com/presigned-url",
			Method: http.MethodGet,
		}

		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil)

		data, err := svc.GetSkinAnalyzeByID(ctx, "123")

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, skinAnalyze.Name, data.Name)
		assert.Equal(t, presignedHttpRequest.URL, skinAnalyze.PathImages[0])
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success get many skin analyzes", func(t *testing.T) {
		filter := &domain.SkinAnalyzeFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}
		selectedFilter := *filter
		oi := "id"
		selectedFilter.OperatorID = oi
		mockRepo.On(
			"GetManySkinAnalyzes",
			mock.Anything,
			&selectedFilter,
		).
			Return([]*domain.SkinAnalyze{skinAnalyze}, 1, nil).
			Once()
		ctx = context.WithValue(ctx, "user_role", domain.Branch)
		ctx = context.WithValue(ctx, "user_id", oi)
		data, total, err := svc.GetManySkinAnalyzes(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, 1, total)
		assert.Equal(t, skinAnalyze.Name, data[0].Name)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success get top concerns skin analyze", func(t *testing.T) {
		ctx := context.Background()
		id := "test-id"

		mockSkinAnalyze := &domain.SkinAnalyze{
			RGBPore:        45,
			RGBSpot:        59,
			RGBWrinkle:     77,
			PLTexture:      utils.IntPtr(55),
			UVPorphyrin:    utils.IntPtr(49),
			UVPigmentation: utils.IntPtr(57),
			UVMoisture:     utils.IntPtr(46),
			SensitiveArea:  utils.IntPtr(69),
			BrownArea:      utils.IntPtr(57),
			UVDamage:       utils.IntPtr(59),
		}
		mockRepo.On("GetSkinAnalyzeByID", mock.Anything, id).
			Return(mockSkinAnalyze, nil).
			Once()

		mockSkinProblems := []domain.SkinProblemResponse{
			{
				ID:   "1",
				Name: "Pore",
				SkinProblemIndications: []domain.SkinProblemIndication{
					{Name: "Pore"}, {Name: "Porphyrin"},
				},
			},
			{
				ID:   "2",
				Name: "Wrinkle",
				SkinProblemIndications: []domain.SkinProblemIndication{
					{Name: "Wrinkle"}, {Name: "Texture"},
				},
			},
			{
				ID:   "3",
				Name: "Scar",
				SkinProblemIndications: []domain.SkinProblemIndication{
					{Name: "Porphyrin"}, {Name: "Texture"},
				},
			},
		}
		mockSkinProblemRepo.On("GetMany", mock.Anything, mock.Anything).
			Return(mockSkinProblems, 3, nil).
			Once()

		result, err := svc.GetTopConcernsSkinAnalyze(ctx, id)

		assert.NoError(t, err)
		assert.Equal(t, []string{"Pore", "Scar"}, result)

		mockRepo.AssertExpectations(t)
		mockSkinProblemRepo.AssertExpectations(t)
	})
}
