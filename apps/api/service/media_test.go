package service_test

import (
	"context"
	"net/http"
	"testing"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/service"
	"api/service/mocks"
)

func TestMediaService(test *testing.T) {
	var (
		ctx           = context.Background()
		mockPresigner = new(mocks.Presigner)
		svc           = service.NewMediaService(mockPresigner)
	)

	filename := "test_file.mp4"

	presignedHttpRequest := v4.PresignedHTTPRequest{
		URL:    "https://example.com/presigned-url",
		Method: http.MethodPut,
	}

	test.Run("Success upload file", func(t *testing.T) {
		mockPresigner.On(
			"PutObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil)

		objectKey, signedUrl, err := svc.UploadS3(ctx, filename)

		assert.NoError(t, err)
		assert.NotNil(t, objectKey)
		assert.NotNil(t, signedUrl)
	})
}
