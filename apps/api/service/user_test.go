package service_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api/domain"
	"api/service"
	"api/service/mocks"
)

func TestUserService(t *testing.T) {
	var (
		ctx        = context.Background()
		mockRepo   = new(mocks.UserRepository)
		mockAction = new(mocks.S3Action)
		svc        = service.NewUserService(mockRepo, mockAction)
	)

	// Test data setup
	password := "test-password"
	user := &domain.User{
		ID:          "test-id",
		Name:        "Test User",
		Email:       "<EMAIL>",
		Role:        domain.Admin,
		PhoneNumber: "08123456789",
		Password:    &password,
	}

	t.Run("Success create user", func(t *testing.T) {
		// Create a copy of the user with hashed password for the mock return
		hashedUser := *user
		hashedPassword := "hashed-password"
		hashedUser.Password = &hashedPassword

		mockRepo.On(
			"Create",
			mock.Anything,
			mock.MatchedBy(func(u *domain.User) bool {
				// Verify that the password is not the original one
				return u.Password != nil && *u.Password != password
			}),
		).
			Return(&hashedUser, nil).
			Once()

		mockAction.On(
			"CreateFolder",
			mock.Anything,
			mock.AnythingOfType("string"),
			fmt.Sprintf("%s/", hashedUser.ID),
		).Return(nil).Once()

		request := &domain.UserRequest{
			Name:        user.Name,
			Email:       user.Email,
			Role:        user.Role,
			PhoneNumber: user.PhoneNumber,
			Password:    &password,
		}

		userToCreate := request.ToRepo()
		data, err := svc.Create(ctx, &userToCreate)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, user.Name, data.Name)
		assert.Equal(t, user.Email, data.Email)
		assert.Equal(t, hashedPassword, *data.Password, "Password should be hashed")
		mockRepo.AssertExpectations(t)
		mockAction.AssertExpectations(t)
	})

	t.Run("Success get user by id", func(t *testing.T) {
		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(user, nil).
			Once()

		data, err := svc.GetByID(ctx, &user.ID)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, user.ID, data.ID)
		assert.Equal(t, user.Name, data.Name)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success update user", func(t *testing.T) {
		updatedUser := *user
		updatedUser.Name = "Updated Name"

		mockRepo.On(
			"GetByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(user, nil).
			Once()

		mockRepo.On(
			"UpdateByID",
			mock.Anything,
			mock.AnythingOfType("*domain.User"),
			mock.AnythingOfType("*domain.User"),
		).
			Return(&updatedUser, nil).
			Once()

		data, err := svc.UpdateByID(ctx, &updatedUser)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, "Updated Name", data.Name)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success get many users", func(t *testing.T) {
		filter := &domain.UserFilter{
			Pagination: domain.Pagination{
				Page:     1,
				PageSize: 10,
			},
		}

		mockRepo.On(
			"GetMany",
			mock.Anything,
			mock.AnythingOfType("*domain.UserFilter"),
		).
			Return([]*domain.User{user}, 1, nil).
			Once()

		data, err := svc.GetMany(ctx, filter)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, 1, data.TotalData)
		assert.Equal(t, 1, data.TotalPages)
		assert.Len(t, data.Content, 1)
		assert.Equal(t, user.Name, data.Content[0].Name)
		mockRepo.AssertExpectations(t)
	})

	t.Run("Success delete user", func(t *testing.T) {
		mockRepo.On(
			"DeleteByID",
			mock.Anything,
			mock.AnythingOfType("*string"),
		).
			Return(user, nil).
			Once()

		mockAction.On(
			"DeleteFolder",
			mock.Anything,
			mock.AnythingOfType("string"),
			fmt.Sprintf("%s/", user.ID),
		).Return(nil).Once()

		data, err := svc.DeleteByID(ctx, &user.ID)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, user.ID, data.ID)
		mockRepo.AssertExpectations(t)
		mockAction.AssertExpectations(t)
	})

	t.Run("Success update password", func(t *testing.T) {
		newPassword := "new-password"

		mockRepo.On(
			"UpdatePassword",
			mock.Anything,
			mock.AnythingOfType("*string"),
			mock.AnythingOfType("*string"),
		).
			Return(nil).
			Once()

		err := svc.UpdatePassword(ctx, &user.ID, &newPassword)

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

}
