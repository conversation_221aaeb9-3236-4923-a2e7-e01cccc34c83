package service

import (
	"api/domain"
	"api/utils"
	"context"
	"encoding/json"
	"os"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/rs/zerolog/log"
)

//go:generate mockery
type SkinAnalyzeRepository interface {
	CreateSkinAnalyze(ctx context.Context, skinAnalyze *domain.SkinAnalyze) (*domain.SkinAnalyze, error)
	GetSkinAnalyzeByID(ctx context.Context, id string) (*domain.SkinAnalyze, error)
	UpdateSkinAnalyzeByID(ctx context.Context, id string, skinAnalyze *domain.SkinAnalyze) (*domain.SkinAnalyze, error)
	GetManySkinAnalyzes(ctx context.Context, filter *domain.SkinAnalyzeFilter) ([]*domain.SkinAnalyze, int, error)
}

//go:generate mockery
type SkinAnalyzeHttp interface {
	UploadImage(ctx context.Context, data *domain.SkinAnalyzeUploadRequest) (*domain.SkinAnalyze, error)
}

//go:generate mockery
type Presigner interface {
	GetObject(
		ctx context.Context,
		bucketName string,
		objectKey string,
		lifetimeSecs int64,
	) (*v4.PresignedHTTPRequest, error)
	PutObject(
		ctx context.Context,
		bucketName string,
		objectKey string,
		lifetimeSecs int64,
	) (*v4.PresignedHTTPRequest, error)
}

type skinAnalyzeService struct {
	repo            SkinAnalyzeRepository
	skinProblemRepo SkinProblemRepository
	logRepo         MachineSyncLogRepository
	http            SkinAnalyzeHttp
	presigner       Presigner
}

func NewSkinAnalyzeService(
	repo SkinAnalyzeRepository,
	skinProblemRepo SkinProblemRepository,
	logRepo MachineSyncLogRepository,
	http SkinAnalyzeHttp,
	presigner Presigner,
) *skinAnalyzeService {
	return &skinAnalyzeService{repo, skinProblemRepo, logRepo, http, presigner}
}

func (s *skinAnalyzeService) CreateSkinAnalyze(
	ctx context.Context,
	data *domain.SkinAnalyzeUploadRequest,
) (*domain.SkinAnalyze, error) {
	skinAnalyze, err := s.http.UploadImage(ctx, data)
	if err != nil {
		log.Error().Err(err).Msg("Failed to upload image")
		return nil, err
	}

	// Save the skin analyze data to the repository
	skinAnalyze.OperatorID = data.OperatorID
	skinAnalyze, err = s.repo.CreateSkinAnalyze(ctx, skinAnalyze)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create skin analyze")
		return nil, err
	}

	logData := make(map[string]any)
	logData["folder_name"] = skinAnalyze.Name
	logData["images"] = skinAnalyze.PathImages
	logData["pdf"] = skinAnalyze.PathPDF
	logData["status"] = "success"
	logData["message"] = nil
	rawData, err := json.Marshal(logData)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal log data")
		return skinAnalyze, nil
	}

	insertLog := domain.MachineSyncLog{
		Data: rawData,
	}
	_, err = s.logRepo.CreateMachineSyncLog(ctx, &insertLog)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create machine sync log")
	}

	return skinAnalyze, nil
}

func (s *skinAnalyzeService) GetSkinAnalyzeByID(
	ctx context.Context,
	id string,
) (*domain.SkinAnalyze, error) {
	skinAnalyze, err := s.repo.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get skin analyze by ID")
		return nil, err
	}

	bucketName := os.Getenv("AWS_S3_BUCKET")
	signedImages := make([]string, len(skinAnalyze.PathImages))
	for i, image := range skinAnalyze.PathImages {
		presignedHttpRequest, err := s.presigner.GetObject(ctx, bucketName, image, 3600)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get presigned URL")
			return nil, err
		}
		signedImages[i] = presignedHttpRequest.URL
	}
	skinAnalyze.PathImages = signedImages

	presignedPdfRequest, err := s.presigner.GetObject(ctx, bucketName, skinAnalyze.PathPDF, 3600)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get presigned PDF URL")
		return nil, err
	}
	skinAnalyze.PathPDF = presignedPdfRequest.URL

	return skinAnalyze, nil
}

func (s *skinAnalyzeService) GetManySkinAnalyzes(
	ctx context.Context,
	filter *domain.SkinAnalyzeFilter,
) ([]*domain.SkinAnalyze, int, error) {
	role := ctx.Value("user_role").(domain.UserRole)
	if role == domain.Branch {
		filter.OperatorID = ctx.Value("user_id").(string)
	}
	skinAnalyzes, total, err := s.repo.GetManySkinAnalyzes(ctx, filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get many skin analyzes")
		return nil, 0, err
	}
	return skinAnalyzes, total, nil
}

func (s *skinAnalyzeService) GetTopConcernsSkinAnalyze(ctx context.Context, id string) ([]string, error) {
	skinAnalyze, err := s.repo.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get skin analyze by id")
		return nil, err
	}

	_, topConcerns := utils.GetTopConcernAndKey(skinAnalyze)

	skinProblems, _, err := s.skinProblemRepo.GetMany(ctx, &domain.SkinProblemFilter{
		Pagination: domain.Pagination{
			Page:     1,
			PageSize: 100,
		},
	})
	if err != nil {
		log.Error().Err(err).Msg("failed to get skin problems")
		return nil, err
	}

	topConcern := utils.MappingUserConcern(topConcerns, skinProblems)

	return topConcern, nil
}
