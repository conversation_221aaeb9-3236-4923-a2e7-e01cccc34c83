package service

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
	"golang.org/x/crypto/bcrypt"

	"api/domain"
)

//go:generate mockery
type AuthRepository interface {
	VerifyEmail(
		ctx context.Context,
		request *domain.LoginRequest,
	) (*domain.User, error)
}

type authService struct {
	repo AuthRepository
	ur   UserRepository
}

func NewAuthService(repo AuthRepository, ur UserRepository) *authService {
	return &authService{repo, ur}
}

func (service *authService) GenerateToken(
	ctx context.Context,
	request *domain.LoginRequest,
) (*string, *string, error) {
	data, err := service.repo.VerifyEmail(ctx, request)
	switch {
	case err != nil:
		return nil, nil, err
	case data == nil:
		return nil, nil, fmt.Errorf("Unauthorized")
	}

	err = bcrypt.CompareHashAndPassword(
		[]byte(*data.Password),
		[]byte(request.Password),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("Unauthorized")
	}

	token, refreshToken, err := domain.GenerateToken(*data)
	if err != nil {
		log.Error().Err(err).Msg("Error creating token")
		return nil, nil, fmt.Errorf("Unauthorized")
	}

	return &token, &refreshToken, nil
}

func (s *authService) RefreshToken(
	ctx context.Context,
	request *domain.RefreshRequest,
) (*string, *string, error) {
	claims, err := domain.VerifyToken(request.RefreshToken)
	if err != nil {
		log.Error().Err(err).Msg("Error verifying token")
		return nil, nil, fmt.Errorf("Unauthorized")
	}

	data, err := s.ur.GetByID(ctx, &claims.ID)
	switch {
	case err != nil:
		return nil, nil, err
	case data == nil:
		return nil, nil, fmt.Errorf("Unauthorized")
	}

	token, refreshToken, err := domain.GenerateToken(*data)
	if err != nil {
		log.Error().Err(err).Msg("Error creating token")
		return nil, nil, fmt.Errorf("Unauthorized")
	}

	return &token, &refreshToken, nil
}
