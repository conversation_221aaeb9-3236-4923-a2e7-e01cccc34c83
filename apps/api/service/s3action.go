package service

import (
	"context"
	"io"

	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
)

//go:generate mockery
type S3Action interface {
	UploadS3Object(
		ctx context.Context,
		bucketName string,
		objectKey string,
		file io.Reader,
	) (*manager.UploadOutput, error)
	CreateFolder(
		ctx context.Context,
		bucketName, folderName string,
	) error
	DeleteFolder(
		ctx context.Context,
		bucketName, folderName string,
	) error
}
