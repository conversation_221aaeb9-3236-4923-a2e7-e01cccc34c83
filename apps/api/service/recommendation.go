package service

import (
	"api/domain"
	"context"
	"fmt"
	"sort"

	"api/utils"

	"github.com/rs/zerolog/log"
)

//go:generate mockery
type RecommendationHttp interface {
	RecommendationTreatment(
		ctx context.Context,
		request *domain.RecommendationTreatmentMLRequest,
	) (*domain.RecommendationTreatmentMLResponse, error)
	GetRecommendation(
		ctx context.Context,
		request *domain.GetRecommendationMLRequest,
	) (*domain.RecommendationResponse, error)
}

type recommendationService struct {
	saRepo          SkinAnalyzeRepository
	caRepo          ConcernAnswerRepository
	cgRepo          ConcernGroupRepository
	tpRepo          TreatmentProductRepository
	usRepo          UserSurveyRepository
	recHttp         RecommendationHttp
	presigner       Presigner
	skinProblemRepo SkinProblemRepository
}

func NewRecommendationService(
	saRepo SkinAnalyzeRepository,
	caRepo ConcernAnswerRepository,
	cgRepo ConcernGroupRepository,
	tpRepo TreatmentProductRepository,
	usRepo UserSurveyRepository,
	recHttp RecommendationHttp,
	presigner Presigner,
	skinProblemRepo SkinProblemRepository,
) *recommendationService {
	return &recommendationService{saRepo, caRepo, cgRepo, tpRepo, usRepo, recHttp, presigner, skinProblemRepo}
}

func (s *recommendationService) RecommendationTreatment(
	ctx context.Context,
	id string,
) (*domain.RecommendationTreatmentMLResponse, error) {
	skinAnalyze, err := s.saRepo.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("failed to get skin analyze by id")
		return nil, err
	}

	key, topConcerns := utils.GetTopConcernAndKey(skinAnalyze)

	concernAnswer, err := s.caRepo.GetConcernAnswerByKey(ctx, key)
	if err != nil {
		log.Error().Err(err).Msg("failed to get concern answer by key")
		return nil, err
	}

	concernGroups, err := s.cgRepo.GetAllConcernGroup(ctx)
	if err != nil {
		log.Error().Err(err).Msg("failed to get all concern group")
		return nil, err
	}

	treatments, _, err := s.tpRepo.GetMany(ctx, &domain.TreatmentProductFilter{
		Pagination: domain.Pagination{
			Page:     1,
			PageSize: 100,
		},
	})
	if err != nil {
		log.Error().Err(err).Msg("failed to get all treatment")
		return nil, err
	}

	mlRequest := domain.RecommendationTreatmentMLRequest{
		SkinAnalyze:   *skinAnalyze,
		TopConcern:    topConcerns,
		ConcernAnswer: *concernAnswer,
		ConcernGroups: concernGroups,
		Treatments:    &treatments,
	}

	recommendationOutput, err := s.recHttp.RecommendationTreatment(ctx, &mlRequest)
	if err != nil {
		log.Error().
			Err(err).
			Str("Skin Analyze ID", id).
			Msg("ML recommendation treatment failed")
		return nil, fmt.Errorf("failed to generate treatment recommendation for Skin Analyze ID %s: %w", id, err)
	}

	// Presign the video URL if it exists
	if recommendationOutput.Video != "" {
		presignedURL, err := s.presigner.GetObject(ctx, recommendationOutput.Video, "video", 60*60)
		if err != nil {
			log.Error().Err(err).Msg("failed to presign video URL")
			return nil, err
		}
		recommendationOutput.Video = presignedURL.URL
	}

	return recommendationOutput, nil
}

func (s *recommendationService) GetRecommendation(
	ctx context.Context,
	id string,
) (*domain.RecommendationResponse, error) {

	skinAnalyze, err := s.saRepo.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("failed to get skin analyze by id")
		return nil, err
	}

	_, topConcerns := utils.GetTopConcernAndKey(skinAnalyze)

	userSurveys, _, err := s.usRepo.GetMany(ctx, &domain.UserSurveyFilter{
		SkinAnalyzeID: id,
	})

	if err != nil {
		log.Error().Err(err).Msg("failed to get user surveys")
		return nil, err
	}

	var userSurvey *domain.UserSurvey
	if len(userSurveys) > 0 {
		// Sort user surveys by UpdatedAt in descending order to get the latest one
		sort.Slice(userSurveys, func(i, j int) bool {
			return userSurveys[i].UpdatedAt > userSurveys[j].UpdatedAt
		})
		userSurvey = &userSurveys[0]
	}

	skinProblems, _, err := s.skinProblemRepo.GetMany(ctx, &domain.SkinProblemFilter{
		Pagination: domain.Pagination{
			Page:     1,
			PageSize: 100,
		},
	})
	if err != nil {
		log.Error().Err(err).Msg("failed to get skin problems")
		return nil, err
	}

	topConcern := utils.MappingUserConcern(topConcerns, skinProblems)
	userConcern := utils.ExtractConcernAnswersFromUserSurvey(userSurvey)
	treatmentIntervals := utils.ExtractTreatmentIntervalsFromUserSurvey(userSurvey)
	surveyContraindications := utils.ExtractSurveyContraindicationsFromUserSurvey(userSurvey)

	mlRequest := domain.GetRecommendationMLRequest{
		SASkinConcerns:          topConcern,
		SurveySkinConcerns:      userConcern,
		TreatmentIntervals:      treatmentIntervals,
		SurveyContraindications: surveyContraindications,
	}

	recommendationOutput, err := s.recHttp.GetRecommendation(ctx, &mlRequest)
	if err != nil {
		log.Error().
			Err(err).
			Str("Skin Analyze ID", id).
			Msg("ML recommendation generation failed")

		return nil, fmt.Errorf("failed to generate recommendation for Skin Analyze ID %s: %w", id, err)
	}

	return recommendationOutput, nil
}
