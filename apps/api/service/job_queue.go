package service

import (
	"api/domain"
	"context"
)

//go:generate mockery
type JobQueueRepository interface {
	CreateJobQueue(
		ctx context.Context,
		uID string,
		job *domain.FaceAgingJobQueue,
	) (*domain.FaceAgingJobQueue, error)
	GetJobQueueByID(
		ctx context.Context,
		id string,
	) (*domain.FaceAgingJobQueue, error)
	ProcessNewJobQueue(
		ctx context.Context,
	) (*domain.FaceAgingJobQueue, error)
	GetProcessingJobQueueCount(
		ctx context.Context,
	) (*int, error)
	UpdateJobQueueByID(
		ctx context.Context,
		id string,
		updateJob *domain.FaceAgingJobQueue,
	) (*domain.FaceAgingJobQueue, error)
	DeleteJobQueueByID(
		ctx context.Context,
		id string,
	) (*domain.FaceAgingJobQueue, error)
}
