package service_test

import (
	"api/domain"
	"api/service"
	"api/service/mocks"
	"context"
	"net/http"
	"testing"

	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/go-faker/faker/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestFaceAgingService(t *testing.T) {
	var (
		ctx              = context.Background()
		mockRepo         = new(mocks.SkinAnalyzeRepository)
		mockPresigner    = new(mocks.Presigner)
		mockJobQueueRepo = new(mocks.JobQueueRepository)
		svc              = service.NewFaceAgingService(mockRepo, mockPresigner, mockJobQueueRepo)
	)

	skinAnalyzeID := "123e4567-e89b-12d3-a456-426614174000"
	actualAge := 30
	phoneNumber := "1234567890"
	evaluationRate := 5
	skinAge := 30
	skinCondition := "Good"
	plTexture := 75
	uvPorphyrin := 80
	uvPigmentation := 60
	uvMoisture := 70
	sensitiveArea := 70
	brownArea := 80
	uvDamage := 90
	suggestion := "Use sunscreen"
	skinAnalyze := &domain.SkinAnalyze{
		ID:             skinAnalyzeID,
		Name:           "John Doe",
		ActualAge:      &actualAge,
		InputDate:      "2023-10-01",
		PhoneNumber:    &phoneNumber,
		EvaluationRate: &evaluationRate,
		SkinAge:        &skinAge,
		SkinCondition:  &skinCondition,
		RGBPore:        75,
		RGBSpot:        50,
		RGBWrinkle:     70,
		PLTexture:      &plTexture,
		UVPorphyrin:    &uvPorphyrin,
		UVPigmentation: &uvPigmentation,
		UVMoisture:     &uvMoisture,
		SensitiveArea:  &sensitiveArea,
		BrownArea:      &brownArea,
		UVDamage:       &uvDamage,
		Suggestion:     &suggestion,
		PathImages:     []string{"RGB.jpg", "rgb_resized.jpg", "mask.png", "Acne RGB.jpg", "RGB Wrinkle.jpg"},
		PathPDF:        "/path/to/pdf",
	}

	t.Run("Success create face aging job", func(t *testing.T) {
		inputData := &domain.FaceAgingConcernRequest{
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaLower,
					},
				},
			},
			IsBeautify: true,
		}

		mockRepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
		).
			Return(skinAnalyze, nil).
			Once()

		maskPath := "mask.png"
		payload := domain.FaceAgingConcernMLRequest{
			ImagePath: "rgb_resized.jpg",
			MaskPath:  &maskPath,
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaLower,
					},
				},
				{
					Concern: domain.FaceAgingConcernBeautify,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaMid,
						domain.FaceAgingAreaLower,
					},
				},
			},
		}

		faceAgingJobPayload := domain.FaceAgingJobQueue{
			SkinAnalyzeID: skinAnalyzeID,
			Payload:       payload,
		}
		faceAgingJobOutput := faceAgingJobPayload
		faceAgingJobOutput.ID = faker.UUIDHyphenated()

		uID := faker.UUIDHyphenated()
		ctx = context.WithValue(ctx, "user_id", uID)
		mockJobQueueRepo.On(
			"CreateJobQueue",
			mock.Anything,
			uID,
			&faceAgingJobPayload,
		).Return(&faceAgingJobOutput, nil)

		data, err := svc.FaceAgingWithConcern(
			ctx,
			skinAnalyzeID,
			inputData,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, &faceAgingJobOutput, data)
		mockRepo.AssertExpectations(t)
		mockJobQueueRepo.AssertExpectations(t)
	})

	t.Run("Success get job status by id", func(t *testing.T) {
		id := faker.UUIDHyphenated()
		maskPath := "mask.png"
		payload := domain.FaceAgingConcernMLRequest{
			ImagePath: "rgb_resized.jpg",
			MaskPath:  &maskPath,
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaLower,
					},
				},
				{
					Concern: domain.FaceAgingConcernBeautify,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaMid,
						domain.FaceAgingAreaLower,
					},
				},
			},
		}

		faceAgingJobPayload := domain.FaceAgingJobQueue{
			Payload: payload,
			Status:  domain.JobQueueStatusQueued,
		}
		faceAgingJobOutput := faceAgingJobPayload
		faceAgingJobOutput.ID = id

		mockJobQueueRepo.On(
			"GetJobQueueByID",
			mock.Anything,
			id,
		).Return(&faceAgingJobOutput, nil)

		data, err := svc.FaceAgingJobStatusByID(
			ctx,
			id,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, &faceAgingJobOutput, data)
		mockRepo.AssertExpectations(t)
		mockJobQueueRepo.AssertExpectations(t)
	})

	t.Run("Success get job status by id completed returning presigned images", func(t *testing.T) {
		id := faker.UUIDHyphenated()
		maskPath := "mask.png"
		payload := domain.FaceAgingConcernMLRequest{
			ImagePath: "rgb_resized.jpg",
			MaskPath:  &maskPath,
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaLower,
					},
				},
				{
					Concern: domain.FaceAgingConcernBeautify,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaMid,
						domain.FaceAgingAreaLower,
					},
				},
			},
		}

		faceAgingResponse := domain.FaceAgingConcernResponse{
			GeneratedImages: []domain.FaceAgingGeneratedMLResponse{
				{
					Concern:              domain.FaceAgingConcernWrinkle,
					GeneratedImageURL:    "https://example.com/generated_image.jpg",
					SelectedAreaImageURL: "https://example.com/selected_area_image.jpg",
				},
				{
					Concern:              domain.FaceAgingConcernBeautify,
					GeneratedImageURL:    "https://example.com/generated_image.jpg",
					SelectedAreaImageURL: "https://example.com/selected_area_image.jpg",
				},
			},
		}
		faceAgingJobOutput := domain.FaceAgingJobQueue{
			ID:      id,
			Payload: payload,
			Status:  domain.JobQueueStatusCompleted,
			Result:  &faceAgingResponse,
		}

		mockJobQueueRepo.On(
			"GetJobQueueByID",
			mock.Anything,
			id,
		).Return(&faceAgingJobOutput, nil)

		presignedHttpRequest := v4.PresignedHTTPRequest{
			URL:    "https://example.com/presigned-url",
			Method: http.MethodGet,
		}
		mockPresigner.On(
			"GetObject",
			mock.Anything,
			mock.AnythingOfType("string"),
			mock.AnythingOfType("string"),
			mock.AnythingOfType("int64"),
		).
			Return(&presignedHttpRequest, nil)

		faceAgingResponsePresigned := domain.FaceAgingConcernResponse{
			GeneratedImages: []domain.FaceAgingGeneratedMLResponse{
				{
					Concern:              domain.FaceAgingConcernWrinkle,
					GeneratedImageURL:    "https://example.com/presigned-url",
					SelectedAreaImageURL: "https://example.com/presigned-url",
				},
				{
					Concern:              domain.FaceAgingConcernBeautify,
					GeneratedImageURL:    "https://example.com/presigned-url",
					SelectedAreaImageURL: "https://example.com/presigned-url",
				},
			},
		}
		jobResult := faceAgingJobOutput
		jobResult.Result = &faceAgingResponsePresigned

		data, err := svc.FaceAgingJobStatusByID(
			ctx,
			id,
		)

		assert.NoError(t, err)
		assert.NotNil(t, data)
		assert.Equal(t, &jobResult, data)
		mockPresigner.AssertExpectations(t)
		mockJobQueueRepo.AssertExpectations(t)
	})

	t.Run("Success face aging job callback", func(t *testing.T) {
		faceAgingConcernMLOutput := domain.FaceAgingConcernMLResponse{
			ImageURL: []string{
				"https://example.com/rgb_resized.jpg",
			},
			GeneratedImage: []domain.FaceAgingGeneratedMLResponse{
				{
					Concern:              domain.FaceAgingConcernWrinkle,
					GeneratedImageURL:    "https://example.com/generated_image.jpg",
					SelectedAreaImageURL: "https://example.com/selected_area_image.jpg",
				},
				{
					Concern:              domain.FaceAgingConcernBeautify,
					GeneratedImageURL:    "https://example.com/generated_image.jpg",
					SelectedAreaImageURL: "https://example.com/selected_area_image.jpg",
				},
			},
		}

		id := faker.UUIDHyphenated()
		maskPath := "mask.png"
		payload := domain.FaceAgingConcernMLRequest{
			ImagePath: "rgb_resized.jpg",
			MaskPath:  &maskPath,
			Concerns: []domain.FaceAgingConcernDetailMLRequest{
				{
					Concern: domain.FaceAgingConcernWrinkle,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaLower,
					},
				},
				{
					Concern: domain.FaceAgingConcernBeautify,
					Areas: []domain.FaceAgingArea{
						domain.FaceAgingAreaUpper,
						domain.FaceAgingAreaMid,
						domain.FaceAgingAreaLower,
					},
				},
			},
		}

		faceAgingResponse := domain.FaceAgingConcernResponse{
			GeneratedImages: []domain.FaceAgingGeneratedMLResponse{
				{
					Concern:              domain.FaceAgingConcernWrinkle,
					GeneratedImageURL:    "https://example.com/generated_image.jpg",
					SelectedAreaImageURL: "https://example.com/selected_area_image.jpg",
				},
				{
					Concern:              domain.FaceAgingConcernBeautify,
					GeneratedImageURL:    "https://example.com/generated_image.jpg",
					SelectedAreaImageURL: "https://example.com/selected_area_image.jpg",
				},
			},
		}

		faceAgingJobPayload := domain.FaceAgingJobQueue{
			ID:            id,
			SkinAnalyzeID: skinAnalyzeID,
			Payload:       payload,
			Status:        domain.JobQueueStatusProcessing,
		}

		mockJobQueueRepo.On(
			"GetJobQueueByID",
			mock.Anything,
			id,
		).Return(&faceAgingJobPayload, nil)

		existSkinAnalyze := *skinAnalyze
		existSkinAnalyze.PathImages = []string{
			"image1.jpg",
			"image2.jpg",
			"https://example.com/generated_image.jpg",
			"https://example.com/selected_area_image.jpg",
			"https://example.com/generated_image.jpg",
			"https://example.com/selected_area_image.jpg",
		}
		mockRepo.On(
			"GetSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
		).
			Return(&existSkinAnalyze, nil).
			Once()

		updateSkinAnalyze := skinAnalyze
		updateSkinAnalyze.PathImages = []string{
			"image1.jpg",
			"image2.jpg",
			faceAgingConcernMLOutput.GeneratedImage[0].GeneratedImageURL,
			faceAgingConcernMLOutput.GeneratedImage[0].SelectedAreaImageURL,
			faceAgingConcernMLOutput.GeneratedImage[1].GeneratedImageURL,
			faceAgingConcernMLOutput.GeneratedImage[1].SelectedAreaImageURL,
			faceAgingConcernMLOutput.ImageURL[0],
		}

		mockRepo.On(
			"UpdateSkinAnalyzeByID",
			mock.Anything,
			skinAnalyzeID,
			updateSkinAnalyze,
		).
			Return(updateSkinAnalyze, nil).
			Once()

		updateJob := faceAgingJobPayload
		updateJob.Status = domain.JobQueueStatusCompleted
		updateJob.Result = &faceAgingResponse
		mockJobQueueRepo.On(
			"UpdateJobQueueByID",
			mock.Anything,
			id,
			&updateJob,
		).Return(&updateJob, nil)

		err := svc.FaceAgingJobCallback(
			ctx,
			id,
			faceAgingConcernMLOutput,
		)

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
		mockJobQueueRepo.AssertExpectations(t)
	})
}
