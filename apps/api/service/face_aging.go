package service

import (
	"api/domain"
	"context"
	"os"
	"path/filepath"
	"slices"

	"github.com/rs/zerolog/log"
)

//go:generate mockery
type FaceAgingHttp interface {
	FaceAgingWithConcern(
		ctx context.Context,
		data *domain.FaceAgingConcernMLRequest,
	) (*domain.FaceAgingConcernMLResponse, error)
}

type faceAgingService struct {
	repo      SkinAnalyzeRepository
	presigner Presigner
	jr        JobQueueRepository
}

func NewFaceAgingService(
	repo SkinAnalyzeRepository,
	presigner Presigner,
	jr JobQueueRepository,
) *faceAgingService {
	return &faceAgingService{repo, presigner, jr}
}

func (s *faceAgingService) FaceAgingWithConcern(
	ctx context.Context,
	id string,
	data *domain.FaceAgingConcernRequest,
) (*domain.FaceAgingJobQueue, error) {
	skinAnalyze, err := s.repo.GetSkinAnalyzeByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if data.IsBeautify {
		data.Concerns = append(data.Concerns, domain.FaceAgingConcernDetailMLRequest{
			Concern: domain.FaceAgingConcernBeautify,
			Areas: []domain.FaceAgingArea{
				domain.FaceAgingAreaUpper,
				domain.FaceAgingAreaMid,
				domain.FaceAgingAreaLower,
			},
		})
	}

	mlRequest := domain.FaceAgingConcernMLRequest{
		Concerns: data.Concerns,
	}
	for _, path := range skinAnalyze.PathImages {
		f := filepath.Base(path)
		if f == "rgb_resized.jpg" {
			mlRequest.ImagePath = path
		} else if f == "RGB.jpg" && mlRequest.ImagePath == "" {
			mlRequest.ImagePath = path
		} else if f == "mask.png" {
			mlRequest.MaskPath = &path
		}
	}

	uID := ctx.Value("user_id").(string)
	faceAgingJob := domain.FaceAgingJobQueue{
		SkinAnalyzeID: id,
		Payload:       mlRequest,
	}
	createdJob, err := s.jr.CreateJobQueue(ctx, uID, &faceAgingJob)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create face aging job")
		return nil, err
	}

	return createdJob, nil
}

func (s *faceAgingService) FaceAgingJobStatusByID(
	ctx context.Context,
	id string,
) (*domain.FaceAgingJobQueue, error) {
	job, err := s.jr.GetJobQueueByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("Error in getting job queue by id")
		return nil, err
	}

	if job.Status == domain.JobQueueStatusCompleted && job.Result != nil {
		output := domain.FaceAgingConcernResponse{}
		bucketName := os.Getenv("AWS_S3_BUCKET")
		for _, generatedImage := range job.Result.GeneratedImages {
			generatedImageURL, err := s.presigner.GetObject(ctx, bucketName, generatedImage.GeneratedImageURL, 3600)
			if err != nil {
				log.Error().Err(err).Msg("Failed to presign URL")
				return nil, err
			}
			selectedAreaImageURL, err := s.presigner.GetObject(ctx, bucketName, generatedImage.SelectedAreaImageURL, 3600)
			if err != nil {
				log.Error().Err(err).Msg("Failed to presign URL")
				return nil, err
			}
			output.GeneratedImages = append(output.GeneratedImages, domain.FaceAgingGeneratedMLResponse{
				Concern:              generatedImage.Concern,
				GeneratedImageURL:    generatedImageURL.URL,
				SelectedAreaImageURL: selectedAreaImageURL.URL,
			})
		}
		job.Result = &output
	}

	return job, nil
}

func (s *faceAgingService) FaceAgingJobCallback(
	ctx context.Context,
	id string,
	d domain.FaceAgingConcernMLResponse,
) error {
	job, err := s.jr.GetJobQueueByID(ctx, id)
	if err != nil {
		log.Error().Err(err).Msg("Error in getting job queue by id")
		return err
	}

	skinAnalyze, err := s.repo.GetSkinAnalyzeByID(ctx, job.SkinAnalyzeID)
	if err != nil {
		log.Error().Err(err).Msg("Error in getting skin analyze by id")
		return err
	}

	updateSkinAnalyze := skinAnalyze
	for _, imageURL := range d.ImageURL {
		if slices.Contains(updateSkinAnalyze.PathImages, imageURL) {
			continue
		}
		updateSkinAnalyze.PathImages = append(updateSkinAnalyze.PathImages, imageURL)
	}
	for _, generatedImage := range d.GeneratedImage {
		if slices.Contains(updateSkinAnalyze.PathImages, generatedImage.GeneratedImageURL) {
			continue
		}
		updateSkinAnalyze.PathImages = append(
			updateSkinAnalyze.PathImages,
			generatedImage.GeneratedImageURL,
			generatedImage.SelectedAreaImageURL,
		)
	}
	_, err = s.repo.UpdateSkinAnalyzeByID(ctx, skinAnalyze.ID, updateSkinAnalyze)
	if err != nil {
		log.Error().Err(err).Msg("Error in updating skin analyze by id")
		return err
	}

	result := domain.FaceAgingConcernResponse{
		GeneratedImages: d.GeneratedImage,
	}

	updateJob := job
	updateJob.Status = domain.JobQueueStatusCompleted
	updateJob.Result = &result
	_, err = s.jr.UpdateJobQueueByID(ctx, id, updateJob)
	if err != nil {
		log.Error().Err(err).Msg("Error in updating job queue by id")
		return err
	}

	return nil

}
