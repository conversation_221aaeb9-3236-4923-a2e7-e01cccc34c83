package service_test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"

	"api/domain"
	"api/service"
	"api/service/mocks"
)

func TestAuthService(t *testing.T) {
	var (
		ctx          = context.Background()
		mockRepo     = new(mocks.AuthRepository)
		mockUserRepo = new(mocks.UserRepository)
		svc          = service.NewAuthService(mockRepo, mockUserRepo)
	)

	var (
		password = "test-password"

		generatedPassword, err = bcrypt.GenerateFromPassword(
			[]byte(password),
			bcrypt.DefaultCost,
		)

		encryptPassword = string(generatedPassword[:])
	)

	require.NoError(t, err, "Generate password should not have an error.")

	var (
		sharedData = domain.User{
			ID:          "test-uuid",
			Name:        "Test user auth",
			Email:       "<EMAIL>",
			Role:        domain.Admin,
			PhoneNumber: "08123456789",
			Password:    &encryptPassword,
		}

		sharedRequest = domain.LoginRequest{
			Email:    sharedData.Email,
			Password: password,
		}
	)

	t.Run("Success Generate Token", func(t *testing.T) {
		t.Run("Success login", func(t *testing.T) {
			data := sharedData
			request := sharedRequest

			mockRepo.On(
				"VerifyEmail",
				mock.Anything,
				mock.AnythingOfType("*domain.LoginRequest"),
			).
				Return(&data, nil).
				Once()

			token, refreshToken, err := svc.GenerateToken(ctx, &request)

			assert.NoError(t, err)
			assert.NotNil(t, token)
			assert.NotNil(t, refreshToken)
			mockRepo.AssertExpectations(t)
		})

		t.Run("Failed login email not found", func(t *testing.T) {
			request := sharedRequest

			mockRepo.On(
				"VerifyEmail",
				mock.Anything,
				mock.AnythingOfType("*domain.LoginRequest"),
			).
				Return(nil, nil).
				Once()

			token, refreshToken, err := svc.GenerateToken(ctx, &request)

			assert.Error(t, err)
			assert.Nil(t, token)
			assert.Nil(t, refreshToken)
			mockRepo.AssertExpectations(t)
		})

		t.Run("Failed login error in verify email", func(t *testing.T) {
			request := sharedRequest

			mockRepo.On(
				"VerifyEmail",
				mock.Anything,
				mock.AnythingOfType("*domain.LoginRequest"),
			).
				Return(nil, fmt.Errorf("Error in verify email")).
				Once()

			token, refreshToken, err := svc.GenerateToken(ctx, &request)

			assert.Error(t, err)
			assert.Nil(t, token)
			assert.Nil(t, refreshToken)
			mockRepo.AssertExpectations(t)
		})
	})

	t.Run("Success Refresh Token", func(t *testing.T) {
		t.Run("Success refresh", func(t *testing.T) {
			data := sharedData
			request := domain.RefreshRequest{
				RefreshToken: generateValidJWT(data.ID),
			}

			mockUserRepo.On(
				"GetByID",
				mock.Anything,
				mock.AnythingOfType("*string"),
			).
				Return(&data, nil).
				Once()

			token, refreshToken, err := svc.RefreshToken(ctx, &request)

			assert.NoError(t, err)
			assert.NotNil(t, token)
			assert.NotNil(t, refreshToken)
			mockUserRepo.AssertExpectations(t)
		})
	})

}

func generateValidJWT(userID string) string {
	secret := []byte(os.Getenv("SECRET_KEY"))

	claims := jwt.MapClaims{
		"user_id": userID,
		"exp":     time.Now().Add(time.Hour).Unix(),
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	signedToken, err := token.SignedString(secret)
	if err != nil {
		panic(err)
	}
	return signedToken
}
