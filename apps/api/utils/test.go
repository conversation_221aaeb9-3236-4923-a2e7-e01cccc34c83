package utils

import (
	"context"
	"encoding/json"
	"io"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/require"
)

var (
	DummyID = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
)

func GetTestDBPool(t *testing.T) *pgxpool.Pool {
	t.Helper()

	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		t.Fatal("DATABASE_URL environment variable is not set.")
	}

	ctx := context.Background()
	dbPool, err := pgxpool.New(ctx, dbURL)
	require.NoError(t, err)

	err = dbPool.Ping(ctx)
	require.NoError(t, err)

	return dbPool
}

func CleanupTestDummyData(
	ctx context.Context,
	dbPool *pgxpool.Pool,
	query string,
	args pgx.NamedArgs,
) error {
	_, err := dbPool.Exec(
		ctx,
		query,
		args,
	)

	return err
}

func CreateDummyIOReader(text string) io.Reader {
	return strings.NewReader(text)
}

// Example usage:
// DebugTestRestResponse[domain.SingleResponse[domain.Empty]](t, rec)
func DebugTestRestResponse[T any](
	t *testing.T,
	rec *httptest.ResponseRecorder,
) {
	respBody, err := io.ReadAll(rec.Result().Body)

	if err != nil {
		t.Fatalf("Failed read response body: %v", err)
	}

	var response T
	err = json.Unmarshal(respBody, &response)

	if err != nil {
		t.Fatalf("Failed unmarshall response body: %v", err)
	}

	t.Logf("Response: %+v", response)
}

func IntPtr(i int) *int {
	return &i
}
