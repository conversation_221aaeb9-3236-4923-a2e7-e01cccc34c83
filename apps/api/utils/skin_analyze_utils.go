package utils

import (
	"api/domain"
	"sort"
	"strings"
	"time"
)

func DerefOrZero(val *int) int {
	if val != nil {
		return *val
	}
	return 0
}

func AppendIfNotNil(
	slice []domain.RecommendationConcern,
	score *int,
	appendValue *domain.RecommendationConcern,
) []domain.RecommendationConcern {
	if score != nil {
		slice = append(slice, *appendValue)
	}
	return slice
}

// GetTopConcernAndKey extracts the top 3 concerns from a SkinAnalyze and returns a key and the concerns.
func GetTopConcernAndKey(
	skinAnalyze *domain.SkinAnalyze,
) (string, []domain.RecommendationConcern) {
	saScores := []domain.RecommendationConcern{
		{
			Name:  "Pore",
			Label: "pores",
			Score: skinAnalyze.RGBPore,
		},
		{
			Name:  "Spot",
			Label: "spots",
			Score: skinAnalyze.RGBSpot,
		},
		{
			Name:  "Wrinkle",
			Label: "wrinkles",
			Score: skinAnalyze.RGBWrinkle,
		},
	}

	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.PLTexture,
		&domain.RecommendationConcern{
			Name:  "Texture",
			Label: "textures",
			Score: DerefOrZero(skinAnalyze.PLTexture),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.UVPorphyrin,
		&domain.RecommendationConcern{
			Name:  "Porphyrin",
			Label: "porphyrin",
			Score: DerefOrZero(skinAnalyze.UVPorphyrin),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.UVPigmentation,
		&domain.RecommendationConcern{
			Name:  "Pigmentation",
			Label: "pigmentation",
			Score: DerefOrZero(skinAnalyze.UVPigmentation),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.UVMoisture,
		&domain.RecommendationConcern{
			Name:  "Moisture",
			Label: "moisture",
			Score: DerefOrZero(skinAnalyze.UVMoisture),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.SensitiveArea,
		&domain.RecommendationConcern{
			Name:  "Sensitive Area",
			Label: "sensitive_area",
			Score: DerefOrZero(skinAnalyze.SensitiveArea),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.BrownArea,
		&domain.RecommendationConcern{
			Name:  "Brown Area",
			Label: "brown_area",
			Score: DerefOrZero(skinAnalyze.BrownArea),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.UVDamage,
		&domain.RecommendationConcern{
			Name:  "UV Damage",
			Label: "uv_damage",
			Score: DerefOrZero(skinAnalyze.UVDamage),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.SensitiveArea,
		&domain.RecommendationConcern{
			Name:  "Sensitive Area",
			Label: "sensitive_area",
			Score: DerefOrZero(skinAnalyze.SensitiveArea),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.BrownArea,
		&domain.RecommendationConcern{
			Name:  "Brown Area",
			Label: "brown_area",
			Score: DerefOrZero(skinAnalyze.BrownArea),
		},
	)
	saScores = AppendIfNotNil(
		saScores,
		skinAnalyze.UVDamage,
		&domain.RecommendationConcern{
			Name:  "UV Damage",
			Label: "uv_damage",
			Score: DerefOrZero(skinAnalyze.UVDamage),
		},
	)

	sort.Slice(saScores, func(i, j int) bool {
		return saScores[i].Score < saScores[j].Score
	})
	topConcerns := saScores[:3]

	var labels []string
	for _, concern := range topConcerns {
		labels = append(labels, concern.Label)
	}
	key := strings.Join(labels, "_")

	return key, topConcerns
}

// ExtractConcernAnswersFromUserSurvey extracts deduplicated concern answers from a user survey.
func ExtractConcernAnswersFromUserSurvey(userSurvey *domain.UserSurvey) []string {
	var concernAnswers []string
	nameSet := make(map[string]struct{})

	concernQuestionIDs := []string{
		"2097dda1-544a-4d7a-89c6-6314bcefa592",
		"959ac8b0-b1a7-4da6-beaf-70063c83fc53",
		"94e22537-8ff3-4702-ac26-87c0a60139cb",
		"cef51648-1f44-43dd-944f-a677d37f8db4",
	}

	if userSurvey != nil {
		for _, result := range userSurvey.Results {
			// Check if question ID matches any of the concern question IDs
			isConcernQuestion := false
			for _, questionID := range concernQuestionIDs {
				if result.ID == questionID {
					isConcernQuestion = true
					break
				}
			}

			if isConcernQuestion {
				for _, answer := range result.Answers {
					var concern string
					if strings.Contains(answer, "-") {
						// Take only the first word before ' - '
						parts := strings.SplitN(answer, " - ", 2)
						concern = strings.TrimSpace(parts[0])
					} else {
						concern = answer
					}
					lower := strings.ToLower(concern)
					if lower != "normal" && lower != "combination" && lower != "tidak" && lower != "no concern" && lower != "no" {
						if _, exists := nameSet[concern]; !exists {
							concernAnswers = append(concernAnswers, concern)
							nameSet[concern] = struct{}{}
						}
					}
				}
			}
		}
	}

    if concernAnswers == nil {
        return []string{}
    }

	return concernAnswers
}

func ExtractTreatmentIntervalsFromUserSurvey(userSurvey *domain.UserSurvey) []domain.RecommendationTreatmentInterval {
	var treatmentIntervals []domain.RecommendationTreatmentInterval

	intervalQuestionID := "4864e913-0c7f-458f-9b19-dd091ccb254e"

	if userSurvey != nil {
		for _, result := range userSurvey.Results {
			if result.ID == intervalQuestionID {
                if len(result.Answers) > 1 && strings.Contains(result.Answers[0], "Yes") {
                    for _, answer := range result.Answers[1:] {
                        parts := strings.SplitN(answer, " - ", 2)
                        name := strings.TrimSpace(parts[0])
                        date := strings.TrimSpace(parts[1])

                        dateTime, err := time.Parse("02/01/2006", date)
                        if err != nil {
                            continue
                        }

                        now := time.Now()
                        diff := now.Sub(dateTime)
                        days := int(diff.Hours() / 24) + 1

                        treatmentIntervals = append(treatmentIntervals, domain.RecommendationTreatmentInterval{
                            Name: name,
                            Days: days,
                        })
                    }
                }

			}

		}
	}

	// Ensure we always return an empty slice instead of nil
	if treatmentIntervals == nil {
		return []domain.RecommendationTreatmentInterval{}
	}

	return treatmentIntervals
}

func ExtractSurveyContraindicationsFromUserSurvey(userSurvey *domain.UserSurvey) []domain.SurveyContraindication {

    var surveyContraindications []domain.SurveyContraindication

    if userSurvey != nil {

        //skip first 4 items (static questions for extracted concern)
        for i := 4; i < len(userSurvey.Results); i++ {
            result := userSurvey.Results[i]
            if result.Category == domain.Contraindication {
                surveyContraindications = append(surveyContraindications, domain.SurveyContraindication{
                    Question: result.Question,
                    Answers: result.Answers,
                })
            }
        }
    }

    if surveyContraindications == nil {
        return []domain.SurveyContraindication{}
    }

    return surveyContraindications
}

func MappingUserConcern(
	topConcerns []domain.RecommendationConcern,
	skinProblems []domain.SkinProblemResponse,
) []string {
	var userConcern []string
	nameSet := make(map[string]struct{})

	// 1. Add skin problem names that match top concerns
	for _, concern := range topConcerns {
		for _, problem := range skinProblems {
			for _, indication := range problem.SkinProblemIndications {
				if indication.Name == concern.Name {
					if _, exists := nameSet[problem.Name]; !exists {
						userConcern = append(userConcern, problem.Name)
						nameSet[problem.Name] = struct{}{}
					}
					break
				}
			}
		}
	}

    if userConcern == nil {
        return []string{}
    }

	return userConcern
}
