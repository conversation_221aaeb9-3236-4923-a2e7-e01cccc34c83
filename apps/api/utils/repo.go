package utils

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

func CustomPostgresErr(err error) error {
	var (
		pgErr     *pgconn.PgError
		customErr = err
	)

	// To handle no rows error in service.
	if errors.Is(err, pgx.ErrNoRows) {
		return nil
	}

	// Return actual error if it's not postgres error.
	if !errors.As(err, &pgErr) {
		return err
	}

	if pgErr.SQLState() == "23505" {
		switch pgErr.ConstraintName {
		case "users_email_key":
			customErr = fmt.Errorf("Email already exist")
		case "parameter_skin_evaluations_name_key":
			customErr = fmt.Errorf("Parameter skin evaluation name already exist")
		case "treatment_products_item_code_key":
			customErr = fmt.Errorf("Item code already exist")
		case "treatment_indications_name_key":
			customErr = fmt.Errorf("Skin problem name already exist")
		case "skin_problem_indications_name_key":
			customErr = fmt.Errorf("Skin problem indication name already exist")
		}
	}

	if pgErr.SQLState() == "23503" {
		switch pgErr.ConstraintName {
		case "skin_problem_groups_skin_problem_indication_id_fkey":
			customErr = fmt.Errorf(
				"skin problem indication still used in skin problem groups",
			)
		case "skin_analyzes_operator_id_fkey":
			customErr = fmt.Errorf(
				"user still used in skin analyzes",
			)
		case "treatment_products_indications_treatment_indication_id_fkey":
			customErr = fmt.Errorf(
				"skin problem still used in treatment or product list",
			)
		case "treatment_products_categories_treatment_category_id_fkey":
			customErr = fmt.Errorf(
				"treatment category still used in treatment or product list",
			)
		case "treatment_products_interval_id_fkey":
			customErr = fmt.Errorf(
				"treatment interval still used in treatment or product list",
			)
		}
	}

	if pgErr.SQLState() == "23514" {
		switch pgErr.ConstraintName {
		case "duration_top_recommendation_should_not_null":
			customErr = fmt.Errorf(
				"duration_top_recommendation should not be null if is_top_recommendation is true",
			)
		}
	}

	return customErr
}

func GetComposedWhereClause(composedFilter []string) *string {
	var whereClause string

	if len(composedFilter) > 0 {
		whereClause = fmt.Sprintf("WHERE %s\n", strings.Join(composedFilter, ` AND `))
	}

	return &whereClause
}
