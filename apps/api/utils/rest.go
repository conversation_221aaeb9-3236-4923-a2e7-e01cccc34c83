package utils

import (
	"context"
	"net/http"
	"os"

	"github.com/golang-jwt/jwt/v5"
	echoJWT "github.com/labstack/echo-jwt/v4"
	"github.com/labstack/echo/v4"

	"api/domain"
)

const (
	SuccessMsg = "success"
	ErrorMsg   = "error"
)

func JWTConfig() echo.MiddlewareFunc {
	secret := os.Getenv("SECRET_KEY")

	config := echoJWT.Config{
		SigningKey: []byte(secret),
		NewClaimsFunc: func(c echo.Context) jwt.Claims {
			return new(domain.JWTClaim)
		},
		SuccessHandler: func(c echo.Context) {
			user := c.Get("user").(*jwt.Token)
			claim := user.Claims.(*domain.JWTClaim)

			ctx := c.Request().Context()
			ctx = context.WithValue(ctx, "user_id", claim.ID)
			ctx = context.WithValue(ctx, "user_role", claim.Role)
			c.SetRequest(c.Request().WithContext(ctx))
		},
		ErrorHandler: func(c echo.Context, err error) error {
			return c.JSON(
				http.StatusUnauthorized,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusUnauthorized,
					Status:  ErrorMsg,
					Message: err.Error(),
				},
			)
		},
	}

	return echoJWT.WithConfig(config)
}

func RequireRoles(roles ...domain.UserRole) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			user, ok := c.Get("user").(*jwt.Token)

			if !ok {
				return c.JSON(
					http.StatusUnauthorized,
					domain.SingleResponse[domain.Empty]{
						Code:    http.StatusUnauthorized,
						Status:  ErrorMsg,
						Message: "Missing token",
					},
				)
			}

			claim, ok := user.Claims.(*domain.JWTClaim)

			if !ok {
				return c.JSON(
					http.StatusUnauthorized,
					domain.SingleResponse[domain.Empty]{
						Code:    http.StatusUnauthorized,
						Status:  ErrorMsg,
						Message: "Invalid token",
					},
				)
			}

			for _, allowed := range roles {
				if domain.UserRole(claim.Role) == allowed {
					return next(c)
				}
			}

			return c.JSON(
				http.StatusForbidden,
				domain.SingleResponse[domain.Empty]{
					Code:    http.StatusForbidden,
					Status:  ErrorMsg,
					Message: "Forbidden: unauthorized role",
				},
			)
		}
	}
}
