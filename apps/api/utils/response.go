package utils

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/jackc/pgx/v5"

	"github.com/go-playground/validator/v10"
)

func GetStatusCode(err error) int {
	if err == nil {
		return http.StatusOK
	}

	var validationErrors validator.ValidationErrors

	if errors.As(err, &validationErrors) {
		return http.StatusBadRequest
	}

	switch {
	case strings.Contains(err.Error(), "not found"):
		return http.StatusNotFound
	case strings.Contains(err.Error(), "already exist"):
		return http.StatusConflict
	case strings.Contains(err.<PERSON>r(), "ParentQuestionAnswer index out of bounds"):
		return http.StatusBadRequest
	case strings.Contains(err.Error(), "still used"):
		return http.StatusBadRequest
	}

	switch err {
	case sql.ErrNoRows, pgx.ErrNoRows:
		return http.StatusNotFound
	default:
		return http.StatusInternalServerError
	}
}

func GetErrorMessage(ctx context.Context, err error, dm string) string {
	env := ctx.Value("APP_ENV").(string)
	if env == "production" {
		return dm
	}

	switch {
	case strings.Contains(err.Error(), "not found"):
		return err.Error()
	}

	switch err {
	case sql.ErrNoRows, pgx.ErrNoRows:
		return fmt.Sprintf("Data not found: %s", err.Error())
	default:
		return fmt.Sprintf("%s %s", dm, err.Error())
	}
}
