package main

import (
	"context"
	"time"

	"github.com/rs/zerolog/log"

	"api/domain"
	"api/internal/repositories/postgres"

	apiConfig "api/config"
	httpRepo "api/internal/repositories/http"
	"api/utils"
)

func main() {
	ctx := context.Background()

	const NUM_WORKER = 1

	apiConfig.LoadEnv()
	log.Logger = log.With().Caller().Logger()

	dbPool, err := utils.SetupDatabase()
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to setup database: %v", err)
	}
	defer dbPool.Close()

	jobQueueRepo := postgres.NewJobQueueRepository(dbPool)
	faceAgingHttp := httpRepo.NewFaceAgingHttp()

	log.Info().Msg("Starting Go Worker...")
	for {
		processingJobCount, err := jobQueueRepo.GetProcessingJobQueueCount(ctx)
		if err != nil {
			log.Error().Err(err).Msg("Error getting job count")
			time.Sleep(5 * time.Second) // Wait before retrying
			continue
		}
		if processingJobCount != nil && *processingJobCount >= NUM_WORKER {
			log.Info().Msg("Processes are already running, continuing")
			time.Sleep(5 * time.Second) // Wait before retrying
			continue
		}

		job, err := jobQueueRepo.ProcessNewJobQueue(ctx)
		if err != nil {
			log.Error().Err(err).Msg("Error getting job from queue")
			time.Sleep(5 * time.Second) // Wait before retrying
			continue
		}

		if job != nil {
			log.Info().Msgf("Found job %s, pushing to Python API...", job.ID)
			_, err := faceAgingHttp.FaceAgingWithConcern(ctx, job.ID, &job.Payload)
			if err != nil {
				log.Error().Err(err).Msg("Failed to push job to python, Re-queuing")
				updateJob := job
				updateJob.RetryCount = job.RetryCount + 1
				if updateJob.RetryCount == 3 {
					updateJob.Status = domain.JobQueueStatusFailed
				} else {
					updateJob.Status = domain.JobQueueStatusQueued
				}
				_, err := jobQueueRepo.UpdateJobQueueByID(ctx, job.ID, updateJob)
				if err != nil {
					log.Error().Err(err).Msg("Failed to update job queue")
				}
				time.Sleep(5 * time.Second)
			}
		} else {
			log.Info().Msg("No jobs in queue. Waiting...")
			time.Sleep(5 * time.Second)
		}
	}
}
