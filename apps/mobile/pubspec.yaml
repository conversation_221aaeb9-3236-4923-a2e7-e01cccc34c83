name: euromedica_aizer
description: A new Flutter project.

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"
dependencies:
  flutter:
    sdk: flutter

  # Navigation
  go_router: ^14.6.3

  # Code Generation
  freezed: ^2.3.2
  freezed_annotation: ^2.2.0
  json_annotation: ^4.8.0
  json_serializable: ^6.6.1

  # State Management
  flutter_riverpod: ^2.1.1
  rxdart: ^0.28.0

  # Networking
  dio: ^5.8.0+1
  retrofit: ^4.4.2
  logger: any  #for logging purpose
  alice_lightweight: ^3.9.0

  # Local Storage
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0

  # Firebase
  firebase_core: ^3.10.0
  firebase_crashlytics: ^4.3.0
  firebase_analytics: ^11.4.0
  firebase_messaging: ^15.2.0 # push notification

  # Localization
  easy_localization: ^3.0.1
  easy_localization_loader: ^2.0.1

  stack_trace: ^1.11.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  # UI
  flutter_screenutil: ^5.6.0
  flutter_svg: ^2.0.4
  fl_chart: ^0.70.1
  smooth_page_indicator: ^1.1.0
  cached_network_image: ^3.2.3
  auto_size_text: ^3.0.0
  chewie: ^1.9.0
  video_player: ^2.9.2
  flutter_iconoir_ttf: ^0.1.1
  image_pixels: ^4.0.2
  table_calendar: ^3.1.3
  patterns_canvas: ^0.5.0
  flutter_staggered_grid_view: ^0.7.0
  visibility_detector: ^0.4.0+2
  flutter_animate: ^4.5.2
  jwt_decoder: ^2.0.1

  # Utils
  collection: ^1.19.1
  flutter_dotenv: ^5.2.1
  formz: ^0.8.0

  # etc
  sqflite_common_ffi: ^2.3.0

dev_dependencies:
  build_runner:
  flutter_gen_runner: ^5.2.0
  hive_generator: ^2.0.0
  retrofit_generator: ^9.1.7

  # Linting & analysis
  very_good_analysis: ^7.0.0
  custom_lint: ^0.7.0
  riverpod_lint: ^2.3.10
  flutter_lints: ^5.0.0


  flutter_test:
    sdk: flutter

  integration_test:
    sdk: flutter

  # Testing
  mocktail: ^1.0.3
  shared_preferences: ^2.5.3
  network_image_mock: ^2.1.1
  
dependency_overrides:
  intl: ^0.18.0

flutter_gen:
  output: lib/gen/ # Optional (default: lib/gen/)
  line_length: 80 # Optional (default: 80)

  integrations:
    flutter_svg: true
    rive: true

flutter:
  uses-material-design: true

  assets:
    - .env
    - assets/translations/
    - assets/images/
    - assets/images/skin_concerns/
    - assets/images/skin_types/
    - assets/icons/
    - assets/jsons/

  fonts:
    - family: Avenir
      fonts:
        - asset: assets/fonts/Avenir-Medium.otf
        - asset: assets/fonts/Avenir-Book.otf
        - asset: assets/fonts/Avenir-Black.otf
        - asset: assets/fonts/Avenir-Heavy.otf
        - asset: assets/fonts/Avenir-Light.otf
        - asset: assets/fonts/Avenir-Roman.otf
