/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/advance_package_icon.svg
  SvgGenImage get advancePackageIcon =>
      const SvgGenImage('assets/icons/advance_package_icon.svg');

  /// File path: assets/icons/apple_icon.svg
  SvgGenImage get appleIcon => const SvgGenImage('assets/icons/apple_icon.svg');

  /// File path: assets/icons/basic_package_icon.svg
  SvgGenImage get basicPackageIcon =>
      const SvgGenImage('assets/icons/basic_package_icon.svg');

  /// File path: assets/icons/chin_outline.png
  AssetGenImage get chinOutline =>
      const AssetGenImage('assets/icons/chin_outline.png');

  /// File path: assets/icons/face_outline.png
  AssetGenImage get faceOutline =>
      const AssetGenImage('assets/icons/face_outline.png');

  /// File path: assets/icons/facebook_icon.svg
  SvgGenImage get facebookIcon =>
      const SvgGenImage('assets/icons/facebook_icon.svg');

  /// File path: assets/icons/forehead_outline.png
  AssetGenImage get foreheadOutline =>
      const AssetGenImage('assets/icons/forehead_outline.png');

  /// File path: assets/icons/google_icon.svg
  SvgGenImage get googleIcon =>
      const SvgGenImage('assets/icons/google_icon.svg');

  /// File path: assets/icons/lips.png
  AssetGenImage get lips => const AssetGenImage('assets/icons/lips.png');

  /// File path: assets/icons/medical_trilogy_icon.svg
  SvgGenImage get medicalTrilogyIcon =>
      const SvgGenImage('assets/icons/medical_trilogy_icon.svg');

  /// File path: assets/icons/nose_outline.png
  AssetGenImage get noseOutline =>
      const AssetGenImage('assets/icons/nose_outline.png');

  /// File path: assets/icons/page_icon.svg
  SvgGenImage get pageIcon => const SvgGenImage('assets/icons/page_icon.svg');

  /// File path: assets/icons/page_notification_icon.svg
  SvgGenImage get pageNotificationIcon =>
      const SvgGenImage('assets/icons/page_notification_icon.svg');

  /// File path: assets/icons/plus_icon.svg
  SvgGenImage get plusIcon => const SvgGenImage('assets/icons/plus_icon.svg');

  /// File path: assets/icons/refresh_icon.svg
  SvgGenImage get refreshIcon =>
      const SvgGenImage('assets/icons/refresh_icon.svg');

  /// File path: assets/icons/slider_handler.png
  AssetGenImage get sliderHandler =>
      const AssetGenImage('assets/icons/slider_handler.png');

  /// File path: assets/icons/sort_icon.svg
  SvgGenImage get sortIcon => const SvgGenImage('assets/icons/sort_icon.svg');

  /// File path: assets/icons/user.svg
  SvgGenImage get user => const SvgGenImage('assets/icons/user.svg');

  /// List of all assets
  List<dynamic> get values => [
        advancePackageIcon,
        appleIcon,
        basicPackageIcon,
        chinOutline,
        faceOutline,
        facebookIcon,
        foreheadOutline,
        googleIcon,
        lips,
        medicalTrilogyIcon,
        noseOutline,
        pageIcon,
        pageNotificationIcon,
        plusIcon,
        refreshIcon,
        sliderHandler,
        sortIcon,
        user
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/RGB.png
  AssetGenImage get rgb => const AssetGenImage('assets/images/RGB.png');

  /// File path: assets/images/actual-age.png
  AssetGenImage get actualAge =>
      const AssetGenImage('assets/images/actual-age.png');

  /// File path: assets/images/area_face.png
  AssetGenImage get areaFace =>
      const AssetGenImage('assets/images/area_face.png');

  /// File path: assets/images/authentication.svg
  SvgGenImage get authentication =>
      const SvgGenImage('assets/images/authentication.svg');

  /// File path: assets/images/dummy-face.png
  AssetGenImage get dummyFace =>
      const AssetGenImage('assets/images/dummy-face.png');

  /// File path: assets/images/english.png
  AssetGenImage get english => const AssetGenImage('assets/images/english.png');

  /// File path: assets/images/facial-skin-actual-age.png
  AssetGenImage get facialSkinActualAge =>
      const AssetGenImage('assets/images/facial-skin-actual-age.png');

  /// File path: assets/images/facial-skin-age-equal.png
  AssetGenImage get facialSkinAgeEqual =>
      const AssetGenImage('assets/images/facial-skin-age-equal.png');

  /// File path: assets/images/facial-skin-age-older.png
  AssetGenImage get facialSkinAgeOlder =>
      const AssetGenImage('assets/images/facial-skin-age-older.png');

  /// File path: assets/images/facial-skin-age.png
  AssetGenImage get facialSkinAge =>
      const AssetGenImage('assets/images/facial-skin-age.png');

  /// File path: assets/images/facial-skin-younger.png
  AssetGenImage get facialSkinYounger =>
      const AssetGenImage('assets/images/facial-skin-younger.png');

  /// File path: assets/images/indonesia.png
  AssetGenImage get indonesia =>
      const AssetGenImage('assets/images/indonesia.png');

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/persona1.png
  AssetGenImage get persona1 =>
      const AssetGenImage('assets/images/persona1.png');

  /// File path: assets/images/persona2.png
  AssetGenImage get persona2 =>
      const AssetGenImage('assets/images/persona2.png');

  /// File path: assets/images/scanning-bar.png
  AssetGenImage get scanningBar =>
      const AssetGenImage('assets/images/scanning-bar.png');

  /// File path: assets/images/serum_preview.jpeg
  AssetGenImage get serumPreview =>
      const AssetGenImage('assets/images/serum_preview.jpeg');

  /// Directory path: assets/images/skin_concerns
  $AssetsImagesSkinConcernsGen get skinConcerns =>
      const $AssetsImagesSkinConcernsGen();

  /// Directory path: assets/images/skin_types
  $AssetsImagesSkinTypesGen get skinTypes => const $AssetsImagesSkinTypesGen();

  /// File path: assets/images/sunscreen_preview.jpeg
  AssetGenImage get sunscreenPreview =>
      const AssetGenImage('assets/images/sunscreen_preview.jpeg');

  /// File path: assets/images/treatment-image.png
  AssetGenImage get treatmentImage =>
      const AssetGenImage('assets/images/treatment-image.png');

  /// File path: assets/images/welcoming_screen_face.png
  AssetGenImage get welcomingScreenFace =>
      const AssetGenImage('assets/images/welcoming_screen_face.png');

  /// File path: assets/images/with-treatment.jpeg
  AssetGenImage get withTreatment =>
      const AssetGenImage('assets/images/with-treatment.jpeg');

  /// File path: assets/images/without-treatment.jpeg
  AssetGenImage get withoutTreatment =>
      const AssetGenImage('assets/images/without-treatment.jpeg');

  /// File path: assets/images/wrinkles.png
  AssetGenImage get wrinkles =>
      const AssetGenImage('assets/images/wrinkles.png');

  /// List of all assets
  List<dynamic> get values => [
        rgb,
        actualAge,
        areaFace,
        authentication,
        dummyFace,
        english,
        facialSkinActualAge,
        facialSkinAgeEqual,
        facialSkinAgeOlder,
        facialSkinAge,
        facialSkinYounger,
        indonesia,
        logo,
        persona1,
        persona2,
        scanningBar,
        serumPreview,
        sunscreenPreview,
        treatmentImage,
        welcomingScreenFace,
        withTreatment,
        withoutTreatment,
        wrinkles
      ];
}

class $AssetsJsonsGen {
  const $AssetsJsonsGen();

  /// File path: assets/jsons/coordinates-response-4.json
  String get coordinatesResponse4 => 'assets/jsons/coordinates-response-4.json';

  /// File path: assets/jsons/coordinates-response-5.json
  String get coordinatesResponse5 => 'assets/jsons/coordinates-response-5.json';

  /// File path: assets/jsons/example.json
  String get example => 'assets/jsons/example.json';

  /// File path: assets/jsons/user.json
  String get user => 'assets/jsons/user.json';

  /// File path: assets/jsons/wrinkle-response-scaled.json
  String get wrinkleResponseScaled =>
      'assets/jsons/wrinkle-response-scaled.json';

  /// File path: assets/jsons/wrinkle-response.json
  String get wrinkleResponse => 'assets/jsons/wrinkle-response.json';

  /// List of all assets
  List<String> get values => [
        coordinatesResponse4,
        coordinatesResponse5,
        example,
        user,
        wrinkleResponseScaled,
        wrinkleResponse
      ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/en-US.json
  String get enUS => 'assets/translations/en-US.json';

  /// File path: assets/translations/id-ID.json
  String get idID => 'assets/translations/id-ID.json';

  /// List of all assets
  List<String> get values => [enUS, idID];
}

class $AssetsImagesSkinConcernsGen {
  const $AssetsImagesSkinConcernsGen();

  /// File path: assets/images/skin_concerns/acne.png
  AssetGenImage get acne =>
      const AssetGenImage('assets/images/skin_concerns/acne.png');

  /// File path: assets/images/skin_concerns/brightening.png
  AssetGenImage get brightening =>
      const AssetGenImage('assets/images/skin_concerns/brightening.png');

  /// File path: assets/images/skin_concerns/no_concern.png
  AssetGenImage get noConcern =>
      const AssetGenImage('assets/images/skin_concerns/no_concern.png');

  /// File path: assets/images/skin_concerns/pigmentation.png
  AssetGenImage get pigmentation =>
      const AssetGenImage('assets/images/skin_concerns/pigmentation.png');

  /// File path: assets/images/skin_concerns/pores.png
  AssetGenImage get pores =>
      const AssetGenImage('assets/images/skin_concerns/pores.png');

  /// File path: assets/images/skin_concerns/scar.png
  AssetGenImage get scar =>
      const AssetGenImage('assets/images/skin_concerns/scar.png');

  /// File path: assets/images/skin_concerns/sensitive.png
  AssetGenImage get sensitive =>
      const AssetGenImage('assets/images/skin_concerns/sensitive.png');

  /// File path: assets/images/skin_concerns/wrinkle.png
  AssetGenImage get wrinkle =>
      const AssetGenImage('assets/images/skin_concerns/wrinkle.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        acne,
        brightening,
        noConcern,
        pigmentation,
        pores,
        scar,
        sensitive,
        wrinkle
      ];
}

class $AssetsImagesSkinTypesGen {
  const $AssetsImagesSkinTypesGen();

  /// File path: assets/images/skin_types/normal.png
  AssetGenImage get normal =>
      const AssetGenImage('assets/images/skin_types/normal.png');

  /// File path: assets/images/skin_types/thick.png
  AssetGenImage get thick =>
      const AssetGenImage('assets/images/skin_types/thick.png');

  /// File path: assets/images/skin_types/thin.png
  AssetGenImage get thin =>
      const AssetGenImage('assets/images/skin_types/thin.png');

  /// List of all assets
  List<AssetGenImage> get values => [normal, thick, thin];
}

class Assets {
  Assets._();

  static const String aEnv = '.env';
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsJsonsGen jsons = $AssetsJsonsGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();

  /// List of all assets
  static List<String> get values => [aEnv];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
