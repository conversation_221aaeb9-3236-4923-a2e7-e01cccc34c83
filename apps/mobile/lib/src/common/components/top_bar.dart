import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/button.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/common/services/auth_service.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CommonTopBar extends ConsumerWidget implements PreferredSizeWidget {
  const CommonTopBar({
    required this.title,
    this.showBackButton = false,
    this.backButtonText = 'Back',
    super.key,
  });

  final String title;
  final bool showBackButton;
  final String backButtonText;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authService = ref.watch(authServiceProvider);
    final user = authService.currentUser;
    return Container(
      height: AppSizes.h72,
      margin: EdgeInsets.symmetric(
        vertical: AppSizes.h16,
        horizontal: AppSizes.w32,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppSizes.w(16),
        vertical: AppSizes.h(12),
      ),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: .3),
        borderRadius: BorderRadius.circular(AppSizes.h40),
        border: Border.all(color: Colors.white, width: AppSizes.h(2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: .08),
            offset: const Offset(0, 20),
            blurRadius: 20,
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Logo
          Align(
            alignment: showBackButton ? Alignment.center : Alignment.centerLeft,
            child: Assets.images.logo.image(
              width: AppSizes.w(85),
              height: AppSizes.h40,
            ),
          ),

          Align(
            alignment: showBackButton ? Alignment.centerLeft : Alignment.center,
            child: showBackButton
                ? CommonButton(
                    onPressed: () => context.pop(),
                    prefixIcon: const Icon(IconoirIcons.navArrowLeft),
                    variant: ButtonVariant.outlined,
                    isStretch: false,
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSizes.w(16),
                      vertical: AppSizes.h4,
                    ),
                    child: Text(
                      backButtonText,
                      style: context.small,
                    ),
                  )
                : !showBackButton
                    ? Text(
                        title,
                        style: context.heading4,
                      )
                    : const SizedBox(),
          ),

          // Right side (profile)
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: () {
                final path = GoRouter.of(context).state?.path;
                if (path != Routes.profile.path) {
                  context.pushNamed(Routes.profile.name);
                }
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircleAvatar(
                    backgroundColor: context.appColors.fgPrimaryColor,
                    child: Icon(
                      IconoirIcons.user,
                      color: context.appColors.fgBaseColor,
                    ),
                  ),
                  Gap.w8,
                  Text(
                    user?.name ?? '-',
                    style: context.small,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(AppSizes.h96);
}
