import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/dropdown/dropdown.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/device_size.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';

class CommonDropdownOverlay extends StatelessWidget {
  const CommonDropdownOverlay({
    required this.dropdownMenuEntries,
    required this.onChanged,
    required this.selectedItem,
    this.isCheckboxStyle = false,
    super.key,
  });

  final List<String> dropdownMenuEntries;
  final SelectedValueCallback onChanged;
  final String? selectedItem;
  final bool isCheckboxStyle;
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: context.height * 0.5,
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: LightColors.neutralColor.shade300,
          ),
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          itemCount: dropdownMenuEntries.length,
          itemBuilder: (context, index) {
            final item = dropdownMenuEntries[index];
            final isSelected = item == selectedItem;

            return ListTile(
              dense: true,
              title: Text(item),
              horizontalTitleGap: isCheckboxStyle ? 0 : AppSizes.w4,
              leading: isCheckboxStyle
                  ? Radio(
                      value: item,
                      groupValue: selectedItem,
                      onChanged: (value) => onChanged(value ?? ''),
                    )
                  : isSelected
                      ? Icon(
                          IconoirIcons.check,
                          color: LightColors.neutralColor.shade900,
                          size: AppSizes.w16,
                        )
                      : Gap.w16,
              onTap: () => onChanged(item),
            );
          },
        ),
      ),
    );
  }
}
