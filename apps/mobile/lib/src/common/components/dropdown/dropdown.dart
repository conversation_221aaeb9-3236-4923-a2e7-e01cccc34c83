import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/dropdown/dropdown_overlay.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';

typedef SelectedValueCallback = void Function(String onChanged);

class CommonDropdown extends StatefulWidget {
  const CommonDropdown({
    required this.dropdownMenuEntries,
    required this.onSelected,
    super.key,
    this.width,
    this.height,
    this.overlayWidth,
    this.initialValue,
    this.hint,
    this.overlayWidget,
    this.isCheckboxStyle = false,
  });

  final double? width;
  final double? height;
  final double? overlayWidth;
  final List<String> dropdownMenuEntries;
  final void Function(String?) onSelected;
  final Widget? overlayWidget;
  final String? initialValue;
  final String? hint;
  final bool isCheckboxStyle;

  @override
  State<CommonDropdown> createState() => CommonDropdownState();
}

class CommonDropdownState extends State<CommonDropdown> {
  String? _selectedItem;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  bool _showOverlayAbove = false;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialValue;
  }

  void setSelectedItem(String? selectedItem) {
    setState(() {
      _selectedItem = selectedItem;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: () {
          if (_overlayEntry == null) {
            setState(() {
              _overlayEntry = _createOverlayEntry();
              Overlay.of(context).insert(_overlayEntry!);
            });
          } else {
            _removeOverlay();
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          width: widget.width,
          height: widget.height,
          child: InputDecorator(
            decoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppSizes.w12,
              ),
              border: OutlineInputBorder(
                borderSide: BorderSide(
                  color: LightColors.neutralColor.shade300,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: LightColors.neutralColor.shade300,
                ),
              ),
            ),
            child: widget.overlayWidget != null
                ? widget.overlayWidget!
                : _selectedItem == null
                    ? Row(
                        children: [
                          Expanded(
                            child: Text(
                              widget.hint ?? '',
                            ),
                          ),
                          Icon(
                            _overlayEntry == null
                                ? IconoirIcons.navArrowDown
                                : IconoirIcons.navArrowUp,
                            color: LightColors.neutralColor,
                            size: AppSizes.w16,
                          ),
                        ],
                      )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              _selectedItem!,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Icon(
                            _overlayEntry == null
                                ? IconoirIcons.navArrowDown
                                : IconoirIcons.navArrowUp,
                            color: LightColors.neutralColor,
                            size: AppSizes.w16,
                          ),
                        ],
                      ),
          ),
        ),
      ),
    );
  }

  OverlayEntry _createOverlayEntry() {
    // Get the size and position information of the widget
    final renderBox = context.findRenderObject() as RenderBox?;
    final size = renderBox!.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // Get the screen size
    final screenSize = MediaQuery.of(context).size;

    // Estimate the overlay height based on number of items
    // You can adjust this according to the actual size of your overlay
    const estimatedItemHeight = 40.0; // Estimated height per item
    const maxOverlayHeight =
        200.0; // Match the constraint in CommonDropdownOverlay

    // Calculate actual overlay height, capped at maxOverlayHeight
    final rawEstimatedHeight =
        widget.dropdownMenuEntries.length * estimatedItemHeight;
    final estimatedOverlayHeight =
        rawEstimatedHeight.clamp(0.0, maxOverlayHeight);

    // Check if there's enough space below to display the overlay
    final spaceBelow = screenSize.height - offset.dy - size.height;
    _showOverlayAbove = spaceBelow < estimatedOverlayHeight;

    final dropdownWidth = size.width;
    final overlayWidth = widget.overlayWidth ?? widget.width ?? dropdownWidth;
    final offsetX = (dropdownWidth - overlayWidth) / 2; // Centering the overlay

    // Determine Y offset based on available space
    final offsetY = _showOverlayAbove
        ? -estimatedOverlayHeight // Appear above
        : size.height; // Appear below

    return OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _removeOverlay,
        child: Stack(
          children: [
            Positioned(
              width: overlayWidth,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(offsetX, offsetY),
                child: CommonDropdownOverlay(
                  dropdownMenuEntries: widget.dropdownMenuEntries,
                  onChanged: (selectedItem) {
                    setState(() {
                      _selectedItem = selectedItem;
                    });
                    widget.onSelected(_selectedItem);
                    _removeOverlay();
                  },
                  selectedItem: _selectedItem,
                  isCheckboxStyle: widget.isCheckboxStyle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _removeOverlay() {
    setState(() {
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }
}
