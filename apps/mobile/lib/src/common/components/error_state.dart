import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:flutter/material.dart';

class CommonErrorState extends StatelessWidget {
  const CommonErrorState({
    required this.errorMessage,
    super.key,
    this.onRetry,
  });

  final String errorMessage;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: AppSizes.w(400),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              errorMessage,
              style: context.p,
            ),
            if (onRetry != null) ...[
              Gap.h16,
              CommonButton(
                onPressed: onRetry!,
                padding: EdgeInsets.symmetric(
                  horizontal: AppSizes.w24,
                  vertical: AppSizes.h12,
                ),
                child: const Text('Retry'),
              ),
            ]
          ],
        ),
      ),
    );
  }
}
