import 'package:flutter/material.dart';

class InnerShadow extends StatelessWidget {
  const InnerShadow({
    required this.child,
    this.colors = const [
      Colors.transparent,
      Colors.white,
    ],
    this.spread = .8,
    this.borderRadius,
    this.blendMode = BlendMode.plus,
    super.key,
  });

  final Widget child;
  final double spread;
  final List<Color> colors;
  final BlendMode blendMode;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    final spreadWidth = 1 - spread;
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: ShaderMask(
        shaderCallback: (Rect bounds) {
          return LinearGradient(
            begin: Alignment.center,
            end: Alignment.centerLeft,
            colors: colors,
            stops: [
              spreadWidth,
              1.0,
            ],
            tileMode: TileMode.mirror,
          ).createShader(bounds);
        },
        blendMode: blendMode,
        child: ShaderMask(
          shaderCallback: (Rect bounds) {
            return LinearGradient(
              begin: Alignment.center,
              end: Alignment.bottomCenter,
              colors: colors,
              stops: [
                spreadWidth,
                1.0,
              ],
              tileMode: TileMode.mirror,
            ).createShader(bounds);
          },
          blendMode: blendMode,
          child: child,
        ),
      ),
    );
  }
}
