import 'dart:math' as math;

import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/device_size.dart';
import 'package:flutter/material.dart';

class CommonScaffold extends StatelessWidget {
  const CommonScaffold({
    super.key,
    this.body,
    this.appBar,
    this.bottomNavBar,
    this.backgroundColor,
  });

  final PreferredSizeWidget? appBar;
  final Widget? body;
  final Widget? bottomNavBar;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final screenWidth = context.width;
    final screenHeight = context.height;
    final circleDiameter =
        math.sqrt(math.pow(screenWidth, 2) + math.pow(screenHeight, 2)) * 1.4;

    return Stack(
      children: [
        Positioned(
          top: (screenHeight - circleDiameter) / 2,
          left: (screenWidth - circleDiameter) / 2.5,
          child: EllipseBackground(circleDiameter: circleDiameter),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: appBar,
          body: Stack(
            children: [
              SizedBox(
                height: screenHeight,
                child: body ?? const SizedBox.shrink(),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: bottomNavBar ?? const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
