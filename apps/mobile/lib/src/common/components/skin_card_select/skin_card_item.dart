part of 'skin_card_select.dart';

class _SkinCardItem<T> extends StatelessWidget implements SkinCardOption<T> {
  const _SkinCardItem({
    required this.label,
    super.key,
    this.description,
    this.image,
    this.value,
    this.isActive = false,
    this.onTap,
    this.onTapRemove,
  });

  @override
  final String label;

  @override
  final String? description;

  @override
  final String? image;

  @override
  final T? value;

  final bool isActive;

  final VoidCallback? onTap;

  final VoidCallback? onTapRemove;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
        margin: EdgeInsets.all(
          isActive ? 0 : AppSizes.w12,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizes.r24),
          border: isActive
              ? Border.all(
                  color: context.appColors.borderWhite,
                  width: AppSizes.w(5),
                )
              : null,
          boxShadow: [
            if (isActive) ...[
              BoxShadow(
                color: context.appColors.borderBaseColor.withAlpha(76),
                blurRadius: AppSizes.r8,
                spreadRadius: AppSizes.r4,
                offset: const Offset(0, 4),
              ),
            ],
          ],
        ),
        child: InnerShadow(
          spread: .2,
          borderRadius: BorderRadius.circular(AppSizes.w(16)),
          colors: isActive
              ? [
                  Colors.transparent,
                  Colors.white24,
                ]
              : [
                  Colors.transparent,
                  Colors.transparent,
                ],
          child: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              image: image != null
                  ? DecorationImage(
                      image: AssetImage(image!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: Stack(
              children: [
                if (onTapRemove != null && isActive)
                  Positioned(
                    top: AppSizes.h8,
                    right: AppSizes.w8,
                    child: CommonButton(
                      padding: EdgeInsets.all(AppSizes.w(10)),
                      onPressed: onTapRemove!,
                      variant: ButtonVariant.outlined,
                      child: const Icon(IconoirIcons.xmark),
                    ),
                  ),
                Padding(
                  padding: EdgeInsets.all(AppSizes.w16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.end,
                    spacing: AppSizes.h8,
                    children: [
                      Text(
                        label,
                        style: context.heading4?.copyWith(
                          color: context.appColors.textOncolor,
                        ),
                      ),
                      if (description != null)
                        LayoutBuilder(
                          builder: (context, constraints) {
                            return SizedBox(
                              width:
                                  (constraints.maxWidth / 100).floorToDouble() *
                                      100,
                              child: Text(
                                description!,
                                style: context.large?.copyWith(
                                  color: context.appColors.textOncolor,
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
