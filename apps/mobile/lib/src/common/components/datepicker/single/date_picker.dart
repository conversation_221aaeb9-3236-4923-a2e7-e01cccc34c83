import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/app/themes/foundation/sizes.dart';
import 'package:euromedica_aizer/src/common/components/datepicker/single/date_picker_overlay.dart';
import 'package:euromedica_aizer/src/common/components/textfield.dart';
import 'package:euromedica_aizer/src/utils/extensions/date_time_extension.dart';
import 'package:flutter/material.dart';

class CommonDatePicker extends StatefulWidget {
  const CommonDatePicker({
    required this.onSelected,
    super.key,
    this.initialDate,
    this.decoration,
    this.hint,
  });

  final void Function(DateTime?) onSelected;
  final DateTime? initialDate;
  final InputDecoration? decoration;
  final String? hint;
  @override
  State<CommonDatePicker> createState() => _CommonDatePickerState();
}

class _CommonDatePickerState extends State<CommonDatePicker> {
  final _focusNode = FocusNode();
  final _dateController = TextEditingController();

  DateTime? _selectedDate;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  void _setSelectedDate(DateTime? date) {
    setState(() {
      _selectedDate = date;
    });
    _dateController.text = date?.toDayMonthYear() ?? '';
  }

  void _handleFocusChange() {
    if (!_focusNode.hasFocus && _overlayEntry != null) {
      if (_overlayEntry != null) {
        _focusNode.requestFocus();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _setSelectedDate(widget.initialDate);
    _focusNode
      ..requestFocus()
      ..addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    _dateController.dispose();
    _focusNode
      ..removeListener(_handleFocusChange)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: SizedBox(
        height: AppSizes.h(60),
        child: CommonTextField(
          focusNode: _focusNode,
          readOnly: true,
          controller: _dateController,
          hintText: widget.hint ?? 'Pick a date',
          onTap: _onTap,
          decoration: widget.decoration?.copyWith(
            border: const OutlineInputBorder(borderSide: BorderSide.none),
            enabledBorder: const OutlineInputBorder(
              borderSide: BorderSide.none,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppSizes.w16,
            ),
          ),
        ),
      ),
    );
  }

  void _onTap() {
    if (_overlayEntry == null) {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    } else {
      _removeOverlay();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox?;
    final size = renderBox?.size;

    final dropdownWidth = size?.width;
    final overlayWidth = AppSizes.w(320) - AppSizes.w20;
    final offsetX = (dropdownWidth ?? 0) - overlayWidth;

    return OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _removeOverlay,
        child: Stack(
          children: [
            Positioned(
              width: overlayWidth,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(offsetX, AppSizes.h(64)),
                child: DatePickerOverlay(
                  initialDate: _selectedDate,
                  onDateSelected: _onDateSelected,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onDateSelected(DateTime? date) {
    _setSelectedDate(date);
    widget.onSelected(date);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _focusNode.unfocus();
  }
}
