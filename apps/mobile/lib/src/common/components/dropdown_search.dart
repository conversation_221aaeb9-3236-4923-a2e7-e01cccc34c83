import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A dropdown with search functionality that looks like a combobox.
///
/// This widget allows users to select an item from a dropdown list
/// with the ability to search through items, similar to a combobox.
class DropdownSearch<T> extends StatefulWidget {
  const DropdownSearch({
    required this.items,
    required this.itemBuilder,
    required this.onChanged,
    super.key,
    this.selectedItem,
    this.hintText = 'Select an item',
    this.labelText,
    this.searchHintText = 'Search...',
    this.isEnabled = true,
    this.showSearchField = true,
    this.itemAsString,
    this.dropdownMaxHeight,
    this.dropdownWidth,
    this.searchFieldDecoration,
  });

  /// List of items to display in the dropdown.
  final List<T> items;

  /// Builder function to build each item in the dropdown.
  final Widget Function(BuildContext, T) itemBuilder;

  /// Function called when an item is selected.
  final void Function(T?) onChanged;

  /// Currently selected item.
  final T? selectedItem;

  /// Hint text shown when no item is selected.
  final String hintText;

  /// Label text for the field (optional).
  final String? labelText;

  /// Hint text for the search field.
  final String searchHintText;

  /// Whether the dropdown is enabled.
  final bool isEnabled;

  /// Whether to show the search field.
  final bool showSearchField;

  /// Function to convert item to string for searching.
  final String Function(T)? itemAsString;

  /// Maximum height of the dropdown list.
  final double? dropdownMaxHeight;

  /// Width of the dropdown list.
  final double? dropdownWidth;

  /// Decoration for the search field.
  final InputDecoration? searchFieldDecoration;

  @override
  State<DropdownSearch<T>> createState() => _DropdownSearchState<T>();
}

class _DropdownSearchState<T> extends State<DropdownSearch<T>> {
  final LayerLink _layerLink = LayerLink();
  final TextEditingController _searchController = TextEditingController();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  String _searchQuery = '';
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus && _isOpen) {
        _closeDropdown();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _overlayEntry?.remove();
    super.dispose();
  }

  void _toggleDropdown() {
    if (!widget.isEnabled) return;

    setState(() {
      if (_isOpen) {
        _closeDropdown();
      } else {
        _openDropdown();
      }
    });
  }

  void _openDropdown() {
    _focusNode.requestFocus();
    _searchController.clear();
    _searchQuery = '';
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    _isOpen = true;
  }

  void _closeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isOpen = false;
    setState(() {});
  }

  void _selectItem(T item) {
    widget.onChanged(item);
    _closeDropdown();
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject()! as RenderBox;
    final size = renderBox.size;

    final screenHeight = MediaQuery.of(context).size.height;
    final offset = renderBox.localToGlobal(Offset.zero);
    final availableSpace = screenHeight - offset.dy - size.height;
    final isReversed = availableSpace < 200.h;

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Modal barrier to detect taps outside the dropdown
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: _closeDropdown,
              child: Container(color: Colors.transparent),
            ),
          ),
          // Dropdown positioned relative to the field
          Positioned(
            width: widget.dropdownWidth ?? size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: isReversed
                  ? Offset(0, -(widget.dropdownMaxHeight ?? 200.h))
                  : Offset.zero,
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(AppSizes.r8),
                color: context.appColors.fgBaseColor,
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: widget.dropdownMaxHeight ?? 400.h,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.showSearchField)
                        Container(
                          padding: EdgeInsets.only(
                            top: AppSizes.h12,
                            left: AppSizes.w12,
                            right: AppSizes.w12,
                          ),
                          child: TextField(
                            controller: _searchController,
                            decoration: widget.searchFieldDecoration ??
                                InputDecoration(
                                  hintText: widget.searchHintText,
                                  hintStyle: context.p?.copyWith(
                                    color: context.appColors.textSubtleColor,
                                  ),
                                  isDense: true,
                                  contentPadding: EdgeInsets.symmetric(
                                    vertical: AppSizes.h12,
                                    horizontal: AppSizes.w16,
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          context.appColors.borderSubtleColor,
                                    ),
                                    borderRadius:
                                        BorderRadius.circular(AppSizes.r8),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          context.appColors.borderSubtleColor,
                                    ),
                                    borderRadius:
                                        BorderRadius.circular(AppSizes.r8),
                                  ),
                                ),
                            onChanged: (value) {
                              setState(() {
                                _searchQuery = value.toLowerCase();
                                _overlayEntry?.markNeedsBuild();
                              });
                            },
                          ),
                        ),
                      if (_filteredItems.isEmpty) ...[
                        Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: AppSizes.h24,
                          ),
                          child: Center(
                            child: Text('No Data', style: context.p),
                          ),
                        )
                      ],
                      Flexible(
                        child: ListView.builder(
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          itemCount: _filteredItems.length,
                          itemBuilder: (context, index) {
                            final item = _filteredItems[index];
                            return InkWell(
                              onTap: () => _selectItem(item),
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSizes.w16,
                                  vertical: AppSizes.h12,
                                ),
                                child: widget.itemBuilder(context, item),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<T> get _filteredItems {
    if (_searchQuery.isEmpty) return widget.items;

    return widget.items.where((item) {
      final itemStr = widget.itemAsString != null
          ? widget.itemAsString!(item).toLowerCase()
          : item.toString().toLowerCase();
      return itemStr.contains(_searchQuery);
    }).toList();
  }

  String _getSelectedItemAsString() {
    if (widget.selectedItem == null) return widget.hintText;
    return widget.itemAsString != null
        ? widget.itemAsString!(widget.selectedItem as T)
        : widget.selectedItem.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.labelText != null)
          Padding(
            padding: EdgeInsets.only(bottom: AppSizes.h8),
            child: Row(
              children: [
                Text(
                  widget.labelText!,
                  style: context.p?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        CompositedTransformTarget(
          link: _layerLink,
          child: GestureDetector(
            onTap: _toggleDropdown,
            child: Container(
              height: AppSizes.h(60),
              padding: EdgeInsets.symmetric(
                horizontal: AppSizes.w16,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSizes.r8),
                border: Border.all(
                  color: context.appColors.borderSubtleColor,
                ),
                color: context.appColors.fgBaseColor,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      _getSelectedItemAsString(),
                      style: context.p?.copyWith(
                        color: widget.selectedItem == null
                            ? context.appColors.textPlaceholderColor
                            : context.appColors.textBaseColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    _isOpen
                        ? IconoirIcons.navArrowUp
                        : IconoirIcons.navArrowDown,
                    color: context.appColors.borderSubtleColor,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
