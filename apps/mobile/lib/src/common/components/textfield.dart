import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';

class CommonTextField extends StatefulWidget {
  const CommonTextField({
    this.label,
    super.key,
    this.controller,
    this.hintText,
    this.hintColor,
    this.onChanged,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.inputType = TextInputType.text,
    this.validator,
    this.onEditingComplete,
    this.caption,
    this.enabled = true,
    this.onTap,
    this.readOnly = false,
    this.focusNode,
    this.decoration,
  });

  final String? label;
  final TextEditingController? controller;
  final String? hintText;
  final Color? hintColor;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onEditingComplete;
  final bool obscureText;
  final TextInputType inputType;
  final void Function(String)? onChanged;
  final FormFieldValidator<String>? validator;
  final String? caption;
  final bool enabled;
  final VoidCallback? onTap;
  final bool readOnly;
  final FocusNode? focusNode;
  final InputDecoration? decoration;

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    _focusNode
      ..removeListener(_handleFocusChange)
      ..dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final isFocused = _focusNode.hasFocus;
    final iconColor = isFocused
        ? LightColors.neutralColor
        : LightColors.neutralColor.shade300;

    final defaultDecoration = InputDecoration(
      errorStyle: context.p?.copyWith(
        color: context.appColors.textErrorColor,
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(
          color: LightColors.neutralColor.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: context.appColors.fgPrimaryColor,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: LightColors.neutralColor.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: LightColors.neutralColor.shade200,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      hintText: widget.hintText,
      suffixIcon: widget.suffixIcon != null
          ? IconTheme(
              data: IconThemeData(color: iconColor),
              child: widget.suffixIcon!,
            )
          : null,
      prefixIcon: widget.prefixIcon != null
          ? IconTheme(
              data: IconThemeData(color: iconColor),
              child: widget.prefixIcon!,
            )
          : null,
      helperText: widget.caption,
      helperStyle: context.subtle?.copyWith(
        color: widget.enabled
            ? LightColors.neutralColor.shade700
            : LightColors.neutralColor.shade300,
      ),
      hintStyle: context.p?.copyWith(
        color: widget.enabled
            ? widget.hintColor ?? LightColors.neutralColor.shade400
            : LightColors.neutralColor.shade300,
      ),
      filled: true,
      fillColor: widget.enabled
          ? Colors.transparent
          : LightColors.neutralColor.shade100,
    );

    // combine default decoration with widget decoration if provided
    final decoration = widget.decoration == null
        ? defaultDecoration
        : defaultDecoration.copyWith(
            labelText: widget.decoration!.labelText,
            labelStyle: widget.decoration!.labelStyle,
            helperText: widget.decoration!.helperText,
            helperStyle: widget.decoration!.helperStyle,
            hintText: widget.decoration!.hintText,
            hintStyle: widget.decoration!.hintStyle,
            hintTextDirection: widget.decoration!.hintTextDirection,
            errorText: widget.decoration!.errorText,
            errorStyle: widget.decoration!.errorStyle,
            prefixIcon: widget.decoration!.prefixIcon,
            suffixIcon: widget.decoration!.suffixIcon,
            border: widget.decoration!.border,
            enabledBorder: widget.decoration!.enabledBorder,
            focusedBorder: widget.decoration!.focusedBorder,
            errorBorder: widget.decoration!.errorBorder,
            focusedErrorBorder: widget.decoration!.focusedErrorBorder,
            contentPadding: widget.decoration!.contentPadding,
            fillColor: widget.decoration!.fillColor,
            filled: widget.decoration!.filled,
          );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.label != null)
          Padding(
            padding: const EdgeInsets.only(top: 15, bottom: 4),
            child: Text(
              widget.label!,
              style: context.small?.copyWith(
                color: LightColors.neutralColor.shade900,
              ),
            ),
          ),
        TextFormField(
          readOnly: widget.readOnly,
          enabled: widget.enabled,
          style: context.p?.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: LightColors.neutralColor.shade900,
          ),
          cursorColor: context.appColors.fgPrimaryColor,
          controller: widget.controller,
          focusNode: _focusNode,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          keyboardType: widget.inputType,
          validator: widget.validator,
          onChanged: widget.onChanged,
          obscureText: widget.obscureText,
          obscuringCharacter: '*',
          onEditingComplete: widget.onEditingComplete,
          onTapOutside: (_) => FocusManager.instance.primaryFocus?.unfocus(),
          onTap: widget.onTap,
          decoration: decoration,
        ),
      ],
    );
  }
}
