import 'package:euromedica_aizer/src/common/data/models/responses/treatment_product_response.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';

class TreatmentProductMapper {
  static TreatmentProduct mapResponseToDomain(
    TreatmentProductResponse response,
  ) {
    return TreatmentProduct(
      id: response.id ?? '',
      itemCode: response.itemCode ?? '',
      name: response.name ?? '',
      description: response.description ?? '',
      categories: response.category?.map((e) => e.name ?? '').toList() ?? [],
      quantity: response.quantity ?? 0,
      price: response.price ?? 0,
      isTopRecommendation: false,
      solvedConcerns: response.concern?.map((e) => e.name ?? '').toList() ?? [],
      mediaUrl: response.mediaUrl ?? '',
      thumbnailUrl: response.thumbnailUrl ?? '',
    );
  }

  static List<TreatmentProduct> mapResponseListToDomain(
    List<TreatmentProductResponse> responses,
  ) {
    return responses.map(mapResponseToDomain).toList();
  }
}
