import 'package:dio/dio.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/api_response.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/login_response.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/refresh_token_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/dio_client.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';

part 'auth_api.g.dart';

@RestApi()
abstract class AuthApi {
  factory AuthApi(Dio dio) = _AuthApi;

  @POST(Endpoint.login)
  Future<ApiResponse<LoginResponse>> login(@Body() Map<String, dynamic> body);

  @POST(Endpoint.refreshToken)
  Future<ApiResponse<RefreshTokenResponse>> refreshToken(
    @Body() Map<String, dynamic> body,
  );
}

// Explicitly specify the provider type to help type inference and break the cycle.
final Provider<AuthApi> authApiProvider = Provider<AuthApi>(
  (ref) => AuthApi(ref.read(apiDioClientProvider).dio),
);
