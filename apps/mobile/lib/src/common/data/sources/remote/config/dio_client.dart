import 'package:alice_lightweight/alice.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:euromedica_aizer/src/app/config/config.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/alice_provider.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/auth_interceptor.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

const _defaultConnectTimeout = Duration(milliseconds: 60000);
const _defaultReceiveTimeout = Duration(milliseconds: 60000);

class DioClient {
  DioClient({
    required HiveService hiveService,
    required Alice alice,
    required String baseUrl,
    List<Interceptor>? interceptors,
    bool withTokenInterceptor = true,
    Duration connectTimeout = _defaultConnectTimeout,
    Duration receiveTimeout = _defaultReceiveTimeout,
  }) {
    _dio = Dio()
      ..options.baseUrl = baseUrl
      ..options.connectTimeout = connectTimeout
      ..options.receiveTimeout = receiveTimeout
      ..httpClientAdapter
      ..options.headers = {
        'Content-Type': 'application/json; charset=UTF-8',
      };

    (_dio.httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (certificate, host, port) => true;

    if (withTokenInterceptor) {
      _dio.interceptors.add(
        AuthInterceptor(
          dio: dio,
          hiveService: hiveService,
          alice: alice,
        ),
      );
    }

    if (interceptors?.isNotEmpty ?? false) {
      _dio.interceptors.addAll(interceptors!);
    }

    if (AppConfig.showAliceInspector.value) {
      dio.interceptors.add(alice.getDioInterceptor());
    }
  }

  late Dio _dio;

  Dio get dio => _dio;
}

final apiDioClientProvider = Provider<DioClient>(
  (ref) => DioClient(
    hiveService: ref.watch(hiveServiceProvider),
    alice: ref.read(aliceProvider),
    baseUrl: NetworkConfig.apiBaseUrl.value,
  ),
);

final faceAgingClientProvider = Provider<DioClient>(
  (ref) => DioClient(
    hiveService: ref.watch(hiveServiceProvider),
    alice: ref.read(aliceProvider),
    baseUrl: NetworkConfig.apiBaseUrl.value,
    connectTimeout: const Duration(seconds: 90),
    receiveTimeout: const Duration(seconds: 90),
  ),
);

final imageDioClientProvider = Provider<DioClient>(
  (ref) => DioClient(
    hiveService: ref.watch(hiveServiceProvider),
    alice: ref.read(aliceProvider),
    baseUrl: NetworkConfig.bucketBaseUrl.value,
    withTokenInterceptor: false,
  ),
);
