import 'package:dio/dio.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze_recommendation/skin_analyze_recommendation.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze_summary/skin_analyze_summary.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/api_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/dio_client.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';

part 'skin_analyze_api.g.dart';

@RestApi()
abstract class SkinAnalyzeApi {
  factory SkinAnalyzeApi(Dio dio) = _SkinAnalyzeApi;

  @GET(Endpoint.skinAnalyze)
  Future<ApiResponseWithPagination<SkinAnalyze>> getAllSkinAnalyze({
    @Query('name') String? name,
    @Query('page') int? page,
    @Query('page_size') int? pageSize,
    @Query('sort_column') String sortColumn = 'created_at',
    @Query('sort_order') String sortOrder = 'desc',
  });

  @GET('${Endpoint.skinAnalyze}/{${Endpoint.id}}')
  Future<ApiResponse<SkinAnalyze>> getDetail({
    @Path(Endpoint.id) required String id,
  });

  @GET('${Endpoint.summary}/{${Endpoint.id}}')
  Future<ApiResponse<SkinAnalyzeSummary>> getSummary({
    @Path(Endpoint.id) required String id,
  });

  @GET('${Endpoint.recommendation}/{${Endpoint.id}}')
  Future<ApiResponse<SkinAnalyzeRecommendation>> getRecommendation({
    @Path(Endpoint.id) required String id,
  });

  @GET('${Endpoint.topConcern}/{${Endpoint.id}}')
  Future<ApiResponse<List<String>>> getTopConcern({
    @Path(Endpoint.id) required String id,
  });
}

final skinAnalyzeApiProvider = Provider<SkinAnalyzeApi>(
  (ref) => SkinAnalyzeApi(ref.read(apiDioClientProvider).dio),
);
