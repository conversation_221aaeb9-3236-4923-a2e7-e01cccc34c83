// ignore_for_file: inference_failure_on_function_invocation

import 'package:dio/dio.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/api_response.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/treatment_product_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/dio_client.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';

part 'treatment_product_api.g.dart';

@RestApi()
// ignore: one_member_abstracts
abstract class TreatmentProductApi {
  factory TreatmentProductApi(Dio dio) = _TreatmentProductApi;

  @GET(Endpoint.treatmentProduct)
  Future<ApiResponseWithPagination<TreatmentProductResponse>>
      getTreatmentProducts({
    @Query('name') String? searchText,
    @Query('page') int page = 1,
    @Query('page_size') int pageSize = 10,
    @Query('types') List<String>? types,
  });
}

final treatmentProductApiProvider = Provider<TreatmentProductApi>(
  (ref) => TreatmentProductApi(ref.watch(apiDioClientProvider).dio),
);
