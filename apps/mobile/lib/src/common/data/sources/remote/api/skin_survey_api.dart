// ignore_for_file: inference_failure_on_function_invocation

import 'package:dio/dio.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/api_response.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/skin_survey_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/dio_client.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';

part 'skin_survey_api.g.dart';

@RestApi()
// ignore: one_member_abstracts
abstract class SkinSurveyApi {
  factory SkinSurveyApi(Dio dio) = _SkinSurveyApi;

  @GET(Endpoint.survey)
  Future<ApiResponseWithPagination<SkinSurveyResponse>> getSurveyQuestions(
    @Query('page') int? page,
    @Query('page_size') int? pageSize,
  );
}

final skinSurveyApiProvider = Provider<SkinSurveyApi>(
  (ref) => SkinSurveyApi(ref.watch(apiDioClientProvider).dio),
);
