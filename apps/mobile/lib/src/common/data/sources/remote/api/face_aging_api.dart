import 'package:dio/dio.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/api_response.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/face_aging_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/dio_client.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';

part 'face_aging_api.g.dart';

@RestApi()
abstract class FaceAgingApi {
  factory FaceAgingApi(Dio dio) = _FaceAgingApi;

  @POST('${Endpoint.faceAging}/{${Endpoint.id}}')
  Future<ApiResponse<JobFaceAgingResponse>> faceAgingConcern({
    @Path(Endpoint.id) required String id,
    @Body() required Map<String, dynamic> body,
  });

  @GET('${Endpoint.faceAgingStatus}/{${Endpoint.id}}')
  Future<ApiResponse<JobFaceAgingResponse>> faceAgingStatus({
    @Path(Endpoint.id) required String id,
  });
}

final faceAgingApiProvider = Provider<FaceAgingApi>(
  (ref) => FaceAgingApi(ref.watch(faceAgingClientProvider).dio),
);
