import 'package:alice_lightweight/alice.dart';
import 'package:dio/dio.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';

class AuthInterceptor extends InterceptorsWrapper {
  AuthInterceptor({
    required this.dio,
    required this.hiveService,
    required this.alice,
  });

  final Dio dio;
  final HiveService hiveService;
  final Alice alice;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = hiveService.userToken;
    final isLoginEndpoint = options.path.endsWith('/auth/login');
    final isRefreshEndpoint = options.path.endsWith('/auth/refresh');
    if (!(isLoginEndpoint || isRefreshEndpoint) && token != null) {
      options.headers.addAll({
        'Authorization': 'Bearer $token',
      });
    }
    super.onRequest(options, handler);
  }

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    final refreshToken = hiveService.userRefreshToken;

    final isAuthEndpoint = err.requestOptions.path.endsWith('/v1/auth/login') ||
        err.requestOptions.path.endsWith('/v1/auth/refresh');
    if (isAuthEndpoint) {
      super.onError(err, handler);
      return;
    }

    // Check if error is 401 (Unauthorized) and we have a refresh token
    if (err.response?.statusCode == 401 && refreshToken != null) {
      try {
        final refreshDio = Dio()
          ..options.baseUrl = dio.options.baseUrl
          ..options.connectTimeout = dio.options.connectTimeout
          ..options.receiveTimeout = dio.options.receiveTimeout
          ..interceptors.add(alice.getDioInterceptor());

        // Call refresh token endpoint
        // ignore: inference_failure_on_function_invocation
        final refreshResponse = await refreshDio.post(
          Endpoint.refreshToken,
          data: {
            'refresh_token': refreshToken,
          },
        );

        if (refreshResponse.statusCode == 200) {
          // Extract new tokens from response
          // ignore: avoid_dynamic_calls
          final newToken = refreshResponse.data['data']['token'] as String;

          final newRefreshToken =
              // ignore: avoid_dynamic_calls
              refreshResponse.data['data']['refresh_token'] as String;

          // Save new tokens
          await hiveService.saveUserToken(newToken);
          await hiveService.saveUserRefreshToken(newRefreshToken);

          // Update the original request with new token
          err.requestOptions.headers['Authorization'] = 'Bearer $newToken';

          // Retry the original request
          // ignore: inference_failure_on_function_invocation
          final retryResponse = await dio.fetch(err.requestOptions);

          // Resolve with the retry response
          handler.resolve(retryResponse);
          return;
        }
      } on Exception {
        // Refresh failed, handle logout
        await _handleAuthFailure();
        handler.next(err);
        return;
      }
    }

    super.onError(err, handler);
  }

  Future<void> _handleAuthFailure() async {
    hiveService
      ..deleteUserToken()
      ..deleteUserRefreshToken();
    final context = rootNavigatorKey.currentContext;
    context?.goNamed(Routes.login.name);
  }
}
