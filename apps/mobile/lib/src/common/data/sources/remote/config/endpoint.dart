class Endpoint {
  // General
  static const String id = 'id';

  // Auth
  static const String login = '/v1/auth/login';
  static const String refreshToken = '/v1/auth/refresh';

  // Skin Analyze
  static const String skinAnalyze = '/v1/skin-analyze';

  // Survey
  static const String survey = '/v1/survey?';

  // User Survey
  static const String userSurvey = '/v1/user-survey';

  // User
  static const String user = '/v1/user';

  // Face Aging
  static const String faceAging = '/v1/face-aging/concerns';
  static const String faceAgingStatus = '/v1/face-aging/status';

  // Treatment Product
  static const String treatmentProduct = '/v1/treatment-product';

  // Summary
  static const String summary = '/v1/summary';

  // Recommendation Treatment
  static const String recommendation = '/v1/recommendation';

  // Machine Sync Log
  static const String machineSyncLog = '/v1/machine-sync-log';

  // Parameter Skin Evaluation
  static const String parameterSkinEvaluation = '/v1/parameter-skin-evaluation';
  
  // Top concern
  static const String topConcern = '/v1/skin-analyze/top-concern';
}
