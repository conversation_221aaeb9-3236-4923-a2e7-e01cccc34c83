import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/machine_sync/machine_sync.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze_recommendation/skin_analyze_recommendation.dart';
import 'package:euromedica_aizer/src/common/domain/entities/user/user.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

class HiveService {
  final userBox = Hive.box<User>(HiveKey.userBox);
  final appInitializedBox = Hive.box<bool>(HiveKey.isInitializedBox);
  final encryptedBox = Hive.box<String?>(HiveKey.encryptedBox);
  final skinAnalyzeBox = Hive.box<List<SkinAnalyze>>(HiveKey.skinAnalyzeBox);
  final skinAnalyzeLastSyncBox = Hive.box<int>(HiveKey.skinAnalyzeLastSyncBox);
  final skinAnalyzeSummaryBox = Hive.box<String>(HiveKey.skinAnalyzeSummaryBox);
  final skinAnalyzeRecommendationBox =
      Hive.box<SkinAnalyzeRecommendation>(HiveKey.skinAnalyzeRecommendationBox);
  final skinAgingPredictionsBox =
      Hive.box<List<SkinConcern>>(HiveKey.skinAgingPredictionsBox);
  final machineSyncBox = Hive.box<MachineSync>(HiveKey.machineSyncBox);

  Future<void> close() async {
    await Hive.close();
  }

  set initialized(bool isInitialized) =>
      appInitializedBox.put(HiveKey.isInitialized, isInitialized);

  bool get initialized => appInitializedBox.get(HiveKey.isInitialized) ?? false;

  /// Set Current User
  Future<void> saveCurrentUser(User user) => userBox.put(HiveKey.user, user);

  /// Get current User
  User? get currentUser => userBox.get(HiveKey.user);

  /// Delete Current User
  void deleteCurrentUser() => userBox.delete(HiveKey.user);

  /// Set User Token
  Future<void> saveUserToken(String token) =>
      encryptedBox.put(HiveKey.userToken, token);

  /// Get Current User
  String? get userToken => encryptedBox.get(HiveKey.userToken);

  /// Delete Ccrrent User Token
  void deleteUserToken() => encryptedBox.delete(HiveKey.userToken);

  /// Set User Token
  Future<void> saveUserRefreshToken(String refreshToken) =>
      encryptedBox.put(HiveKey.userRefreshToken, refreshToken);

  /// Get Current User
  String? get userRefreshToken => encryptedBox.get(HiveKey.userRefreshToken);

  /// Delete Ccrrent User Token
  void deleteUserRefreshToken() =>
      encryptedBox.delete(HiveKey.userRefreshToken);

  /// Set Skin Analyze
  void saveSkinAnalyze(List<SkinAnalyze> skinAnalyze) {
    skinAnalyzeBox.put(HiveKey.skinAnalyzeBox, skinAnalyze);
    skinAnalyzeLastSyncBox.put(
      HiveKey.skinAnalyzeLastSyncBox,
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  /// Get Skin Analyze
  List<SkinAnalyze>? get skinAnalyze =>
      skinAnalyzeBox.get(HiveKey.skinAnalyzeBox);

  /// Get Skin Analyze Last Sync
  int? get skinAnalyzeLastSync =>
      skinAnalyzeLastSyncBox.get(HiveKey.skinAnalyzeLastSyncBox);

  /// Delete Skin Analyze
  Future<void> deleteSkinAnalyze() async {
    await skinAnalyzeBox.delete(HiveKey.skinAnalyzeBox);
    await skinAnalyzeLastSyncBox.delete(HiveKey.skinAnalyzeLastSyncBox);
  }

  // get summary
  String? get skinAnalyzeSummary =>
      skinAnalyzeSummaryBox.get(HiveKey.skinAnalyzeSummaryBox);

  // set summary
  void saveSkinAnalyzeSummary(String summary) =>
      skinAnalyzeSummaryBox.put(HiveKey.skinAnalyzeSummaryBox, summary);

  // delete summary
  Future<void> deleteSkinAnalyzeSummary() async =>
      skinAnalyzeSummaryBox.delete(HiveKey.skinAnalyzeSummaryBox);

  // get recommendation
  SkinAnalyzeRecommendation? get skinAnalyzeRecommendation =>
      skinAnalyzeRecommendationBox.get(HiveKey.skinAnalyzeRecommendationBox);

  // set recommendation
  Future<void> saveSkinAnalyzeRecommendation(
    SkinAnalyzeRecommendation recommendation,
  ) async {
    await skinAnalyzeRecommendationBox.put(
      HiveKey.skinAnalyzeRecommendationBox,
      recommendation,
    );
  }

  // delete recommendation
  Future<void> deleteSkinAnalyzeRecommendation() =>
      skinAnalyzeRecommendationBox.delete(HiveKey.skinAnalyzeRecommendationBox);

  /// Save Face Agings
  Future<void> saveFaceAgings(List<SkinConcern> faceAgings) =>
      skinAgingPredictionsBox.put(HiveKey.skinAgingPredictionsBox, faceAgings);

  /// Get Face Agings
  List<SkinConcern>? get faceAgings =>
      skinAgingPredictionsBox.get(HiveKey.skinAgingPredictionsBox);

  /// Delete Face Agings
  Future<void> deleteFaceAgings() =>
      skinAgingPredictionsBox.delete(HiveKey.skinAgingPredictionsBox);

  Future<void> saveMachineSync(MachineSync machineSync) => machineSyncBox.put(
        HiveKey.machineSyncBox,
        machineSync,
      );

  MachineSync? get machineSync => machineSyncBox.get(HiveKey.machineSyncBox);

  Future<void> deleteMachineSync() =>
      machineSyncBox.delete(HiveKey.machineSyncBox);

  Future<void> clearCacheData() async {
    await deleteSkinAnalyze();
    await deleteSkinAnalyzeSummary();
    await deleteSkinAnalyzeRecommendation();
    await deleteFaceAgings();
    await deleteMachineSync();
  }
}

final hiveServiceProvider = Provider<HiveService>((ref) => HiveService());
