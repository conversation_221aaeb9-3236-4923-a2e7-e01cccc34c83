import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_survey_results.freezed.dart';
part 'user_survey_results.g.dart';

@freezed
class UserSurveyResult with _$UserSurveyResult {
  const factory UserSurveyResult({
    required String id,
    required String question,
    required List<String> answers,
    required String category,
  }) = _UserSurveyResult;

  factory UserSurveyResult.fromJson(Map<String, dynamic> json) =>
      _$UserSurveyResultFromJson(json);
}
