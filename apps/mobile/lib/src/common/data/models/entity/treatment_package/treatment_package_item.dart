// ignore_for_file: public_member_api_docs, sort_constructors_first
class TreatmentPackageItem {
  const TreatmentPackageItem({
    required this.id,
    this.title = '',
    this.price = 0,
    this.description = '',
    this.isSelected = false,
    this.mediaUrl,
    this.thumbnailUrl,
    this.categories = const [],
    this.concern = const [],
    this.score = 0,
    this.quantity = 0,
    this.isChecked = false,
  });
  final String id;
  final String title;
  final String description;
  final bool isSelected;
  final int price;
  final String? mediaUrl;
  final String? thumbnailUrl;
  final List<String> categories;
  final List<String> concern;
  final double score;
  final int quantity;
  final bool isChecked;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TreatmentPackageItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  TreatmentPackageItem copyWith({
    String? id,
    String? title,
    String? description,
    bool? isSelected,
    int? price,
    String? videoUrl,
    String? thumbnail,
    List<String>? categories,
    List<String>? concern,
    double? score,
    int? quantity,
    bool? isChecked,
  }) {
    return TreatmentPackageItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      isSelected: isSelected ?? this.isSelected,
      price: price ?? this.price,
      mediaUrl: mediaUrl,
      thumbnailUrl: thumbnailUrl,
      categories: categories ?? this.categories,
      concern: concern ?? this.concern,
      score: score ?? this.score,
      quantity: quantity ?? this.quantity,
      isChecked: isChecked ?? this.isChecked,
    );
  }
}
