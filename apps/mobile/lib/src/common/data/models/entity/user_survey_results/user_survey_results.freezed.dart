// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_survey_results.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserSurveyResult _$UserSurveyResultFromJson(Map<String, dynamic> json) {
  return _UserSurveyResult.fromJson(json);
}

/// @nodoc
mixin _$UserSurveyResult {
  String get id => throw _privateConstructorUsedError;
  String get question => throw _privateConstructorUsedError;
  List<String> get answers => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;

  /// Serializes this UserSurveyResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserSurveyResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserSurveyResultCopyWith<UserSurveyResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserSurveyResultCopyWith<$Res> {
  factory $UserSurveyResultCopyWith(
          UserSurveyResult value, $Res Function(UserSurveyResult) then) =
      _$UserSurveyResultCopyWithImpl<$Res, UserSurveyResult>;
  @useResult
  $Res call(
      {String id, String question, List<String> answers, String category});
}

/// @nodoc
class _$UserSurveyResultCopyWithImpl<$Res, $Val extends UserSurveyResult>
    implements $UserSurveyResultCopyWith<$Res> {
  _$UserSurveyResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserSurveyResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? question = null,
    Object? answers = null,
    Object? category = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _value.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<String>,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserSurveyResultImplCopyWith<$Res>
    implements $UserSurveyResultCopyWith<$Res> {
  factory _$$UserSurveyResultImplCopyWith(_$UserSurveyResultImpl value,
          $Res Function(_$UserSurveyResultImpl) then) =
      __$$UserSurveyResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String question, List<String> answers, String category});
}

/// @nodoc
class __$$UserSurveyResultImplCopyWithImpl<$Res>
    extends _$UserSurveyResultCopyWithImpl<$Res, _$UserSurveyResultImpl>
    implements _$$UserSurveyResultImplCopyWith<$Res> {
  __$$UserSurveyResultImplCopyWithImpl(_$UserSurveyResultImpl _value,
      $Res Function(_$UserSurveyResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserSurveyResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? question = null,
    Object? answers = null,
    Object? category = null,
  }) {
    return _then(_$UserSurveyResultImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _value._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<String>,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserSurveyResultImpl implements _UserSurveyResult {
  const _$UserSurveyResultImpl(
      {required this.id,
      required this.question,
      required final List<String> answers,
      required this.category})
      : _answers = answers;

  factory _$UserSurveyResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserSurveyResultImplFromJson(json);

  @override
  final String id;
  @override
  final String question;
  final List<String> _answers;
  @override
  List<String> get answers {
    if (_answers is EqualUnmodifiableListView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answers);
  }

  @override
  final String category;

  @override
  String toString() {
    return 'UserSurveyResult(id: $id, question: $question, answers: $answers, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserSurveyResultImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.question, question) ||
                other.question == question) &&
            const DeepCollectionEquality().equals(other._answers, _answers) &&
            (identical(other.category, category) ||
                other.category == category));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, question,
      const DeepCollectionEquality().hash(_answers), category);

  /// Create a copy of UserSurveyResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserSurveyResultImplCopyWith<_$UserSurveyResultImpl> get copyWith =>
      __$$UserSurveyResultImplCopyWithImpl<_$UserSurveyResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserSurveyResultImplToJson(
      this,
    );
  }
}

abstract class _UserSurveyResult implements UserSurveyResult {
  const factory _UserSurveyResult(
      {required final String id,
      required final String question,
      required final List<String> answers,
      required final String category}) = _$UserSurveyResultImpl;

  factory _UserSurveyResult.fromJson(Map<String, dynamic> json) =
      _$UserSurveyResultImpl.fromJson;

  @override
  String get id;
  @override
  String get question;
  @override
  List<String> get answers;
  @override
  String get category;

  /// Create a copy of UserSurveyResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserSurveyResultImplCopyWith<_$UserSurveyResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
