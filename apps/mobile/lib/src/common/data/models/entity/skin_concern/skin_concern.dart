import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'skin_concern.freezed.dart';
part 'skin_concern.g.dart';

@freezed
@HiveType(typeId: 15)
class SkinConcern with _$SkinConcern {
  const factory SkinConcern({
    @HiveField(0) String? name,
    @HiveField(1) String? faceAgingImageBase64,
    @HiveField(2) String? faceBeautifyingImageBase64,
    @HiveField(3) String? selectedAreaImageBase64,
    @HiveField(4) String? key,
    @HiveField(5) List<FaceArea>? faceAreas,
    @HiveField(6) String? topConcernKey,
  }) = _SkinConcern;

  factory SkinConcern.fromJson(Map<String, dynamic> json) =>
      _$SkinConcernFromJson(json);

  static List<SkinConcern> get skinConcerns => [
        const SkinConcern(
          name: 'Wrinkle',
          key: 'wrinkles',
          topConcernKey: 'Wrinkle',
        ),
        const SkinConcern(name: 'Acne', key: 'acne', topConcernKey: 'Acne'),
        const SkinConcern(name: 'Pores', key: 'pores', topConcernKey: 'Pore'),
        const SkinConcern(name: 'Scar', key: 'scar', topConcernKey: 'Scar'),
        const SkinConcern(
          name: 'Redness',
          key: 'redness',
          topConcernKey: 'Sensitive',
        ),
        const SkinConcern(
          name: 'Pigmentation',
          key: 'pigment',
          topConcernKey: 'Hyperpigmentation',
        ),
      ];
}
