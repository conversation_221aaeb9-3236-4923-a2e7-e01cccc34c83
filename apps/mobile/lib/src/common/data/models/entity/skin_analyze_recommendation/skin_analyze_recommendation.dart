// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'skin_analyze_recommendation.freezed.dart';
part 'skin_analyze_recommendation.g.dart';

@freezed
@HiveType(typeId: 5)
class SkinAnalyzeRecommendation with _$SkinAnalyzeRecommendation {
  @JsonSerializable(fieldRename: FieldRename.snake)
  factory SkinAnalyzeRecommendation({
    @HiveField(0) String? summary,
    @HiveField(1) List<Treatment>? treatments,
  }) = _SkinAnalyzeRecommendation;

  factory SkinAnalyzeRecommendation.fromJson(Map<String, dynamic> json) =>
      _$SkinAnalyzeRecommendationFromJson(json);
}

@freezed
@HiveType(typeId: 6)
class Treatment with _$Treatment {
  @JsonSerializable(fieldRename: FieldRename.snake)
  factory Treatment({
    @HiveField(0) String? id,
    @HiveField(1) String? itemCode,
    @HiveField(2) String? name,
    @HiveField(3) String? description,
    @HiveField(4) List<String>? categories,
    @HiveField(5) int? quantity,
    @HiveField(6) int? price,
    @HiveField(7) bool? isTopRecommendation,
    @HiveField(8) List<String>? solvedConcerns,
    @HiveField(9) double? totalScore,
    @HiveField(10) String? mediaUrl,
    @HiveField(11) String? thumbnailUrl,
    @HiveField(12) bool? isChecked,
  }) = _Treatment;

  factory Treatment.fromJson(Map<String, dynamic> json) =>
      _$TreatmentFromJson(json);
}
