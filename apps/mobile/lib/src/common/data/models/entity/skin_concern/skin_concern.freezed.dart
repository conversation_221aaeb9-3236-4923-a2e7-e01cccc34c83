// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'skin_concern.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SkinConcern _$SkinConcernFromJson(Map<String, dynamic> json) {
  return _SkinConcern.fromJson(json);
}

/// @nodoc
mixin _$SkinConcern {
  @HiveField(0)
  String? get name => throw _privateConstructorUsedError;
  @HiveField(1)
  String? get faceAgingImageBase64 => throw _privateConstructorUsedError;
  @HiveField(2)
  String? get faceBeautifyingImageBase64 => throw _privateConstructorUsedError;
  @HiveField(3)
  String? get selectedAreaImageBase64 => throw _privateConstructorUsedError;
  @HiveField(4)
  String? get key => throw _privateConstructorUsedError;
  @HiveField(5)
  List<FaceArea>? get faceAreas => throw _privateConstructorUsedError;
  @HiveField(6)
  String? get topConcernKey => throw _privateConstructorUsedError;

  /// Serializes this SkinConcern to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SkinConcern
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SkinConcernCopyWith<SkinConcern> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkinConcernCopyWith<$Res> {
  factory $SkinConcernCopyWith(
          SkinConcern value, $Res Function(SkinConcern) then) =
      _$SkinConcernCopyWithImpl<$Res, SkinConcern>;
  @useResult
  $Res call(
      {@HiveField(0) String? name,
      @HiveField(1) String? faceAgingImageBase64,
      @HiveField(2) String? faceBeautifyingImageBase64,
      @HiveField(3) String? selectedAreaImageBase64,
      @HiveField(4) String? key,
      @HiveField(5) List<FaceArea>? faceAreas,
      @HiveField(6) String? topConcernKey});
}

/// @nodoc
class _$SkinConcernCopyWithImpl<$Res, $Val extends SkinConcern>
    implements $SkinConcernCopyWith<$Res> {
  _$SkinConcernCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SkinConcern
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? faceAgingImageBase64 = freezed,
    Object? faceBeautifyingImageBase64 = freezed,
    Object? selectedAreaImageBase64 = freezed,
    Object? key = freezed,
    Object? faceAreas = freezed,
    Object? topConcernKey = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      faceAgingImageBase64: freezed == faceAgingImageBase64
          ? _value.faceAgingImageBase64
          : faceAgingImageBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
      faceBeautifyingImageBase64: freezed == faceBeautifyingImageBase64
          ? _value.faceBeautifyingImageBase64
          : faceBeautifyingImageBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedAreaImageBase64: freezed == selectedAreaImageBase64
          ? _value.selectedAreaImageBase64
          : selectedAreaImageBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      faceAreas: freezed == faceAreas
          ? _value.faceAreas
          : faceAreas // ignore: cast_nullable_to_non_nullable
              as List<FaceArea>?,
      topConcernKey: freezed == topConcernKey
          ? _value.topConcernKey
          : topConcernKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SkinConcernImplCopyWith<$Res>
    implements $SkinConcernCopyWith<$Res> {
  factory _$$SkinConcernImplCopyWith(
          _$SkinConcernImpl value, $Res Function(_$SkinConcernImpl) then) =
      __$$SkinConcernImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String? name,
      @HiveField(1) String? faceAgingImageBase64,
      @HiveField(2) String? faceBeautifyingImageBase64,
      @HiveField(3) String? selectedAreaImageBase64,
      @HiveField(4) String? key,
      @HiveField(5) List<FaceArea>? faceAreas,
      @HiveField(6) String? topConcernKey});
}

/// @nodoc
class __$$SkinConcernImplCopyWithImpl<$Res>
    extends _$SkinConcernCopyWithImpl<$Res, _$SkinConcernImpl>
    implements _$$SkinConcernImplCopyWith<$Res> {
  __$$SkinConcernImplCopyWithImpl(
      _$SkinConcernImpl _value, $Res Function(_$SkinConcernImpl) _then)
      : super(_value, _then);

  /// Create a copy of SkinConcern
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? faceAgingImageBase64 = freezed,
    Object? faceBeautifyingImageBase64 = freezed,
    Object? selectedAreaImageBase64 = freezed,
    Object? key = freezed,
    Object? faceAreas = freezed,
    Object? topConcernKey = freezed,
  }) {
    return _then(_$SkinConcernImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      faceAgingImageBase64: freezed == faceAgingImageBase64
          ? _value.faceAgingImageBase64
          : faceAgingImageBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
      faceBeautifyingImageBase64: freezed == faceBeautifyingImageBase64
          ? _value.faceBeautifyingImageBase64
          : faceBeautifyingImageBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedAreaImageBase64: freezed == selectedAreaImageBase64
          ? _value.selectedAreaImageBase64
          : selectedAreaImageBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      faceAreas: freezed == faceAreas
          ? _value._faceAreas
          : faceAreas // ignore: cast_nullable_to_non_nullable
              as List<FaceArea>?,
      topConcernKey: freezed == topConcernKey
          ? _value.topConcernKey
          : topConcernKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SkinConcernImpl implements _SkinConcern {
  const _$SkinConcernImpl(
      {@HiveField(0) this.name,
      @HiveField(1) this.faceAgingImageBase64,
      @HiveField(2) this.faceBeautifyingImageBase64,
      @HiveField(3) this.selectedAreaImageBase64,
      @HiveField(4) this.key,
      @HiveField(5) final List<FaceArea>? faceAreas,
      @HiveField(6) this.topConcernKey})
      : _faceAreas = faceAreas;

  factory _$SkinConcernImpl.fromJson(Map<String, dynamic> json) =>
      _$$SkinConcernImplFromJson(json);

  @override
  @HiveField(0)
  final String? name;
  @override
  @HiveField(1)
  final String? faceAgingImageBase64;
  @override
  @HiveField(2)
  final String? faceBeautifyingImageBase64;
  @override
  @HiveField(3)
  final String? selectedAreaImageBase64;
  @override
  @HiveField(4)
  final String? key;
  final List<FaceArea>? _faceAreas;
  @override
  @HiveField(5)
  List<FaceArea>? get faceAreas {
    final value = _faceAreas;
    if (value == null) return null;
    if (_faceAreas is EqualUnmodifiableListView) return _faceAreas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @HiveField(6)
  final String? topConcernKey;

  @override
  String toString() {
    return 'SkinConcern(name: $name, faceAgingImageBase64: $faceAgingImageBase64, faceBeautifyingImageBase64: $faceBeautifyingImageBase64, selectedAreaImageBase64: $selectedAreaImageBase64, key: $key, faceAreas: $faceAreas, topConcernKey: $topConcernKey)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SkinConcernImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.faceAgingImageBase64, faceAgingImageBase64) ||
                other.faceAgingImageBase64 == faceAgingImageBase64) &&
            (identical(other.faceBeautifyingImageBase64,
                    faceBeautifyingImageBase64) ||
                other.faceBeautifyingImageBase64 ==
                    faceBeautifyingImageBase64) &&
            (identical(
                    other.selectedAreaImageBase64, selectedAreaImageBase64) ||
                other.selectedAreaImageBase64 == selectedAreaImageBase64) &&
            (identical(other.key, key) || other.key == key) &&
            const DeepCollectionEquality()
                .equals(other._faceAreas, _faceAreas) &&
            (identical(other.topConcernKey, topConcernKey) ||
                other.topConcernKey == topConcernKey));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      faceAgingImageBase64,
      faceBeautifyingImageBase64,
      selectedAreaImageBase64,
      key,
      const DeepCollectionEquality().hash(_faceAreas),
      topConcernKey);

  /// Create a copy of SkinConcern
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SkinConcernImplCopyWith<_$SkinConcernImpl> get copyWith =>
      __$$SkinConcernImplCopyWithImpl<_$SkinConcernImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SkinConcernImplToJson(
      this,
    );
  }
}

abstract class _SkinConcern implements SkinConcern {
  const factory _SkinConcern(
      {@HiveField(0) final String? name,
      @HiveField(1) final String? faceAgingImageBase64,
      @HiveField(2) final String? faceBeautifyingImageBase64,
      @HiveField(3) final String? selectedAreaImageBase64,
      @HiveField(4) final String? key,
      @HiveField(5) final List<FaceArea>? faceAreas,
      @HiveField(6) final String? topConcernKey}) = _$SkinConcernImpl;

  factory _SkinConcern.fromJson(Map<String, dynamic> json) =
      _$SkinConcernImpl.fromJson;

  @override
  @HiveField(0)
  String? get name;
  @override
  @HiveField(1)
  String? get faceAgingImageBase64;
  @override
  @HiveField(2)
  String? get faceBeautifyingImageBase64;
  @override
  @HiveField(3)
  String? get selectedAreaImageBase64;
  @override
  @HiveField(4)
  String? get key;
  @override
  @HiveField(5)
  List<FaceArea>? get faceAreas;
  @override
  @HiveField(6)
  String? get topConcernKey;

  /// Create a copy of SkinConcern
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SkinConcernImplCopyWith<_$SkinConcernImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
