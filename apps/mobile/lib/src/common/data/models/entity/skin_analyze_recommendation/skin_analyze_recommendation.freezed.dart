// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'skin_analyze_recommendation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SkinAnalyzeRecommendation _$SkinAnalyzeRecommendationFromJson(
    Map<String, dynamic> json) {
  return _SkinAnalyzeRecommendation.fromJson(json);
}

/// @nodoc
mixin _$SkinAnalyzeRecommendation {
  @HiveField(0)
  String? get summary => throw _privateConstructorUsedError;
  @HiveField(1)
  List<Treatment>? get treatments => throw _privateConstructorUsedError;

  /// Serializes this SkinAnalyzeRecommendation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SkinAnalyzeRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SkinAnalyzeRecommendationCopyWith<SkinAnalyzeRecommendation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkinAnalyzeRecommendationCopyWith<$Res> {
  factory $SkinAnalyzeRecommendationCopyWith(SkinAnalyzeRecommendation value,
          $Res Function(SkinAnalyzeRecommendation) then) =
      _$SkinAnalyzeRecommendationCopyWithImpl<$Res, SkinAnalyzeRecommendation>;
  @useResult
  $Res call(
      {@HiveField(0) String? summary,
      @HiveField(1) List<Treatment>? treatments});
}

/// @nodoc
class _$SkinAnalyzeRecommendationCopyWithImpl<$Res,
        $Val extends SkinAnalyzeRecommendation>
    implements $SkinAnalyzeRecommendationCopyWith<$Res> {
  _$SkinAnalyzeRecommendationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SkinAnalyzeRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? summary = freezed,
    Object? treatments = freezed,
  }) {
    return _then(_value.copyWith(
      summary: freezed == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as String?,
      treatments: freezed == treatments
          ? _value.treatments
          : treatments // ignore: cast_nullable_to_non_nullable
              as List<Treatment>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SkinAnalyzeRecommendationImplCopyWith<$Res>
    implements $SkinAnalyzeRecommendationCopyWith<$Res> {
  factory _$$SkinAnalyzeRecommendationImplCopyWith(
          _$SkinAnalyzeRecommendationImpl value,
          $Res Function(_$SkinAnalyzeRecommendationImpl) then) =
      __$$SkinAnalyzeRecommendationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String? summary,
      @HiveField(1) List<Treatment>? treatments});
}

/// @nodoc
class __$$SkinAnalyzeRecommendationImplCopyWithImpl<$Res>
    extends _$SkinAnalyzeRecommendationCopyWithImpl<$Res,
        _$SkinAnalyzeRecommendationImpl>
    implements _$$SkinAnalyzeRecommendationImplCopyWith<$Res> {
  __$$SkinAnalyzeRecommendationImplCopyWithImpl(
      _$SkinAnalyzeRecommendationImpl _value,
      $Res Function(_$SkinAnalyzeRecommendationImpl) _then)
      : super(_value, _then);

  /// Create a copy of SkinAnalyzeRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? summary = freezed,
    Object? treatments = freezed,
  }) {
    return _then(_$SkinAnalyzeRecommendationImpl(
      summary: freezed == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as String?,
      treatments: freezed == treatments
          ? _value._treatments
          : treatments // ignore: cast_nullable_to_non_nullable
              as List<Treatment>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$SkinAnalyzeRecommendationImpl implements _SkinAnalyzeRecommendation {
  _$SkinAnalyzeRecommendationImpl(
      {@HiveField(0) this.summary,
      @HiveField(1) final List<Treatment>? treatments})
      : _treatments = treatments;

  factory _$SkinAnalyzeRecommendationImpl.fromJson(Map<String, dynamic> json) =>
      _$$SkinAnalyzeRecommendationImplFromJson(json);

  @override
  @HiveField(0)
  final String? summary;
  final List<Treatment>? _treatments;
  @override
  @HiveField(1)
  List<Treatment>? get treatments {
    final value = _treatments;
    if (value == null) return null;
    if (_treatments is EqualUnmodifiableListView) return _treatments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SkinAnalyzeRecommendation(summary: $summary, treatments: $treatments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SkinAnalyzeRecommendationImpl &&
            (identical(other.summary, summary) || other.summary == summary) &&
            const DeepCollectionEquality()
                .equals(other._treatments, _treatments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, summary, const DeepCollectionEquality().hash(_treatments));

  /// Create a copy of SkinAnalyzeRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SkinAnalyzeRecommendationImplCopyWith<_$SkinAnalyzeRecommendationImpl>
      get copyWith => __$$SkinAnalyzeRecommendationImplCopyWithImpl<
          _$SkinAnalyzeRecommendationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SkinAnalyzeRecommendationImplToJson(
      this,
    );
  }
}

abstract class _SkinAnalyzeRecommendation implements SkinAnalyzeRecommendation {
  factory _SkinAnalyzeRecommendation(
          {@HiveField(0) final String? summary,
          @HiveField(1) final List<Treatment>? treatments}) =
      _$SkinAnalyzeRecommendationImpl;

  factory _SkinAnalyzeRecommendation.fromJson(Map<String, dynamic> json) =
      _$SkinAnalyzeRecommendationImpl.fromJson;

  @override
  @HiveField(0)
  String? get summary;
  @override
  @HiveField(1)
  List<Treatment>? get treatments;

  /// Create a copy of SkinAnalyzeRecommendation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SkinAnalyzeRecommendationImplCopyWith<_$SkinAnalyzeRecommendationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

Treatment _$TreatmentFromJson(Map<String, dynamic> json) {
  return _Treatment.fromJson(json);
}

/// @nodoc
mixin _$Treatment {
  @HiveField(0)
  String? get id => throw _privateConstructorUsedError;
  @HiveField(1)
  String? get itemCode => throw _privateConstructorUsedError;
  @HiveField(2)
  String? get name => throw _privateConstructorUsedError;
  @HiveField(3)
  String? get description => throw _privateConstructorUsedError;
  @HiveField(4)
  List<String>? get categories => throw _privateConstructorUsedError;
  @HiveField(5)
  int? get quantity => throw _privateConstructorUsedError;
  @HiveField(6)
  int? get price => throw _privateConstructorUsedError;
  @HiveField(7)
  bool? get isTopRecommendation => throw _privateConstructorUsedError;
  @HiveField(8)
  List<String>? get solvedConcerns => throw _privateConstructorUsedError;
  @HiveField(9)
  double? get totalScore => throw _privateConstructorUsedError;
  @HiveField(10)
  String? get mediaUrl => throw _privateConstructorUsedError;
  @HiveField(11)
  String? get thumbnailUrl => throw _privateConstructorUsedError;
  @HiveField(12)
  bool? get isChecked => throw _privateConstructorUsedError;

  /// Serializes this Treatment to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Treatment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentCopyWith<Treatment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentCopyWith<$Res> {
  factory $TreatmentCopyWith(Treatment value, $Res Function(Treatment) then) =
      _$TreatmentCopyWithImpl<$Res, Treatment>;
  @useResult
  $Res call(
      {@HiveField(0) String? id,
      @HiveField(1) String? itemCode,
      @HiveField(2) String? name,
      @HiveField(3) String? description,
      @HiveField(4) List<String>? categories,
      @HiveField(5) int? quantity,
      @HiveField(6) int? price,
      @HiveField(7) bool? isTopRecommendation,
      @HiveField(8) List<String>? solvedConcerns,
      @HiveField(9) double? totalScore,
      @HiveField(10) String? mediaUrl,
      @HiveField(11) String? thumbnailUrl,
      @HiveField(12) bool? isChecked});
}

/// @nodoc
class _$TreatmentCopyWithImpl<$Res, $Val extends Treatment>
    implements $TreatmentCopyWith<$Res> {
  _$TreatmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Treatment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? itemCode = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? categories = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? isTopRecommendation = freezed,
    Object? solvedConcerns = freezed,
    Object? totalScore = freezed,
    Object? mediaUrl = freezed,
    Object? thumbnailUrl = freezed,
    Object? isChecked = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      itemCode: freezed == itemCode
          ? _value.itemCode
          : itemCode // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      categories: freezed == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      isTopRecommendation: freezed == isTopRecommendation
          ? _value.isTopRecommendation
          : isTopRecommendation // ignore: cast_nullable_to_non_nullable
              as bool?,
      solvedConcerns: freezed == solvedConcerns
          ? _value.solvedConcerns
          : solvedConcerns // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      totalScore: freezed == totalScore
          ? _value.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as double?,
      mediaUrl: freezed == mediaUrl
          ? _value.mediaUrl
          : mediaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailUrl: freezed == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isChecked: freezed == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentImplCopyWith<$Res>
    implements $TreatmentCopyWith<$Res> {
  factory _$$TreatmentImplCopyWith(
          _$TreatmentImpl value, $Res Function(_$TreatmentImpl) then) =
      __$$TreatmentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String? id,
      @HiveField(1) String? itemCode,
      @HiveField(2) String? name,
      @HiveField(3) String? description,
      @HiveField(4) List<String>? categories,
      @HiveField(5) int? quantity,
      @HiveField(6) int? price,
      @HiveField(7) bool? isTopRecommendation,
      @HiveField(8) List<String>? solvedConcerns,
      @HiveField(9) double? totalScore,
      @HiveField(10) String? mediaUrl,
      @HiveField(11) String? thumbnailUrl,
      @HiveField(12) bool? isChecked});
}

/// @nodoc
class __$$TreatmentImplCopyWithImpl<$Res>
    extends _$TreatmentCopyWithImpl<$Res, _$TreatmentImpl>
    implements _$$TreatmentImplCopyWith<$Res> {
  __$$TreatmentImplCopyWithImpl(
      _$TreatmentImpl _value, $Res Function(_$TreatmentImpl) _then)
      : super(_value, _then);

  /// Create a copy of Treatment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? itemCode = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? categories = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? isTopRecommendation = freezed,
    Object? solvedConcerns = freezed,
    Object? totalScore = freezed,
    Object? mediaUrl = freezed,
    Object? thumbnailUrl = freezed,
    Object? isChecked = freezed,
  }) {
    return _then(_$TreatmentImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      itemCode: freezed == itemCode
          ? _value.itemCode
          : itemCode // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      categories: freezed == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      isTopRecommendation: freezed == isTopRecommendation
          ? _value.isTopRecommendation
          : isTopRecommendation // ignore: cast_nullable_to_non_nullable
              as bool?,
      solvedConcerns: freezed == solvedConcerns
          ? _value._solvedConcerns
          : solvedConcerns // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      totalScore: freezed == totalScore
          ? _value.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as double?,
      mediaUrl: freezed == mediaUrl
          ? _value.mediaUrl
          : mediaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailUrl: freezed == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isChecked: freezed == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentImpl implements _Treatment {
  _$TreatmentImpl(
      {@HiveField(0) this.id,
      @HiveField(1) this.itemCode,
      @HiveField(2) this.name,
      @HiveField(3) this.description,
      @HiveField(4) final List<String>? categories,
      @HiveField(5) this.quantity,
      @HiveField(6) this.price,
      @HiveField(7) this.isTopRecommendation,
      @HiveField(8) final List<String>? solvedConcerns,
      @HiveField(9) this.totalScore,
      @HiveField(10) this.mediaUrl,
      @HiveField(11) this.thumbnailUrl,
      @HiveField(12) this.isChecked})
      : _categories = categories,
        _solvedConcerns = solvedConcerns;

  factory _$TreatmentImpl.fromJson(Map<String, dynamic> json) =>
      _$$TreatmentImplFromJson(json);

  @override
  @HiveField(0)
  final String? id;
  @override
  @HiveField(1)
  final String? itemCode;
  @override
  @HiveField(2)
  final String? name;
  @override
  @HiveField(3)
  final String? description;
  final List<String>? _categories;
  @override
  @HiveField(4)
  List<String>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @HiveField(5)
  final int? quantity;
  @override
  @HiveField(6)
  final int? price;
  @override
  @HiveField(7)
  final bool? isTopRecommendation;
  final List<String>? _solvedConcerns;
  @override
  @HiveField(8)
  List<String>? get solvedConcerns {
    final value = _solvedConcerns;
    if (value == null) return null;
    if (_solvedConcerns is EqualUnmodifiableListView) return _solvedConcerns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @HiveField(9)
  final double? totalScore;
  @override
  @HiveField(10)
  final String? mediaUrl;
  @override
  @HiveField(11)
  final String? thumbnailUrl;
  @override
  @HiveField(12)
  final bool? isChecked;

  @override
  String toString() {
    return 'Treatment(id: $id, itemCode: $itemCode, name: $name, description: $description, categories: $categories, quantity: $quantity, price: $price, isTopRecommendation: $isTopRecommendation, solvedConcerns: $solvedConcerns, totalScore: $totalScore, mediaUrl: $mediaUrl, thumbnailUrl: $thumbnailUrl, isChecked: $isChecked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.itemCode, itemCode) ||
                other.itemCode == itemCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.isTopRecommendation, isTopRecommendation) ||
                other.isTopRecommendation == isTopRecommendation) &&
            const DeepCollectionEquality()
                .equals(other._solvedConcerns, _solvedConcerns) &&
            (identical(other.totalScore, totalScore) ||
                other.totalScore == totalScore) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      itemCode,
      name,
      description,
      const DeepCollectionEquality().hash(_categories),
      quantity,
      price,
      isTopRecommendation,
      const DeepCollectionEquality().hash(_solvedConcerns),
      totalScore,
      mediaUrl,
      thumbnailUrl,
      isChecked);

  /// Create a copy of Treatment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentImplCopyWith<_$TreatmentImpl> get copyWith =>
      __$$TreatmentImplCopyWithImpl<_$TreatmentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentImplToJson(
      this,
    );
  }
}

abstract class _Treatment implements Treatment {
  factory _Treatment(
      {@HiveField(0) final String? id,
      @HiveField(1) final String? itemCode,
      @HiveField(2) final String? name,
      @HiveField(3) final String? description,
      @HiveField(4) final List<String>? categories,
      @HiveField(5) final int? quantity,
      @HiveField(6) final int? price,
      @HiveField(7) final bool? isTopRecommendation,
      @HiveField(8) final List<String>? solvedConcerns,
      @HiveField(9) final double? totalScore,
      @HiveField(10) final String? mediaUrl,
      @HiveField(11) final String? thumbnailUrl,
      @HiveField(12) final bool? isChecked}) = _$TreatmentImpl;

  factory _Treatment.fromJson(Map<String, dynamic> json) =
      _$TreatmentImpl.fromJson;

  @override
  @HiveField(0)
  String? get id;
  @override
  @HiveField(1)
  String? get itemCode;
  @override
  @HiveField(2)
  String? get name;
  @override
  @HiveField(3)
  String? get description;
  @override
  @HiveField(4)
  List<String>? get categories;
  @override
  @HiveField(5)
  int? get quantity;
  @override
  @HiveField(6)
  int? get price;
  @override
  @HiveField(7)
  bool? get isTopRecommendation;
  @override
  @HiveField(8)
  List<String>? get solvedConcerns;
  @override
  @HiveField(9)
  double? get totalScore;
  @override
  @HiveField(10)
  String? get mediaUrl;
  @override
  @HiveField(11)
  String? get thumbnailUrl;
  @override
  @HiveField(12)
  bool? get isChecked;

  /// Create a copy of Treatment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentImplCopyWith<_$TreatmentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
