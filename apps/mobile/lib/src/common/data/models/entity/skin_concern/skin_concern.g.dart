// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'skin_concern.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SkinConcernAdapter extends TypeAdapter<SkinConcern> {
  @override
  final int typeId = 15;

  @override
  SkinConcern read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SkinConcern(
      name: fields[0] as String?,
      faceAgingImageBase64: fields[1] as String?,
      faceBeautifyingImageBase64: fields[2] as String?,
      selectedAreaImageBase64: fields[3] as String?,
      key: fields[4] as String?,
      faceAreas: (fields[5] as List?)?.cast<FaceArea>(),
      topConcernKey: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, SkinConcern obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.faceAgingImageBase64)
      ..writeByte(2)
      ..write(obj.faceBeautifyingImageBase64)
      ..writeByte(3)
      ..write(obj.selectedAreaImageBase64)
      ..writeByte(4)
      ..write(obj.key)
      ..writeByte(5)
      ..write(obj.faceAreas)
      ..writeByte(6)
      ..write(obj.topConcernKey);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SkinConcernAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SkinConcernImpl _$$SkinConcernImplFromJson(Map<String, dynamic> json) =>
    _$SkinConcernImpl(
      name: json['name'] as String?,
      faceAgingImageBase64: json['faceAgingImageBase64'] as String?,
      faceBeautifyingImageBase64: json['faceBeautifyingImageBase64'] as String?,
      selectedAreaImageBase64: json['selectedAreaImageBase64'] as String?,
      key: json['key'] as String?,
      faceAreas: (json['faceAreas'] as List<dynamic>?)
          ?.map((e) => FaceArea.fromJson(e as Map<String, dynamic>))
          .toList(),
      topConcernKey: json['topConcernKey'] as String?,
    );

Map<String, dynamic> _$$SkinConcernImplToJson(_$SkinConcernImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'faceAgingImageBase64': instance.faceAgingImageBase64,
      'faceBeautifyingImageBase64': instance.faceBeautifyingImageBase64,
      'selectedAreaImageBase64': instance.selectedAreaImageBase64,
      'key': instance.key,
      'faceAreas': instance.faceAreas,
      'topConcernKey': instance.topConcernKey,
    };
