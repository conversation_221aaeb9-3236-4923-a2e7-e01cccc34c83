// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'skin_analyze_recommendation.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SkinAnalyzeRecommendationAdapter
    extends TypeAdapter<SkinAnalyzeRecommendation> {
  @override
  final int typeId = 5;

  @override
  SkinAnalyzeRecommendation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SkinAnalyzeRecommendation(
      summary: fields[0] as String?,
      treatments: (fields[1] as List?)?.cast<Treatment>(),
    );
  }

  @override
  void write(BinaryWriter writer, SkinAnalyzeRecommendation obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.summary)
      ..writeByte(1)
      ..write(obj.treatments);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SkinAnalyzeRecommendationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TreatmentAdapter extends TypeAdapter<Treatment> {
  @override
  final int typeId = 6;

  @override
  Treatment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Treatment(
      id: fields[0] as String?,
      itemCode: fields[1] as String?,
      name: fields[2] as String?,
      description: fields[3] as String?,
      categories: (fields[4] as List?)?.cast<String>(),
      quantity: fields[5] as int?,
      price: fields[6] as int?,
      isTopRecommendation: fields[7] as bool?,
      solvedConcerns: (fields[8] as List?)?.cast<String>(),
      totalScore: fields[9] as double?,
      mediaUrl: fields[10] as String?,
      thumbnailUrl: fields[11] as String?,
      isChecked: fields[12] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, Treatment obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.itemCode)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.categories)
      ..writeByte(5)
      ..write(obj.quantity)
      ..writeByte(6)
      ..write(obj.price)
      ..writeByte(7)
      ..write(obj.isTopRecommendation)
      ..writeByte(8)
      ..write(obj.solvedConcerns)
      ..writeByte(9)
      ..write(obj.totalScore)
      ..writeByte(10)
      ..write(obj.mediaUrl)
      ..writeByte(11)
      ..write(obj.thumbnailUrl)
      ..writeByte(12)
      ..write(obj.isChecked);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TreatmentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SkinAnalyzeRecommendationImpl _$$SkinAnalyzeRecommendationImplFromJson(
        Map<String, dynamic> json) =>
    _$SkinAnalyzeRecommendationImpl(
      summary: json['summary'] as String?,
      treatments: (json['treatments'] as List<dynamic>?)
          ?.map((e) => Treatment.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$SkinAnalyzeRecommendationImplToJson(
        _$SkinAnalyzeRecommendationImpl instance) =>
    <String, dynamic>{
      'summary': instance.summary,
      'treatments': instance.treatments,
    };

_$TreatmentImpl _$$TreatmentImplFromJson(Map<String, dynamic> json) =>
    _$TreatmentImpl(
      id: json['id'] as String?,
      itemCode: json['item_code'] as String?,
      name: json['name'] as String?,
      description: json['description'] as String?,
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      quantity: (json['quantity'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toInt(),
      isTopRecommendation: json['is_top_recommendation'] as bool?,
      solvedConcerns: (json['solved_concerns'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      totalScore: (json['total_score'] as num?)?.toDouble(),
      mediaUrl: json['media_url'] as String?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      isChecked: json['is_checked'] as bool?,
    );

Map<String, dynamic> _$$TreatmentImplToJson(_$TreatmentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'item_code': instance.itemCode,
      'name': instance.name,
      'description': instance.description,
      'categories': instance.categories,
      'quantity': instance.quantity,
      'price': instance.price,
      'is_top_recommendation': instance.isTopRecommendation,
      'solved_concerns': instance.solvedConcerns,
      'total_score': instance.totalScore,
      'media_url': instance.mediaUrl,
      'thumbnail_url': instance.thumbnailUrl,
      'is_checked': instance.isChecked,
    };
