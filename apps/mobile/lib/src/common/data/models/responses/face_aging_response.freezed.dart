// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'face_aging_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

JobFaceAgingResponse _$JobFaceAgingResponseFromJson(Map<String, dynamic> json) {
  return _JobFaceAgingResponse.fromJson(json);
}

/// @nodoc
mixin _$JobFaceAgingResponse {
  String? get id => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  FaceAgingResponse? get response => throw _privateConstructorUsedError;

  /// Serializes this JobFaceAgingResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JobFaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JobFaceAgingResponseCopyWith<JobFaceAgingResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JobFaceAgingResponseCopyWith<$Res> {
  factory $JobFaceAgingResponseCopyWith(JobFaceAgingResponse value,
          $Res Function(JobFaceAgingResponse) then) =
      _$JobFaceAgingResponseCopyWithImpl<$Res, JobFaceAgingResponse>;
  @useResult
  $Res call({String? id, String? status, FaceAgingResponse? response});

  $FaceAgingResponseCopyWith<$Res>? get response;
}

/// @nodoc
class _$JobFaceAgingResponseCopyWithImpl<$Res,
        $Val extends JobFaceAgingResponse>
    implements $JobFaceAgingResponseCopyWith<$Res> {
  _$JobFaceAgingResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JobFaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? status = freezed,
    Object? response = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      response: freezed == response
          ? _value.response
          : response // ignore: cast_nullable_to_non_nullable
              as FaceAgingResponse?,
    ) as $Val);
  }

  /// Create a copy of JobFaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FaceAgingResponseCopyWith<$Res>? get response {
    if (_value.response == null) {
      return null;
    }

    return $FaceAgingResponseCopyWith<$Res>(_value.response!, (value) {
      return _then(_value.copyWith(response: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$JobFaceAgingResponseImplCopyWith<$Res>
    implements $JobFaceAgingResponseCopyWith<$Res> {
  factory _$$JobFaceAgingResponseImplCopyWith(_$JobFaceAgingResponseImpl value,
          $Res Function(_$JobFaceAgingResponseImpl) then) =
      __$$JobFaceAgingResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id, String? status, FaceAgingResponse? response});

  @override
  $FaceAgingResponseCopyWith<$Res>? get response;
}

/// @nodoc
class __$$JobFaceAgingResponseImplCopyWithImpl<$Res>
    extends _$JobFaceAgingResponseCopyWithImpl<$Res, _$JobFaceAgingResponseImpl>
    implements _$$JobFaceAgingResponseImplCopyWith<$Res> {
  __$$JobFaceAgingResponseImplCopyWithImpl(_$JobFaceAgingResponseImpl _value,
      $Res Function(_$JobFaceAgingResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of JobFaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? status = freezed,
    Object? response = freezed,
  }) {
    return _then(_$JobFaceAgingResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      response: freezed == response
          ? _value.response
          : response // ignore: cast_nullable_to_non_nullable
              as FaceAgingResponse?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$JobFaceAgingResponseImpl implements _JobFaceAgingResponse {
  const _$JobFaceAgingResponseImpl({this.id, this.status, this.response});

  factory _$JobFaceAgingResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$JobFaceAgingResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final String? status;
  @override
  final FaceAgingResponse? response;

  @override
  String toString() {
    return 'JobFaceAgingResponse(id: $id, status: $status, response: $response)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JobFaceAgingResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.response, response) ||
                other.response == response));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, status, response);

  /// Create a copy of JobFaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JobFaceAgingResponseImplCopyWith<_$JobFaceAgingResponseImpl>
      get copyWith =>
          __$$JobFaceAgingResponseImplCopyWithImpl<_$JobFaceAgingResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JobFaceAgingResponseImplToJson(
      this,
    );
  }
}

abstract class _JobFaceAgingResponse implements JobFaceAgingResponse {
  const factory _JobFaceAgingResponse(
      {final String? id,
      final String? status,
      final FaceAgingResponse? response}) = _$JobFaceAgingResponseImpl;

  factory _JobFaceAgingResponse.fromJson(Map<String, dynamic> json) =
      _$JobFaceAgingResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String? get status;
  @override
  FaceAgingResponse? get response;

  /// Create a copy of JobFaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JobFaceAgingResponseImplCopyWith<_$JobFaceAgingResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FaceAgingResponse _$FaceAgingResponseFromJson(Map<String, dynamic> json) {
  return _FaceAgingResponse.fromJson(json);
}

/// @nodoc
mixin _$FaceAgingResponse {
  List<FaceAging> get generatedImages => throw _privateConstructorUsedError;

  /// Serializes this FaceAgingResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FaceAgingResponseCopyWith<FaceAgingResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FaceAgingResponseCopyWith<$Res> {
  factory $FaceAgingResponseCopyWith(
          FaceAgingResponse value, $Res Function(FaceAgingResponse) then) =
      _$FaceAgingResponseCopyWithImpl<$Res, FaceAgingResponse>;
  @useResult
  $Res call({List<FaceAging> generatedImages});
}

/// @nodoc
class _$FaceAgingResponseCopyWithImpl<$Res, $Val extends FaceAgingResponse>
    implements $FaceAgingResponseCopyWith<$Res> {
  _$FaceAgingResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? generatedImages = null,
  }) {
    return _then(_value.copyWith(
      generatedImages: null == generatedImages
          ? _value.generatedImages
          : generatedImages // ignore: cast_nullable_to_non_nullable
              as List<FaceAging>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FaceAgingResponseImplCopyWith<$Res>
    implements $FaceAgingResponseCopyWith<$Res> {
  factory _$$FaceAgingResponseImplCopyWith(_$FaceAgingResponseImpl value,
          $Res Function(_$FaceAgingResponseImpl) then) =
      __$$FaceAgingResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<FaceAging> generatedImages});
}

/// @nodoc
class __$$FaceAgingResponseImplCopyWithImpl<$Res>
    extends _$FaceAgingResponseCopyWithImpl<$Res, _$FaceAgingResponseImpl>
    implements _$$FaceAgingResponseImplCopyWith<$Res> {
  __$$FaceAgingResponseImplCopyWithImpl(_$FaceAgingResponseImpl _value,
      $Res Function(_$FaceAgingResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of FaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? generatedImages = null,
  }) {
    return _then(_$FaceAgingResponseImpl(
      generatedImages: null == generatedImages
          ? _value._generatedImages
          : generatedImages // ignore: cast_nullable_to_non_nullable
              as List<FaceAging>,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$FaceAgingResponseImpl implements _FaceAgingResponse {
  const _$FaceAgingResponseImpl(
      {required final List<FaceAging> generatedImages})
      : _generatedImages = generatedImages;

  factory _$FaceAgingResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$FaceAgingResponseImplFromJson(json);

  final List<FaceAging> _generatedImages;
  @override
  List<FaceAging> get generatedImages {
    if (_generatedImages is EqualUnmodifiableListView) return _generatedImages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_generatedImages);
  }

  @override
  String toString() {
    return 'FaceAgingResponse(generatedImages: $generatedImages)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FaceAgingResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._generatedImages, _generatedImages));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_generatedImages));

  /// Create a copy of FaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FaceAgingResponseImplCopyWith<_$FaceAgingResponseImpl> get copyWith =>
      __$$FaceAgingResponseImplCopyWithImpl<_$FaceAgingResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FaceAgingResponseImplToJson(
      this,
    );
  }
}

abstract class _FaceAgingResponse implements FaceAgingResponse {
  const factory _FaceAgingResponse(
          {required final List<FaceAging> generatedImages}) =
      _$FaceAgingResponseImpl;

  factory _FaceAgingResponse.fromJson(Map<String, dynamic> json) =
      _$FaceAgingResponseImpl.fromJson;

  @override
  List<FaceAging> get generatedImages;

  /// Create a copy of FaceAgingResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FaceAgingResponseImplCopyWith<_$FaceAgingResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
