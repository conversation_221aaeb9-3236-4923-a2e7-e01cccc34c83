// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'face_aging_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JobFaceAgingResponseImpl _$$JobFaceAgingResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$JobFaceAgingResponseImpl(
      id: json['id'] as String?,
      status: json['status'] as String?,
      response: json['response'] == null
          ? null
          : FaceAgingResponse.fromJson(
              json['response'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$JobFaceAgingResponseImplToJson(
        _$JobFaceAgingResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'status': instance.status,
      'response': instance.response,
    };

_$FaceAgingResponseImpl _$$FaceAgingResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$FaceAgingResponseImpl(
      generatedImages: (json['generated_images'] as List<dynamic>)
          .map((e) => FaceAging.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$FaceAgingResponseImplToJson(
        _$FaceAgingResponseImpl instance) =>
    <String, dynamic>{
      'generated_images': instance.generatedImages,
    };
