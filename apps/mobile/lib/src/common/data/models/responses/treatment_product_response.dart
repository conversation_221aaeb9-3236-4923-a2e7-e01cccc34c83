import 'package:freezed_annotation/freezed_annotation.dart';

part 'treatment_product_response.freezed.dart';
part 'treatment_product_response.g.dart';

@freezed
class TreatmentProductResponse with _$TreatmentProductResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentProductResponse({
    String? id,
    String? itemCode,
    String? name,
    String? type,
    String? description,
    String? intervalId,
    int? price,
    int? quantity,
    bool? isTopRecommendation,
    int? createdAt,
    int? updatedAt,
    List<TreatmentCategoryResponse>? category,
    TreatmentIntervalResponse? interval,
    List<TreatmentConcernResponse>? concern,
    List<TreatmentSurveyQuestionResponse>? surveyQuestions,
    String? mediaUrl,
    String? thumbnailUrl,
    String? notes,
    int? durationTopRecommendation,
    String? createdBy,
    String? updatedBy,
  }) = _TreatmentProductResponse;

  factory TreatmentProductResponse.fromJson(Map<String, dynamic> json) =>
      _$TreatmentProductResponseFromJson(json);
}

@freezed
class TreatmentCategoryResponse with _$TreatmentCategoryResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentCategoryResponse({
    String? id,
    String? name,
    int? createdAt,
    int? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) = _TreatmentCategoryResponse;

  factory TreatmentCategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$TreatmentCategoryResponseFromJson(json);
}

@freezed
class TreatmentIntervalResponse with _$TreatmentIntervalResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentIntervalResponse({
    String? id,
    int? days,
    int? createdAt,
    int? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) = _TreatmentIntervalResponse;

  factory TreatmentIntervalResponse.fromJson(Map<String, dynamic> json) =>
      _$TreatmentIntervalResponseFromJson(json);
}

@freezed
class TreatmentConcernResponse with _$TreatmentConcernResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentConcernResponse({
    String? id,
    String? name,
    int? createdAt,
    int? updatedAt,
    String? createdBy,
    String? updatedBy,
    List<TreatmentConcernIndicationsResponse>? concernIndications,
  }) = _TreatmentConcernResponse;

  factory TreatmentConcernResponse.fromJson(Map<String, dynamic> json) =>
      _$TreatmentConcernResponseFromJson(json);
}

@freezed
class TreatmentConcernIndicationsResponse
    with _$TreatmentConcernIndicationsResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentConcernIndicationsResponse({
    String? id,
    String? name,
    int? createdAt,
    int? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) = _TreatmentConcernIndicationsResponse;

  factory TreatmentConcernIndicationsResponse.fromJson(Map<String, dynamic> json) =>
      _$TreatmentConcernIndicationsResponseFromJson(json);
}

@freezed
class TreatmentSurveyQuestionResponse with _$TreatmentSurveyQuestionResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentSurveyQuestionResponse({
    String? id,
    String? question,
    List<TreatmentSurveyAnswerResponse>? answers,
    int? selectedAnswer,
    int? questionOrder,
  }) = _TreatmentSurveyQuestionResponse;

  factory TreatmentSurveyQuestionResponse.fromJson(Map<String, dynamic> json) =>
      _$TreatmentSurveyQuestionResponseFromJson(json);
}

@freezed
class TreatmentSurveyAnswerResponse with _$TreatmentSurveyAnswerResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentSurveyAnswerResponse({
    String? title,
    String? description,
    String? imageUrl,
  }) = _TreatmentSurveyAnswerResponse;

  factory TreatmentSurveyAnswerResponse.fromJson(Map<String, dynamic> json) =>
      _$TreatmentSurveyAnswerResponseFromJson(json);
}
