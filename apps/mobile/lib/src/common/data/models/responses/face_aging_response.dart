import 'package:euromedica_aizer/src/common/domain/entities/face_aging/face_aging.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'face_aging_response.freezed.dart';
part 'face_aging_response.g.dart';

@freezed
class JobFaceAgingResponse with _$JobFaceAgingResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory JobFaceAgingResponse({
    String? id,
    String? status,
    FaceAgingResponse? response,
  }) = _JobFaceAgingResponse;

  factory JobFaceAgingResponse.fromJson(Map<String, dynamic> json) =>
      _$JobFaceAgingResponseFromJson(json);
}

@freezed
class FaceAgingResponse with _$FaceAgingResponse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory FaceAgingResponse({
    required List<FaceAging> generatedImages,
  }) = _FaceAgingResponse;

  factory FaceAgingResponse.fromJson(Map<String, dynamic> json) =>
      _$FaceAgingResponseFromJson(json);
}
