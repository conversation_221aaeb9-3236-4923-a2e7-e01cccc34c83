// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'treatment_product_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TreatmentProductResponse _$TreatmentProductResponseFromJson(
    Map<String, dynamic> json) {
  return _TreatmentProductResponse.fromJson(json);
}

/// @nodoc
mixin _$TreatmentProductResponse {
  String? get id => throw _privateConstructorUsedError;
  String? get itemCode => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get intervalId => throw _privateConstructorUsedError;
  int? get price => throw _privateConstructorUsedError;
  int? get quantity => throw _privateConstructorUsedError;
  bool? get isTopRecommendation => throw _privateConstructorUsedError;
  int? get createdAt => throw _privateConstructorUsedError;
  int? get updatedAt => throw _privateConstructorUsedError;
  List<TreatmentCategoryResponse>? get category =>
      throw _privateConstructorUsedError;
  TreatmentIntervalResponse? get interval => throw _privateConstructorUsedError;
  List<TreatmentConcernResponse>? get concern =>
      throw _privateConstructorUsedError;
  List<TreatmentSurveyQuestionResponse>? get surveyQuestions =>
      throw _privateConstructorUsedError;
  String? get mediaUrl => throw _privateConstructorUsedError;
  String? get thumbnailUrl => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  int? get durationTopRecommendation => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;

  /// Serializes this TreatmentProductResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentProductResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentProductResponseCopyWith<TreatmentProductResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentProductResponseCopyWith<$Res> {
  factory $TreatmentProductResponseCopyWith(TreatmentProductResponse value,
          $Res Function(TreatmentProductResponse) then) =
      _$TreatmentProductResponseCopyWithImpl<$Res, TreatmentProductResponse>;
  @useResult
  $Res call(
      {String? id,
      String? itemCode,
      String? name,
      String? type,
      String? description,
      String? intervalId,
      int? price,
      int? quantity,
      bool? isTopRecommendation,
      int? createdAt,
      int? updatedAt,
      List<TreatmentCategoryResponse>? category,
      TreatmentIntervalResponse? interval,
      List<TreatmentConcernResponse>? concern,
      List<TreatmentSurveyQuestionResponse>? surveyQuestions,
      String? mediaUrl,
      String? thumbnailUrl,
      String? notes,
      int? durationTopRecommendation,
      String? createdBy,
      String? updatedBy});

  $TreatmentIntervalResponseCopyWith<$Res>? get interval;
}

/// @nodoc
class _$TreatmentProductResponseCopyWithImpl<$Res,
        $Val extends TreatmentProductResponse>
    implements $TreatmentProductResponseCopyWith<$Res> {
  _$TreatmentProductResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentProductResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? itemCode = freezed,
    Object? name = freezed,
    Object? type = freezed,
    Object? description = freezed,
    Object? intervalId = freezed,
    Object? price = freezed,
    Object? quantity = freezed,
    Object? isTopRecommendation = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? category = freezed,
    Object? interval = freezed,
    Object? concern = freezed,
    Object? surveyQuestions = freezed,
    Object? mediaUrl = freezed,
    Object? thumbnailUrl = freezed,
    Object? notes = freezed,
    Object? durationTopRecommendation = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      itemCode: freezed == itemCode
          ? _value.itemCode
          : itemCode // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      intervalId: freezed == intervalId
          ? _value.intervalId
          : intervalId // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      isTopRecommendation: freezed == isTopRecommendation
          ? _value.isTopRecommendation
          : isTopRecommendation // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as List<TreatmentCategoryResponse>?,
      interval: freezed == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as TreatmentIntervalResponse?,
      concern: freezed == concern
          ? _value.concern
          : concern // ignore: cast_nullable_to_non_nullable
              as List<TreatmentConcernResponse>?,
      surveyQuestions: freezed == surveyQuestions
          ? _value.surveyQuestions
          : surveyQuestions // ignore: cast_nullable_to_non_nullable
              as List<TreatmentSurveyQuestionResponse>?,
      mediaUrl: freezed == mediaUrl
          ? _value.mediaUrl
          : mediaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailUrl: freezed == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      durationTopRecommendation: freezed == durationTopRecommendation
          ? _value.durationTopRecommendation
          : durationTopRecommendation // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of TreatmentProductResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TreatmentIntervalResponseCopyWith<$Res>? get interval {
    if (_value.interval == null) {
      return null;
    }

    return $TreatmentIntervalResponseCopyWith<$Res>(_value.interval!, (value) {
      return _then(_value.copyWith(interval: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TreatmentProductResponseImplCopyWith<$Res>
    implements $TreatmentProductResponseCopyWith<$Res> {
  factory _$$TreatmentProductResponseImplCopyWith(
          _$TreatmentProductResponseImpl value,
          $Res Function(_$TreatmentProductResponseImpl) then) =
      __$$TreatmentProductResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? itemCode,
      String? name,
      String? type,
      String? description,
      String? intervalId,
      int? price,
      int? quantity,
      bool? isTopRecommendation,
      int? createdAt,
      int? updatedAt,
      List<TreatmentCategoryResponse>? category,
      TreatmentIntervalResponse? interval,
      List<TreatmentConcernResponse>? concern,
      List<TreatmentSurveyQuestionResponse>? surveyQuestions,
      String? mediaUrl,
      String? thumbnailUrl,
      String? notes,
      int? durationTopRecommendation,
      String? createdBy,
      String? updatedBy});

  @override
  $TreatmentIntervalResponseCopyWith<$Res>? get interval;
}

/// @nodoc
class __$$TreatmentProductResponseImplCopyWithImpl<$Res>
    extends _$TreatmentProductResponseCopyWithImpl<$Res,
        _$TreatmentProductResponseImpl>
    implements _$$TreatmentProductResponseImplCopyWith<$Res> {
  __$$TreatmentProductResponseImplCopyWithImpl(
      _$TreatmentProductResponseImpl _value,
      $Res Function(_$TreatmentProductResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentProductResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? itemCode = freezed,
    Object? name = freezed,
    Object? type = freezed,
    Object? description = freezed,
    Object? intervalId = freezed,
    Object? price = freezed,
    Object? quantity = freezed,
    Object? isTopRecommendation = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? category = freezed,
    Object? interval = freezed,
    Object? concern = freezed,
    Object? surveyQuestions = freezed,
    Object? mediaUrl = freezed,
    Object? thumbnailUrl = freezed,
    Object? notes = freezed,
    Object? durationTopRecommendation = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_$TreatmentProductResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      itemCode: freezed == itemCode
          ? _value.itemCode
          : itemCode // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      intervalId: freezed == intervalId
          ? _value.intervalId
          : intervalId // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      isTopRecommendation: freezed == isTopRecommendation
          ? _value.isTopRecommendation
          : isTopRecommendation // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _value._category
          : category // ignore: cast_nullable_to_non_nullable
              as List<TreatmentCategoryResponse>?,
      interval: freezed == interval
          ? _value.interval
          : interval // ignore: cast_nullable_to_non_nullable
              as TreatmentIntervalResponse?,
      concern: freezed == concern
          ? _value._concern
          : concern // ignore: cast_nullable_to_non_nullable
              as List<TreatmentConcernResponse>?,
      surveyQuestions: freezed == surveyQuestions
          ? _value._surveyQuestions
          : surveyQuestions // ignore: cast_nullable_to_non_nullable
              as List<TreatmentSurveyQuestionResponse>?,
      mediaUrl: freezed == mediaUrl
          ? _value.mediaUrl
          : mediaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailUrl: freezed == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      durationTopRecommendation: freezed == durationTopRecommendation
          ? _value.durationTopRecommendation
          : durationTopRecommendation // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentProductResponseImpl implements _TreatmentProductResponse {
  const _$TreatmentProductResponseImpl(
      {this.id,
      this.itemCode,
      this.name,
      this.type,
      this.description,
      this.intervalId,
      this.price,
      this.quantity,
      this.isTopRecommendation,
      this.createdAt,
      this.updatedAt,
      final List<TreatmentCategoryResponse>? category,
      this.interval,
      final List<TreatmentConcernResponse>? concern,
      final List<TreatmentSurveyQuestionResponse>? surveyQuestions,
      this.mediaUrl,
      this.thumbnailUrl,
      this.notes,
      this.durationTopRecommendation,
      this.createdBy,
      this.updatedBy})
      : _category = category,
        _concern = concern,
        _surveyQuestions = surveyQuestions;

  factory _$TreatmentProductResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TreatmentProductResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final String? itemCode;
  @override
  final String? name;
  @override
  final String? type;
  @override
  final String? description;
  @override
  final String? intervalId;
  @override
  final int? price;
  @override
  final int? quantity;
  @override
  final bool? isTopRecommendation;
  @override
  final int? createdAt;
  @override
  final int? updatedAt;
  final List<TreatmentCategoryResponse>? _category;
  @override
  List<TreatmentCategoryResponse>? get category {
    final value = _category;
    if (value == null) return null;
    if (_category is EqualUnmodifiableListView) return _category;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final TreatmentIntervalResponse? interval;
  final List<TreatmentConcernResponse>? _concern;
  @override
  List<TreatmentConcernResponse>? get concern {
    final value = _concern;
    if (value == null) return null;
    if (_concern is EqualUnmodifiableListView) return _concern;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TreatmentSurveyQuestionResponse>? _surveyQuestions;
  @override
  List<TreatmentSurveyQuestionResponse>? get surveyQuestions {
    final value = _surveyQuestions;
    if (value == null) return null;
    if (_surveyQuestions is EqualUnmodifiableListView) return _surveyQuestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? mediaUrl;
  @override
  final String? thumbnailUrl;
  @override
  final String? notes;
  @override
  final int? durationTopRecommendation;
  @override
  final String? createdBy;
  @override
  final String? updatedBy;

  @override
  String toString() {
    return 'TreatmentProductResponse(id: $id, itemCode: $itemCode, name: $name, type: $type, description: $description, intervalId: $intervalId, price: $price, quantity: $quantity, isTopRecommendation: $isTopRecommendation, createdAt: $createdAt, updatedAt: $updatedAt, category: $category, interval: $interval, concern: $concern, surveyQuestions: $surveyQuestions, mediaUrl: $mediaUrl, thumbnailUrl: $thumbnailUrl, notes: $notes, durationTopRecommendation: $durationTopRecommendation, createdBy: $createdBy, updatedBy: $updatedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentProductResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.itemCode, itemCode) ||
                other.itemCode == itemCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.intervalId, intervalId) ||
                other.intervalId == intervalId) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.isTopRecommendation, isTopRecommendation) ||
                other.isTopRecommendation == isTopRecommendation) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other._category, _category) &&
            (identical(other.interval, interval) ||
                other.interval == interval) &&
            const DeepCollectionEquality().equals(other._concern, _concern) &&
            const DeepCollectionEquality()
                .equals(other._surveyQuestions, _surveyQuestions) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.durationTopRecommendation,
                    durationTopRecommendation) ||
                other.durationTopRecommendation == durationTopRecommendation) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        itemCode,
        name,
        type,
        description,
        intervalId,
        price,
        quantity,
        isTopRecommendation,
        createdAt,
        updatedAt,
        const DeepCollectionEquality().hash(_category),
        interval,
        const DeepCollectionEquality().hash(_concern),
        const DeepCollectionEquality().hash(_surveyQuestions),
        mediaUrl,
        thumbnailUrl,
        notes,
        durationTopRecommendation,
        createdBy,
        updatedBy
      ]);

  /// Create a copy of TreatmentProductResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentProductResponseImplCopyWith<_$TreatmentProductResponseImpl>
      get copyWith => __$$TreatmentProductResponseImplCopyWithImpl<
          _$TreatmentProductResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentProductResponseImplToJson(
      this,
    );
  }
}

abstract class _TreatmentProductResponse implements TreatmentProductResponse {
  const factory _TreatmentProductResponse(
      {final String? id,
      final String? itemCode,
      final String? name,
      final String? type,
      final String? description,
      final String? intervalId,
      final int? price,
      final int? quantity,
      final bool? isTopRecommendation,
      final int? createdAt,
      final int? updatedAt,
      final List<TreatmentCategoryResponse>? category,
      final TreatmentIntervalResponse? interval,
      final List<TreatmentConcernResponse>? concern,
      final List<TreatmentSurveyQuestionResponse>? surveyQuestions,
      final String? mediaUrl,
      final String? thumbnailUrl,
      final String? notes,
      final int? durationTopRecommendation,
      final String? createdBy,
      final String? updatedBy}) = _$TreatmentProductResponseImpl;

  factory _TreatmentProductResponse.fromJson(Map<String, dynamic> json) =
      _$TreatmentProductResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String? get itemCode;
  @override
  String? get name;
  @override
  String? get type;
  @override
  String? get description;
  @override
  String? get intervalId;
  @override
  int? get price;
  @override
  int? get quantity;
  @override
  bool? get isTopRecommendation;
  @override
  int? get createdAt;
  @override
  int? get updatedAt;
  @override
  List<TreatmentCategoryResponse>? get category;
  @override
  TreatmentIntervalResponse? get interval;
  @override
  List<TreatmentConcernResponse>? get concern;
  @override
  List<TreatmentSurveyQuestionResponse>? get surveyQuestions;
  @override
  String? get mediaUrl;
  @override
  String? get thumbnailUrl;
  @override
  String? get notes;
  @override
  int? get durationTopRecommendation;
  @override
  String? get createdBy;
  @override
  String? get updatedBy;

  /// Create a copy of TreatmentProductResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentProductResponseImplCopyWith<_$TreatmentProductResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TreatmentCategoryResponse _$TreatmentCategoryResponseFromJson(
    Map<String, dynamic> json) {
  return _TreatmentCategoryResponse.fromJson(json);
}

/// @nodoc
mixin _$TreatmentCategoryResponse {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get createdAt => throw _privateConstructorUsedError;
  int? get updatedAt => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;

  /// Serializes this TreatmentCategoryResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentCategoryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentCategoryResponseCopyWith<TreatmentCategoryResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentCategoryResponseCopyWith<$Res> {
  factory $TreatmentCategoryResponseCopyWith(TreatmentCategoryResponse value,
          $Res Function(TreatmentCategoryResponse) then) =
      _$TreatmentCategoryResponseCopyWithImpl<$Res, TreatmentCategoryResponse>;
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class _$TreatmentCategoryResponseCopyWithImpl<$Res,
        $Val extends TreatmentCategoryResponse>
    implements $TreatmentCategoryResponseCopyWith<$Res> {
  _$TreatmentCategoryResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentCategoryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentCategoryResponseImplCopyWith<$Res>
    implements $TreatmentCategoryResponseCopyWith<$Res> {
  factory _$$TreatmentCategoryResponseImplCopyWith(
          _$TreatmentCategoryResponseImpl value,
          $Res Function(_$TreatmentCategoryResponseImpl) then) =
      __$$TreatmentCategoryResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class __$$TreatmentCategoryResponseImplCopyWithImpl<$Res>
    extends _$TreatmentCategoryResponseCopyWithImpl<$Res,
        _$TreatmentCategoryResponseImpl>
    implements _$$TreatmentCategoryResponseImplCopyWith<$Res> {
  __$$TreatmentCategoryResponseImplCopyWithImpl(
      _$TreatmentCategoryResponseImpl _value,
      $Res Function(_$TreatmentCategoryResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentCategoryResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_$TreatmentCategoryResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentCategoryResponseImpl implements _TreatmentCategoryResponse {
  const _$TreatmentCategoryResponseImpl(
      {this.id,
      this.name,
      this.createdAt,
      this.updatedAt,
      this.createdBy,
      this.updatedBy});

  factory _$TreatmentCategoryResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TreatmentCategoryResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  @override
  final int? createdAt;
  @override
  final int? updatedAt;
  @override
  final String? createdBy;
  @override
  final String? updatedBy;

  @override
  String toString() {
    return 'TreatmentCategoryResponse(id: $id, name: $name, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentCategoryResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, createdAt, updatedAt, createdBy, updatedBy);

  /// Create a copy of TreatmentCategoryResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentCategoryResponseImplCopyWith<_$TreatmentCategoryResponseImpl>
      get copyWith => __$$TreatmentCategoryResponseImplCopyWithImpl<
          _$TreatmentCategoryResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentCategoryResponseImplToJson(
      this,
    );
  }
}

abstract class _TreatmentCategoryResponse implements TreatmentCategoryResponse {
  const factory _TreatmentCategoryResponse(
      {final String? id,
      final String? name,
      final int? createdAt,
      final int? updatedAt,
      final String? createdBy,
      final String? updatedBy}) = _$TreatmentCategoryResponseImpl;

  factory _TreatmentCategoryResponse.fromJson(Map<String, dynamic> json) =
      _$TreatmentCategoryResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String? get name;
  @override
  int? get createdAt;
  @override
  int? get updatedAt;
  @override
  String? get createdBy;
  @override
  String? get updatedBy;

  /// Create a copy of TreatmentCategoryResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentCategoryResponseImplCopyWith<_$TreatmentCategoryResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TreatmentIntervalResponse _$TreatmentIntervalResponseFromJson(
    Map<String, dynamic> json) {
  return _TreatmentIntervalResponse.fromJson(json);
}

/// @nodoc
mixin _$TreatmentIntervalResponse {
  String? get id => throw _privateConstructorUsedError;
  int? get days => throw _privateConstructorUsedError;
  int? get createdAt => throw _privateConstructorUsedError;
  int? get updatedAt => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;

  /// Serializes this TreatmentIntervalResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentIntervalResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentIntervalResponseCopyWith<TreatmentIntervalResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentIntervalResponseCopyWith<$Res> {
  factory $TreatmentIntervalResponseCopyWith(TreatmentIntervalResponse value,
          $Res Function(TreatmentIntervalResponse) then) =
      _$TreatmentIntervalResponseCopyWithImpl<$Res, TreatmentIntervalResponse>;
  @useResult
  $Res call(
      {String? id,
      int? days,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class _$TreatmentIntervalResponseCopyWithImpl<$Res,
        $Val extends TreatmentIntervalResponse>
    implements $TreatmentIntervalResponseCopyWith<$Res> {
  _$TreatmentIntervalResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentIntervalResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? days = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentIntervalResponseImplCopyWith<$Res>
    implements $TreatmentIntervalResponseCopyWith<$Res> {
  factory _$$TreatmentIntervalResponseImplCopyWith(
          _$TreatmentIntervalResponseImpl value,
          $Res Function(_$TreatmentIntervalResponseImpl) then) =
      __$$TreatmentIntervalResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      int? days,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class __$$TreatmentIntervalResponseImplCopyWithImpl<$Res>
    extends _$TreatmentIntervalResponseCopyWithImpl<$Res,
        _$TreatmentIntervalResponseImpl>
    implements _$$TreatmentIntervalResponseImplCopyWith<$Res> {
  __$$TreatmentIntervalResponseImplCopyWithImpl(
      _$TreatmentIntervalResponseImpl _value,
      $Res Function(_$TreatmentIntervalResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentIntervalResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? days = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_$TreatmentIntervalResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentIntervalResponseImpl implements _TreatmentIntervalResponse {
  const _$TreatmentIntervalResponseImpl(
      {this.id,
      this.days,
      this.createdAt,
      this.updatedAt,
      this.createdBy,
      this.updatedBy});

  factory _$TreatmentIntervalResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TreatmentIntervalResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final int? days;
  @override
  final int? createdAt;
  @override
  final int? updatedAt;
  @override
  final String? createdBy;
  @override
  final String? updatedBy;

  @override
  String toString() {
    return 'TreatmentIntervalResponse(id: $id, days: $days, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentIntervalResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.days, days) || other.days == days) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, days, createdAt, updatedAt, createdBy, updatedBy);

  /// Create a copy of TreatmentIntervalResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentIntervalResponseImplCopyWith<_$TreatmentIntervalResponseImpl>
      get copyWith => __$$TreatmentIntervalResponseImplCopyWithImpl<
          _$TreatmentIntervalResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentIntervalResponseImplToJson(
      this,
    );
  }
}

abstract class _TreatmentIntervalResponse implements TreatmentIntervalResponse {
  const factory _TreatmentIntervalResponse(
      {final String? id,
      final int? days,
      final int? createdAt,
      final int? updatedAt,
      final String? createdBy,
      final String? updatedBy}) = _$TreatmentIntervalResponseImpl;

  factory _TreatmentIntervalResponse.fromJson(Map<String, dynamic> json) =
      _$TreatmentIntervalResponseImpl.fromJson;

  @override
  String? get id;
  @override
  int? get days;
  @override
  int? get createdAt;
  @override
  int? get updatedAt;
  @override
  String? get createdBy;
  @override
  String? get updatedBy;

  /// Create a copy of TreatmentIntervalResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentIntervalResponseImplCopyWith<_$TreatmentIntervalResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TreatmentConcernResponse _$TreatmentConcernResponseFromJson(
    Map<String, dynamic> json) {
  return _TreatmentConcernResponse.fromJson(json);
}

/// @nodoc
mixin _$TreatmentConcernResponse {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get createdAt => throw _privateConstructorUsedError;
  int? get updatedAt => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;
  List<TreatmentConcernIndicationsResponse>? get concernIndications =>
      throw _privateConstructorUsedError;

  /// Serializes this TreatmentConcernResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentConcernResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentConcernResponseCopyWith<TreatmentConcernResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentConcernResponseCopyWith<$Res> {
  factory $TreatmentConcernResponseCopyWith(TreatmentConcernResponse value,
          $Res Function(TreatmentConcernResponse) then) =
      _$TreatmentConcernResponseCopyWithImpl<$Res, TreatmentConcernResponse>;
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy,
      List<TreatmentConcernIndicationsResponse>? concernIndications});
}

/// @nodoc
class _$TreatmentConcernResponseCopyWithImpl<$Res,
        $Val extends TreatmentConcernResponse>
    implements $TreatmentConcernResponseCopyWith<$Res> {
  _$TreatmentConcernResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentConcernResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
    Object? concernIndications = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      concernIndications: freezed == concernIndications
          ? _value.concernIndications
          : concernIndications // ignore: cast_nullable_to_non_nullable
              as List<TreatmentConcernIndicationsResponse>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentConcernResponseImplCopyWith<$Res>
    implements $TreatmentConcernResponseCopyWith<$Res> {
  factory _$$TreatmentConcernResponseImplCopyWith(
          _$TreatmentConcernResponseImpl value,
          $Res Function(_$TreatmentConcernResponseImpl) then) =
      __$$TreatmentConcernResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy,
      List<TreatmentConcernIndicationsResponse>? concernIndications});
}

/// @nodoc
class __$$TreatmentConcernResponseImplCopyWithImpl<$Res>
    extends _$TreatmentConcernResponseCopyWithImpl<$Res,
        _$TreatmentConcernResponseImpl>
    implements _$$TreatmentConcernResponseImplCopyWith<$Res> {
  __$$TreatmentConcernResponseImplCopyWithImpl(
      _$TreatmentConcernResponseImpl _value,
      $Res Function(_$TreatmentConcernResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentConcernResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
    Object? concernIndications = freezed,
  }) {
    return _then(_$TreatmentConcernResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      concernIndications: freezed == concernIndications
          ? _value._concernIndications
          : concernIndications // ignore: cast_nullable_to_non_nullable
              as List<TreatmentConcernIndicationsResponse>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentConcernResponseImpl implements _TreatmentConcernResponse {
  const _$TreatmentConcernResponseImpl(
      {this.id,
      this.name,
      this.createdAt,
      this.updatedAt,
      this.createdBy,
      this.updatedBy,
      final List<TreatmentConcernIndicationsResponse>? concernIndications})
      : _concernIndications = concernIndications;

  factory _$TreatmentConcernResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TreatmentConcernResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  @override
  final int? createdAt;
  @override
  final int? updatedAt;
  @override
  final String? createdBy;
  @override
  final String? updatedBy;
  final List<TreatmentConcernIndicationsResponse>? _concernIndications;
  @override
  List<TreatmentConcernIndicationsResponse>? get concernIndications {
    final value = _concernIndications;
    if (value == null) return null;
    if (_concernIndications is EqualUnmodifiableListView)
      return _concernIndications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TreatmentConcernResponse(id: $id, name: $name, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy, concernIndications: $concernIndications)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentConcernResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy) &&
            const DeepCollectionEquality()
                .equals(other._concernIndications, _concernIndications));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      createdAt,
      updatedAt,
      createdBy,
      updatedBy,
      const DeepCollectionEquality().hash(_concernIndications));

  /// Create a copy of TreatmentConcernResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentConcernResponseImplCopyWith<_$TreatmentConcernResponseImpl>
      get copyWith => __$$TreatmentConcernResponseImplCopyWithImpl<
          _$TreatmentConcernResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentConcernResponseImplToJson(
      this,
    );
  }
}

abstract class _TreatmentConcernResponse implements TreatmentConcernResponse {
  const factory _TreatmentConcernResponse(
      {final String? id,
      final String? name,
      final int? createdAt,
      final int? updatedAt,
      final String? createdBy,
      final String? updatedBy,
      final List<TreatmentConcernIndicationsResponse>?
          concernIndications}) = _$TreatmentConcernResponseImpl;

  factory _TreatmentConcernResponse.fromJson(Map<String, dynamic> json) =
      _$TreatmentConcernResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String? get name;
  @override
  int? get createdAt;
  @override
  int? get updatedAt;
  @override
  String? get createdBy;
  @override
  String? get updatedBy;
  @override
  List<TreatmentConcernIndicationsResponse>? get concernIndications;

  /// Create a copy of TreatmentConcernResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentConcernResponseImplCopyWith<_$TreatmentConcernResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TreatmentConcernIndicationsResponse
    _$TreatmentConcernIndicationsResponseFromJson(Map<String, dynamic> json) {
  return _TreatmentConcernIndicationsResponse.fromJson(json);
}

/// @nodoc
mixin _$TreatmentConcernIndicationsResponse {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get createdAt => throw _privateConstructorUsedError;
  int? get updatedAt => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;

  /// Serializes this TreatmentConcernIndicationsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentConcernIndicationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentConcernIndicationsResponseCopyWith<
          TreatmentConcernIndicationsResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentConcernIndicationsResponseCopyWith<$Res> {
  factory $TreatmentConcernIndicationsResponseCopyWith(
          TreatmentConcernIndicationsResponse value,
          $Res Function(TreatmentConcernIndicationsResponse) then) =
      _$TreatmentConcernIndicationsResponseCopyWithImpl<$Res,
          TreatmentConcernIndicationsResponse>;
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class _$TreatmentConcernIndicationsResponseCopyWithImpl<$Res,
        $Val extends TreatmentConcernIndicationsResponse>
    implements $TreatmentConcernIndicationsResponseCopyWith<$Res> {
  _$TreatmentConcernIndicationsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentConcernIndicationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentConcernIndicationsResponseImplCopyWith<$Res>
    implements $TreatmentConcernIndicationsResponseCopyWith<$Res> {
  factory _$$TreatmentConcernIndicationsResponseImplCopyWith(
          _$TreatmentConcernIndicationsResponseImpl value,
          $Res Function(_$TreatmentConcernIndicationsResponseImpl) then) =
      __$$TreatmentConcernIndicationsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      int? createdAt,
      int? updatedAt,
      String? createdBy,
      String? updatedBy});
}

/// @nodoc
class __$$TreatmentConcernIndicationsResponseImplCopyWithImpl<$Res>
    extends _$TreatmentConcernIndicationsResponseCopyWithImpl<$Res,
        _$TreatmentConcernIndicationsResponseImpl>
    implements _$$TreatmentConcernIndicationsResponseImplCopyWith<$Res> {
  __$$TreatmentConcernIndicationsResponseImplCopyWithImpl(
      _$TreatmentConcernIndicationsResponseImpl _value,
      $Res Function(_$TreatmentConcernIndicationsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentConcernIndicationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
  }) {
    return _then(_$TreatmentConcernIndicationsResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentConcernIndicationsResponseImpl
    implements _TreatmentConcernIndicationsResponse {
  const _$TreatmentConcernIndicationsResponseImpl(
      {this.id,
      this.name,
      this.createdAt,
      this.updatedAt,
      this.createdBy,
      this.updatedBy});

  factory _$TreatmentConcernIndicationsResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$TreatmentConcernIndicationsResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  @override
  final int? createdAt;
  @override
  final int? updatedAt;
  @override
  final String? createdBy;
  @override
  final String? updatedBy;

  @override
  String toString() {
    return 'TreatmentConcernIndicationsResponse(id: $id, name: $name, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, updatedBy: $updatedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentConcernIndicationsResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, createdAt, updatedAt, createdBy, updatedBy);

  /// Create a copy of TreatmentConcernIndicationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentConcernIndicationsResponseImplCopyWith<
          _$TreatmentConcernIndicationsResponseImpl>
      get copyWith => __$$TreatmentConcernIndicationsResponseImplCopyWithImpl<
          _$TreatmentConcernIndicationsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentConcernIndicationsResponseImplToJson(
      this,
    );
  }
}

abstract class _TreatmentConcernIndicationsResponse
    implements TreatmentConcernIndicationsResponse {
  const factory _TreatmentConcernIndicationsResponse(
      {final String? id,
      final String? name,
      final int? createdAt,
      final int? updatedAt,
      final String? createdBy,
      final String? updatedBy}) = _$TreatmentConcernIndicationsResponseImpl;

  factory _TreatmentConcernIndicationsResponse.fromJson(
          Map<String, dynamic> json) =
      _$TreatmentConcernIndicationsResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String? get name;
  @override
  int? get createdAt;
  @override
  int? get updatedAt;
  @override
  String? get createdBy;
  @override
  String? get updatedBy;

  /// Create a copy of TreatmentConcernIndicationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentConcernIndicationsResponseImplCopyWith<
          _$TreatmentConcernIndicationsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TreatmentSurveyQuestionResponse _$TreatmentSurveyQuestionResponseFromJson(
    Map<String, dynamic> json) {
  return _TreatmentSurveyQuestionResponse.fromJson(json);
}

/// @nodoc
mixin _$TreatmentSurveyQuestionResponse {
  String? get id => throw _privateConstructorUsedError;
  String? get question => throw _privateConstructorUsedError;
  List<TreatmentSurveyAnswerResponse>? get answers =>
      throw _privateConstructorUsedError;
  int? get selectedAnswer => throw _privateConstructorUsedError;
  int? get questionOrder => throw _privateConstructorUsedError;

  /// Serializes this TreatmentSurveyQuestionResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentSurveyQuestionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentSurveyQuestionResponseCopyWith<TreatmentSurveyQuestionResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentSurveyQuestionResponseCopyWith<$Res> {
  factory $TreatmentSurveyQuestionResponseCopyWith(
          TreatmentSurveyQuestionResponse value,
          $Res Function(TreatmentSurveyQuestionResponse) then) =
      _$TreatmentSurveyQuestionResponseCopyWithImpl<$Res,
          TreatmentSurveyQuestionResponse>;
  @useResult
  $Res call(
      {String? id,
      String? question,
      List<TreatmentSurveyAnswerResponse>? answers,
      int? selectedAnswer,
      int? questionOrder});
}

/// @nodoc
class _$TreatmentSurveyQuestionResponseCopyWithImpl<$Res,
        $Val extends TreatmentSurveyQuestionResponse>
    implements $TreatmentSurveyQuestionResponseCopyWith<$Res> {
  _$TreatmentSurveyQuestionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentSurveyQuestionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? question = freezed,
    Object? answers = freezed,
    Object? selectedAnswer = freezed,
    Object? questionOrder = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      answers: freezed == answers
          ? _value.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<TreatmentSurveyAnswerResponse>?,
      selectedAnswer: freezed == selectedAnswer
          ? _value.selectedAnswer
          : selectedAnswer // ignore: cast_nullable_to_non_nullable
              as int?,
      questionOrder: freezed == questionOrder
          ? _value.questionOrder
          : questionOrder // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentSurveyQuestionResponseImplCopyWith<$Res>
    implements $TreatmentSurveyQuestionResponseCopyWith<$Res> {
  factory _$$TreatmentSurveyQuestionResponseImplCopyWith(
          _$TreatmentSurveyQuestionResponseImpl value,
          $Res Function(_$TreatmentSurveyQuestionResponseImpl) then) =
      __$$TreatmentSurveyQuestionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? question,
      List<TreatmentSurveyAnswerResponse>? answers,
      int? selectedAnswer,
      int? questionOrder});
}

/// @nodoc
class __$$TreatmentSurveyQuestionResponseImplCopyWithImpl<$Res>
    extends _$TreatmentSurveyQuestionResponseCopyWithImpl<$Res,
        _$TreatmentSurveyQuestionResponseImpl>
    implements _$$TreatmentSurveyQuestionResponseImplCopyWith<$Res> {
  __$$TreatmentSurveyQuestionResponseImplCopyWithImpl(
      _$TreatmentSurveyQuestionResponseImpl _value,
      $Res Function(_$TreatmentSurveyQuestionResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentSurveyQuestionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? question = freezed,
    Object? answers = freezed,
    Object? selectedAnswer = freezed,
    Object? questionOrder = freezed,
  }) {
    return _then(_$TreatmentSurveyQuestionResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      answers: freezed == answers
          ? _value._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<TreatmentSurveyAnswerResponse>?,
      selectedAnswer: freezed == selectedAnswer
          ? _value.selectedAnswer
          : selectedAnswer // ignore: cast_nullable_to_non_nullable
              as int?,
      questionOrder: freezed == questionOrder
          ? _value.questionOrder
          : questionOrder // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentSurveyQuestionResponseImpl
    implements _TreatmentSurveyQuestionResponse {
  const _$TreatmentSurveyQuestionResponseImpl(
      {this.id,
      this.question,
      final List<TreatmentSurveyAnswerResponse>? answers,
      this.selectedAnswer,
      this.questionOrder})
      : _answers = answers;

  factory _$TreatmentSurveyQuestionResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$TreatmentSurveyQuestionResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final String? question;
  final List<TreatmentSurveyAnswerResponse>? _answers;
  @override
  List<TreatmentSurveyAnswerResponse>? get answers {
    final value = _answers;
    if (value == null) return null;
    if (_answers is EqualUnmodifiableListView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? selectedAnswer;
  @override
  final int? questionOrder;

  @override
  String toString() {
    return 'TreatmentSurveyQuestionResponse(id: $id, question: $question, answers: $answers, selectedAnswer: $selectedAnswer, questionOrder: $questionOrder)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentSurveyQuestionResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.question, question) ||
                other.question == question) &&
            const DeepCollectionEquality().equals(other._answers, _answers) &&
            (identical(other.selectedAnswer, selectedAnswer) ||
                other.selectedAnswer == selectedAnswer) &&
            (identical(other.questionOrder, questionOrder) ||
                other.questionOrder == questionOrder));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      question,
      const DeepCollectionEquality().hash(_answers),
      selectedAnswer,
      questionOrder);

  /// Create a copy of TreatmentSurveyQuestionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentSurveyQuestionResponseImplCopyWith<
          _$TreatmentSurveyQuestionResponseImpl>
      get copyWith => __$$TreatmentSurveyQuestionResponseImplCopyWithImpl<
          _$TreatmentSurveyQuestionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentSurveyQuestionResponseImplToJson(
      this,
    );
  }
}

abstract class _TreatmentSurveyQuestionResponse
    implements TreatmentSurveyQuestionResponse {
  const factory _TreatmentSurveyQuestionResponse(
      {final String? id,
      final String? question,
      final List<TreatmentSurveyAnswerResponse>? answers,
      final int? selectedAnswer,
      final int? questionOrder}) = _$TreatmentSurveyQuestionResponseImpl;

  factory _TreatmentSurveyQuestionResponse.fromJson(Map<String, dynamic> json) =
      _$TreatmentSurveyQuestionResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String? get question;
  @override
  List<TreatmentSurveyAnswerResponse>? get answers;
  @override
  int? get selectedAnswer;
  @override
  int? get questionOrder;

  /// Create a copy of TreatmentSurveyQuestionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentSurveyQuestionResponseImplCopyWith<
          _$TreatmentSurveyQuestionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TreatmentSurveyAnswerResponse _$TreatmentSurveyAnswerResponseFromJson(
    Map<String, dynamic> json) {
  return _TreatmentSurveyAnswerResponse.fromJson(json);
}

/// @nodoc
mixin _$TreatmentSurveyAnswerResponse {
  String? get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;

  /// Serializes this TreatmentSurveyAnswerResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentSurveyAnswerResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentSurveyAnswerResponseCopyWith<TreatmentSurveyAnswerResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentSurveyAnswerResponseCopyWith<$Res> {
  factory $TreatmentSurveyAnswerResponseCopyWith(
          TreatmentSurveyAnswerResponse value,
          $Res Function(TreatmentSurveyAnswerResponse) then) =
      _$TreatmentSurveyAnswerResponseCopyWithImpl<$Res,
          TreatmentSurveyAnswerResponse>;
  @useResult
  $Res call({String? title, String? description, String? imageUrl});
}

/// @nodoc
class _$TreatmentSurveyAnswerResponseCopyWithImpl<$Res,
        $Val extends TreatmentSurveyAnswerResponse>
    implements $TreatmentSurveyAnswerResponseCopyWith<$Res> {
  _$TreatmentSurveyAnswerResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentSurveyAnswerResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? description = freezed,
    Object? imageUrl = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentSurveyAnswerResponseImplCopyWith<$Res>
    implements $TreatmentSurveyAnswerResponseCopyWith<$Res> {
  factory _$$TreatmentSurveyAnswerResponseImplCopyWith(
          _$TreatmentSurveyAnswerResponseImpl value,
          $Res Function(_$TreatmentSurveyAnswerResponseImpl) then) =
      __$$TreatmentSurveyAnswerResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? title, String? description, String? imageUrl});
}

/// @nodoc
class __$$TreatmentSurveyAnswerResponseImplCopyWithImpl<$Res>
    extends _$TreatmentSurveyAnswerResponseCopyWithImpl<$Res,
        _$TreatmentSurveyAnswerResponseImpl>
    implements _$$TreatmentSurveyAnswerResponseImplCopyWith<$Res> {
  __$$TreatmentSurveyAnswerResponseImplCopyWithImpl(
      _$TreatmentSurveyAnswerResponseImpl _value,
      $Res Function(_$TreatmentSurveyAnswerResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentSurveyAnswerResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? description = freezed,
    Object? imageUrl = freezed,
  }) {
    return _then(_$TreatmentSurveyAnswerResponseImpl(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentSurveyAnswerResponseImpl
    implements _TreatmentSurveyAnswerResponse {
  const _$TreatmentSurveyAnswerResponseImpl(
      {this.title, this.description, this.imageUrl});

  factory _$TreatmentSurveyAnswerResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$TreatmentSurveyAnswerResponseImplFromJson(json);

  @override
  final String? title;
  @override
  final String? description;
  @override
  final String? imageUrl;

  @override
  String toString() {
    return 'TreatmentSurveyAnswerResponse(title: $title, description: $description, imageUrl: $imageUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentSurveyAnswerResponseImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, description, imageUrl);

  /// Create a copy of TreatmentSurveyAnswerResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentSurveyAnswerResponseImplCopyWith<
          _$TreatmentSurveyAnswerResponseImpl>
      get copyWith => __$$TreatmentSurveyAnswerResponseImplCopyWithImpl<
          _$TreatmentSurveyAnswerResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentSurveyAnswerResponseImplToJson(
      this,
    );
  }
}

abstract class _TreatmentSurveyAnswerResponse
    implements TreatmentSurveyAnswerResponse {
  const factory _TreatmentSurveyAnswerResponse(
      {final String? title,
      final String? description,
      final String? imageUrl}) = _$TreatmentSurveyAnswerResponseImpl;

  factory _TreatmentSurveyAnswerResponse.fromJson(Map<String, dynamic> json) =
      _$TreatmentSurveyAnswerResponseImpl.fromJson;

  @override
  String? get title;
  @override
  String? get description;
  @override
  String? get imageUrl;

  /// Create a copy of TreatmentSurveyAnswerResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentSurveyAnswerResponseImplCopyWith<
          _$TreatmentSurveyAnswerResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
