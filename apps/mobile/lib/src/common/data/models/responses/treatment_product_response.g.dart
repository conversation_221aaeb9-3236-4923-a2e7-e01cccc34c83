// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'treatment_product_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TreatmentProductResponseImpl _$$TreatmentProductResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TreatmentProductResponseImpl(
      id: json['id'] as String?,
      itemCode: json['item_code'] as String?,
      name: json['name'] as String?,
      type: json['type'] as String?,
      description: json['description'] as String?,
      intervalId: json['interval_id'] as String?,
      price: (json['price'] as num?)?.toInt(),
      quantity: (json['quantity'] as num?)?.toInt(),
      isTopRecommendation: json['is_top_recommendation'] as bool?,
      createdAt: (json['created_at'] as num?)?.toInt(),
      updatedAt: (json['updated_at'] as num?)?.toInt(),
      category: (json['category'] as List<dynamic>?)
          ?.map((e) =>
              TreatmentCategoryResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
      interval: json['interval'] == null
          ? null
          : TreatmentIntervalResponse.fromJson(
              json['interval'] as Map<String, dynamic>),
      concern: (json['concern'] as List<dynamic>?)
          ?.map((e) =>
              TreatmentConcernResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
      surveyQuestions: (json['survey_questions'] as List<dynamic>?)
          ?.map((e) => TreatmentSurveyQuestionResponse.fromJson(
              e as Map<String, dynamic>))
          .toList(),
      mediaUrl: json['media_url'] as String?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      notes: json['notes'] as String?,
      durationTopRecommendation:
          (json['duration_top_recommendation'] as num?)?.toInt(),
      createdBy: json['created_by'] as String?,
      updatedBy: json['updated_by'] as String?,
    );

Map<String, dynamic> _$$TreatmentProductResponseImplToJson(
        _$TreatmentProductResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'item_code': instance.itemCode,
      'name': instance.name,
      'type': instance.type,
      'description': instance.description,
      'interval_id': instance.intervalId,
      'price': instance.price,
      'quantity': instance.quantity,
      'is_top_recommendation': instance.isTopRecommendation,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'category': instance.category,
      'interval': instance.interval,
      'concern': instance.concern,
      'survey_questions': instance.surveyQuestions,
      'media_url': instance.mediaUrl,
      'thumbnail_url': instance.thumbnailUrl,
      'notes': instance.notes,
      'duration_top_recommendation': instance.durationTopRecommendation,
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };

_$TreatmentCategoryResponseImpl _$$TreatmentCategoryResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TreatmentCategoryResponseImpl(
      id: json['id'] as String?,
      name: json['name'] as String?,
      createdAt: (json['created_at'] as num?)?.toInt(),
      updatedAt: (json['updated_at'] as num?)?.toInt(),
      createdBy: json['created_by'] as String?,
      updatedBy: json['updated_by'] as String?,
    );

Map<String, dynamic> _$$TreatmentCategoryResponseImplToJson(
        _$TreatmentCategoryResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };

_$TreatmentIntervalResponseImpl _$$TreatmentIntervalResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TreatmentIntervalResponseImpl(
      id: json['id'] as String?,
      days: (json['days'] as num?)?.toInt(),
      createdAt: (json['created_at'] as num?)?.toInt(),
      updatedAt: (json['updated_at'] as num?)?.toInt(),
      createdBy: json['created_by'] as String?,
      updatedBy: json['updated_by'] as String?,
    );

Map<String, dynamic> _$$TreatmentIntervalResponseImplToJson(
        _$TreatmentIntervalResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'days': instance.days,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };

_$TreatmentConcernResponseImpl _$$TreatmentConcernResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$TreatmentConcernResponseImpl(
      id: json['id'] as String?,
      name: json['name'] as String?,
      createdAt: (json['created_at'] as num?)?.toInt(),
      updatedAt: (json['updated_at'] as num?)?.toInt(),
      createdBy: json['created_by'] as String?,
      updatedBy: json['updated_by'] as String?,
      concernIndications: (json['concern_indications'] as List<dynamic>?)
          ?.map((e) => TreatmentConcernIndicationsResponse.fromJson(
              e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$TreatmentConcernResponseImplToJson(
        _$TreatmentConcernResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
      'concern_indications': instance.concernIndications,
    };

_$TreatmentConcernIndicationsResponseImpl
    _$$TreatmentConcernIndicationsResponseImplFromJson(
            Map<String, dynamic> json) =>
        _$TreatmentConcernIndicationsResponseImpl(
          id: json['id'] as String?,
          name: json['name'] as String?,
          createdAt: (json['created_at'] as num?)?.toInt(),
          updatedAt: (json['updated_at'] as num?)?.toInt(),
          createdBy: json['created_by'] as String?,
          updatedBy: json['updated_by'] as String?,
        );

Map<String, dynamic> _$$TreatmentConcernIndicationsResponseImplToJson(
        _$TreatmentConcernIndicationsResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };

_$TreatmentSurveyQuestionResponseImpl
    _$$TreatmentSurveyQuestionResponseImplFromJson(Map<String, dynamic> json) =>
        _$TreatmentSurveyQuestionResponseImpl(
          id: json['id'] as String?,
          question: json['question'] as String?,
          answers: (json['answers'] as List<dynamic>?)
              ?.map((e) => TreatmentSurveyAnswerResponse.fromJson(
                  e as Map<String, dynamic>))
              .toList(),
          selectedAnswer: (json['selected_answer'] as num?)?.toInt(),
          questionOrder: (json['question_order'] as num?)?.toInt(),
        );

Map<String, dynamic> _$$TreatmentSurveyQuestionResponseImplToJson(
        _$TreatmentSurveyQuestionResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'question': instance.question,
      'answers': instance.answers,
      'selected_answer': instance.selectedAnswer,
      'question_order': instance.questionOrder,
    };

_$TreatmentSurveyAnswerResponseImpl
    _$$TreatmentSurveyAnswerResponseImplFromJson(Map<String, dynamic> json) =>
        _$TreatmentSurveyAnswerResponseImpl(
          title: json['title'] as String?,
          description: json['description'] as String?,
          imageUrl: json['image_url'] as String?,
        );

Map<String, dynamic> _$$TreatmentSurveyAnswerResponseImplToJson(
        _$TreatmentSurveyAnswerResponseImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'image_url': instance.imageUrl,
    };
