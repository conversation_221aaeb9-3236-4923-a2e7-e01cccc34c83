import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze/skin_analyze.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze_recommendation/skin_analyze_recommendation.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/api_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinAnalyzeRepository {
  SkinAnalyzeRepository({
    required this.skinAnalyzeApi,
    required this.hiveService,
  });

  final SkinAnalyzeApi skinAnalyzeApi;
  final HiveService hiveService;

  static final Map<String, Future<Result<SkinAnalyzeRecommendation>>>
      _ongoingCalls = {};

  Future<Result<PaginatedDataResponse<SkinAnalyze>>> fetchAll(
    int page,
    int pageSize,
    String? name,
  ) async {
    try {
      final response = await skinAnalyzeApi.getAllSkinAnalyze(
        name: name,
        page: page,
        pageSize: pageSize,
      );
      final list = response.data.content;
      // TODO: refactor saved data model to use paginatedDataResponse
      hiveService.saveSkinAnalyze(list);
      return Result.success(response.data);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Future<Result<List<SkinAnalyze>>> fetchAllFromLocal() async {
    try {
      final box = hiveService.skinAnalyzeBox;
      final list = box.values as List<SkinAnalyze>;
      return Result.success(list);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Future<Result<SkinAnalyze?>> getDetail(String id) async {
    try {
      final response = await skinAnalyzeApi.getDetail(id: id);
      return Result.success(response.data);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Future<Result<String>> getSummary(String id) async {
    try {
      final response = await skinAnalyzeApi.getSummary(id: id);
      final summary = response.data;
      if (summary.summary != null) {
        hiveService.saveSkinAnalyzeSummary(summary.summary!);
      }
      return Result.success(summary.summary ?? '');
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Future<Result<String>> getSummaryFromLocal(String id) async {
    final localSummary = hiveService.skinAnalyzeSummary;
    if (localSummary == null) {
      return getSummary(id);
    }

    return Result.success(localSummary);
  }

  Future<Result<SkinAnalyzeRecommendation>> getRecommendation(String id) async {
    if (_ongoingCalls.containsKey(id)) {
      return _ongoingCalls[id]!;
    }

    final call = _makeRecommendationCallWithRetry(id);
    _ongoingCalls[id] = call;

    try {
      final result = await call;
      return result;
    } on Exception catch (e, _) {
      return call;
    } finally {
      await _ongoingCalls.remove(id);
    }
  }

  Future<Result<SkinAnalyzeRecommendation>> _makeRecommendationCallWithRetry(
    String id,
  ) async {
    const maxRetries = 2;
    const initialDelay = Duration(seconds: 2);

    for (var attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        final response = await skinAnalyzeApi.getRecommendation(id: id);
        final recommendation = response.data;
        await hiveService.saveSkinAnalyzeRecommendation(recommendation);
        return Result.success(recommendation);
      } on Exception catch (e, stackTrace) {
        if (attempt > maxRetries) {
          return Result.failure(
            NetworkExceptions.getException(e, stackTrace),
            stackTrace,
          );
        }

        final delay = Duration(seconds: initialDelay.inSeconds * (attempt - 1));
        await Future<void>.delayed(delay);
      }
    }

    return Result.failure(
      const NetworkExceptions.unexpectedError(),
      StackTrace.current,
    );
  }

  Future<Result<SkinAnalyzeRecommendation>> getRecommendationFromLocal(
    String id,
  ) async {
    final localRecommendation = hiveService.skinAnalyzeRecommendation;

    if (localRecommendation != null) {
      return Result.success(localRecommendation);
    }

    final result = await getRecommendation(id);

    return result;
  }

  Future<Result<List<String>>> getTopConcern(
    String id,
  ) async {
    try {
      final response = await skinAnalyzeApi.getTopConcern(id: id);
      return Result.success(response.data);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }
}

final skinAnalyzeRepositoryProvider = Provider(
  (ref) => SkinAnalyzeRepository(
    skinAnalyzeApi: ref.watch(skinAnalyzeApiProvider),
    hiveService: ref.watch(hiveServiceProvider),
  ),
);
