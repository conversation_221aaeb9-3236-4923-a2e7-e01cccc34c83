import 'package:euromedica_aizer/src/common/data/mappers/skin_survey_mapper.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/api/skin_survey_api.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/network_exceptions.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/result.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinSurveyRepository {
  SkinSurveyRepository(this._api);

  final SkinSurveyApi _api;

  Future<Result<List<SkinSurvey>>> getSurveyQuestions() async {
    try {
      final response = await _api.getSurveyQuestions(
        1,
        100,
      );
      final questions =
          SkinSurveyMapper.mapResponseListToDomain(response.data.content);
      
      return Result.success(questions);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }
}

final skinSurveyRepositoryProvider = Provider<SkinSurveyRepository>(
  (ref) => SkinSurveyRepository(ref.read(skinSurveyApiProvider)),
);
