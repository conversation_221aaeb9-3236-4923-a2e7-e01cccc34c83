import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/requests/login_request.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/common/domain/entities/user/user.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

class AuthRepository {
  AuthRepository(
    this.authApi, {
    required this.userApi,
    required this.hiveService,
  });

  final AuthApi authApi;
  final UserApi userApi;
  final HiveService hiveService;

  User? get currentUser => hiveService.currentUser;

  String? get userToken => hiveService.userToken;

  // TODO: Refactor this method
  // 1. Separate login and get user detail into different methods
  // 2. Get user detail on AuthService
  Future<Result<User>> login(
    String email,
    String password,
  ) async {
    final request = LoginRequest(
      email: email,
      password: password,
    );

    try {
      // Step 1: Attempt to login and get token
      final response = await authApi.login(request.toJson());
      final token = response.data.token;
      final refreshToken = response.data.refreshToken;

      print('Login successful: token: $token, refreshToken: $refreshToken');

      try {
        // Step 2: Decode JWT and get user ID
        final jwt = JwtDecoder.decode(token);
        final userId = jwt['id'] as String?;

        if (userId == null) {
          return Result.failure(
            const NetworkExceptions.defaultError(
              'Invalid token: User ID not found',
            ),
            StackTrace.current,
          );
        }

        final encodedId = Uri.encodeComponent(userId);

        // Step 3: Get user details
        try {
          final userDetailResponse = await userApi.getDetail(id: encodedId);

          final user = UserMapper.mapUserResponseToUser(
            userDetailResponse.data,
          );

          // Step 4: Save user data
          await hiveService.saveCurrentUser(user);
          await hiveService.saveUserToken(token);
          await hiveService.saveUserRefreshToken(refreshToken);

          return Result.success(user);
        } on Exception {
          rethrow;
        }
      } on Exception {
        rethrow;
      }
    } on Exception {
      rethrow;
    }
  }

  Future<void> logout() async => hiveService
    ..deleteCurrentUser()
    ..deleteUserToken();

  void dispose() => hiveService.close();
}

final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final auth = AuthRepository(
    ref.watch(authApiProvider),
    userApi: ref.watch(userApiProvider),
    hiveService: ref.watch(hiveServiceProvider),
  );
  ref.onDispose(auth.dispose);
  return auth;
});
