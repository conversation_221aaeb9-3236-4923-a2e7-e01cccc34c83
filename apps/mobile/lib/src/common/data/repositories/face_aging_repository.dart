import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/dio_client.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FaceAgingRepository {
  FaceAgingRepository({
    required FaceAgingApi faceAgingApi,
    required Dio imageDio,
    required HiveService hiveService,
  })  : _faceAgingApi = faceAgingApi,
        _imageDio = imageDio,
        _hiveService = hiveService;

  final FaceAgingApi _faceAgingApi;
  final Dio _imageDio;
  final HiveService _hiveService;

  Future<Result<JobFaceAgingResponse>> getFaceAgingConcern({
    required String skinAnalyzeId,
    required FaceAgingRequest request,
  }) async {
    try {
      final response = await _faceAgingApi.faceAgingConcern(
        id: skinAnalyzeId,
        body: request.toJson(),
      );
      return Result.success(response.data);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Future<Result<JobFaceAgingResponse>> getFaceAgingStatus({
    required String jobId,
  }) async {
    try {
      final response = await _faceAgingApi.faceAgingStatus(id: jobId);
      return Result.success(response.data);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Result<List<SkinConcern>?> getSkinAgingPredictionsFromLocal({
    required String skinAnalyzeId,
  }) {
    final faceAgings = _hiveService.faceAgings;
    return Result.success(faceAgings);
  }

  Future<void> saveSkinAgingPredictionsToLocal({
    required List<SkinConcern> skinConcerns,
  }) async {
    return _hiveService.saveFaceAgings(skinConcerns);
  }

  Future<Result<String>> getImageBytes({
    required String imageUrl,
  }) async {
    try {
      final response = await _imageDio.get<List<int>>(
        imageUrl,
        options: Options(
          headers: {},
          responseType: ResponseType.bytes,
          followRedirects: true,
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        final bytes = Uint8List.fromList(response.data!);
        final base64String = base64Encode(bytes);
        return Result.success(base64String);
      } else {
        return Result.failure(
          NetworkExceptions.getException(
            Exception('Failed to download image: ${response.statusCode}'),
            StackTrace.current,
          ),
          StackTrace.current,
        );
      }
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }
}

final faceAgingRepositoryProvider = Provider<FaceAgingRepository>(
  (ref) => FaceAgingRepository(
    faceAgingApi: ref.read(faceAgingApiProvider),
    imageDio: ref.read(imageDioClientProvider).dio,
    hiveService: ref.read(hiveServiceProvider),
  ),
);
