import 'package:euromedica_aizer/src/common/data/mappers/treatment_product_mapper.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/api/treatment_product_api.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/network_exceptions.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/result.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TreatmentProductRepository {
  TreatmentProductRepository(this._api);

  final TreatmentProductApi _api;

  Future<Result<List<TreatmentProduct>>> getTreatmentProducts() async {
    try {
      final response = await _api.getTreatmentProducts(
        pageSize: 100,
      );
      final treatments =
          TreatmentProductMapper.mapResponseListToDomain(response.data.content);
      return Result.success(treatments);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Future<Result<List<TreatmentProduct>>> getProducts() async {
    try {
      final response = await _api.getTreatmentProducts(
        pageSize: 100,
        types: ['product'],
      );
      final treatments =
          TreatmentProductMapper.mapResponseListToDomain(response.data.content);
      return Result.success(treatments);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }

  Future<Result<List<TreatmentProduct>>> searchTreatment(
    String searchText,
  ) async {
    try {
      final response = await _api.getTreatmentProducts(
        searchText: searchText,
        pageSize: 100,
      );
      final treatments =
          TreatmentProductMapper.mapResponseListToDomain(response.data.content);
      return Result.success(treatments);
    } on Exception catch (e, st) {
      return Result.failure(
        NetworkExceptions.getException(e, st),
        st,
      );
    }
  }
}

final treatmentProductRepositoryProvider = Provider<TreatmentProductRepository>(
  (ref) => TreatmentProductRepository(ref.read(treatmentProductApiProvider)),
);
