// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'skin_aging_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SkinAgingData {
  SkinAnalyze get skinAnalyze => throw _privateConstructorUsedError;
  List<SkinConcern> get skinConcerns => throw _privateConstructorUsedError;
  bool get isPregnantOrCancerTreatment => throw _privateConstructorUsedError;

  /// Create a copy of SkinAgingData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SkinAgingDataCopyWith<SkinAgingData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkinAgingDataCopyWith<$Res> {
  factory $SkinAgingDataCopyWith(
          SkinAgingData value, $Res Function(SkinAgingData) then) =
      _$SkinAgingDataCopyWithImpl<$Res, SkinAgingData>;
  @useResult
  $Res call(
      {SkinAnalyze skinAnalyze,
      List<SkinConcern> skinConcerns,
      bool isPregnantOrCancerTreatment});

  $SkinAnalyzeCopyWith<$Res> get skinAnalyze;
}

/// @nodoc
class _$SkinAgingDataCopyWithImpl<$Res, $Val extends SkinAgingData>
    implements $SkinAgingDataCopyWith<$Res> {
  _$SkinAgingDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SkinAgingData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinAnalyze = null,
    Object? skinConcerns = null,
    Object? isPregnantOrCancerTreatment = null,
  }) {
    return _then(_value.copyWith(
      skinAnalyze: null == skinAnalyze
          ? _value.skinAnalyze
          : skinAnalyze // ignore: cast_nullable_to_non_nullable
              as SkinAnalyze,
      skinConcerns: null == skinConcerns
          ? _value.skinConcerns
          : skinConcerns // ignore: cast_nullable_to_non_nullable
              as List<SkinConcern>,
      isPregnantOrCancerTreatment: null == isPregnantOrCancerTreatment
          ? _value.isPregnantOrCancerTreatment
          : isPregnantOrCancerTreatment // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of SkinAgingData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SkinAnalyzeCopyWith<$Res> get skinAnalyze {
    return $SkinAnalyzeCopyWith<$Res>(_value.skinAnalyze, (value) {
      return _then(_value.copyWith(skinAnalyze: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SkinAgingDataImplCopyWith<$Res>
    implements $SkinAgingDataCopyWith<$Res> {
  factory _$$SkinAgingDataImplCopyWith(
          _$SkinAgingDataImpl value, $Res Function(_$SkinAgingDataImpl) then) =
      __$$SkinAgingDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SkinAnalyze skinAnalyze,
      List<SkinConcern> skinConcerns,
      bool isPregnantOrCancerTreatment});

  @override
  $SkinAnalyzeCopyWith<$Res> get skinAnalyze;
}

/// @nodoc
class __$$SkinAgingDataImplCopyWithImpl<$Res>
    extends _$SkinAgingDataCopyWithImpl<$Res, _$SkinAgingDataImpl>
    implements _$$SkinAgingDataImplCopyWith<$Res> {
  __$$SkinAgingDataImplCopyWithImpl(
      _$SkinAgingDataImpl _value, $Res Function(_$SkinAgingDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of SkinAgingData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skinAnalyze = null,
    Object? skinConcerns = null,
    Object? isPregnantOrCancerTreatment = null,
  }) {
    return _then(_$SkinAgingDataImpl(
      skinAnalyze: null == skinAnalyze
          ? _value.skinAnalyze
          : skinAnalyze // ignore: cast_nullable_to_non_nullable
              as SkinAnalyze,
      skinConcerns: null == skinConcerns
          ? _value._skinConcerns
          : skinConcerns // ignore: cast_nullable_to_non_nullable
              as List<SkinConcern>,
      isPregnantOrCancerTreatment: null == isPregnantOrCancerTreatment
          ? _value.isPregnantOrCancerTreatment
          : isPregnantOrCancerTreatment // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SkinAgingDataImpl implements _SkinAgingData {
  const _$SkinAgingDataImpl(
      {required this.skinAnalyze,
      required final List<SkinConcern> skinConcerns,
      this.isPregnantOrCancerTreatment = false})
      : _skinConcerns = skinConcerns;

  @override
  final SkinAnalyze skinAnalyze;
  final List<SkinConcern> _skinConcerns;
  @override
  List<SkinConcern> get skinConcerns {
    if (_skinConcerns is EqualUnmodifiableListView) return _skinConcerns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_skinConcerns);
  }

  @override
  @JsonKey()
  final bool isPregnantOrCancerTreatment;

  @override
  String toString() {
    return 'SkinAgingData(skinAnalyze: $skinAnalyze, skinConcerns: $skinConcerns, isPregnantOrCancerTreatment: $isPregnantOrCancerTreatment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SkinAgingDataImpl &&
            (identical(other.skinAnalyze, skinAnalyze) ||
                other.skinAnalyze == skinAnalyze) &&
            const DeepCollectionEquality()
                .equals(other._skinConcerns, _skinConcerns) &&
            (identical(other.isPregnantOrCancerTreatment,
                    isPregnantOrCancerTreatment) ||
                other.isPregnantOrCancerTreatment ==
                    isPregnantOrCancerTreatment));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      skinAnalyze,
      const DeepCollectionEquality().hash(_skinConcerns),
      isPregnantOrCancerTreatment);

  /// Create a copy of SkinAgingData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SkinAgingDataImplCopyWith<_$SkinAgingDataImpl> get copyWith =>
      __$$SkinAgingDataImplCopyWithImpl<_$SkinAgingDataImpl>(this, _$identity);
}

abstract class _SkinAgingData implements SkinAgingData {
  const factory _SkinAgingData(
      {required final SkinAnalyze skinAnalyze,
      required final List<SkinConcern> skinConcerns,
      final bool isPregnantOrCancerTreatment}) = _$SkinAgingDataImpl;

  @override
  SkinAnalyze get skinAnalyze;
  @override
  List<SkinConcern> get skinConcerns;
  @override
  bool get isPregnantOrCancerTreatment;

  /// Create a copy of SkinAgingData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SkinAgingDataImplCopyWith<_$SkinAgingDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
