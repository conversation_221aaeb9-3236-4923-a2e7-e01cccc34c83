import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'skin_aging_data.freezed.dart';

@freezed
class SkinAgingData with _$SkinAgingData {
  const factory SkinAgingData({
    required SkinAnalyze skinAnalyze,
    required List<SkinConcern> skinConcerns,
    @Default(false) bool isPregnantOrCancerTreatment,
  }) = _SkinAgingData;
}
