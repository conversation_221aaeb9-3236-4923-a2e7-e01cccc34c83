// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'treatment_product.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TreatmentProduct _$TreatmentProductFromJson(Map<String, dynamic> json) {
  return _TreatmentProduct.fromJson(json);
}

/// @nodoc
mixin _$TreatmentProduct {
  String get id => throw _privateConstructorUsedError;
  String get itemCode => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<String> get categories => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  int get price => throw _privateConstructorUsedError;
  bool get isTopRecommendation => throw _privateConstructorUsedError;
  List<String> get solvedConcerns => throw _privateConstructorUsedError;
  String get mediaUrl => throw _privateConstructorUsedError;
  String get thumbnailUrl => throw _privateConstructorUsedError;

  /// Serializes this TreatmentProduct to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TreatmentProduct
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentProductCopyWith<TreatmentProduct> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentProductCopyWith<$Res> {
  factory $TreatmentProductCopyWith(
          TreatmentProduct value, $Res Function(TreatmentProduct) then) =
      _$TreatmentProductCopyWithImpl<$Res, TreatmentProduct>;
  @useResult
  $Res call(
      {String id,
      String itemCode,
      String name,
      String description,
      List<String> categories,
      int quantity,
      int price,
      bool isTopRecommendation,
      List<String> solvedConcerns,
      String mediaUrl,
      String thumbnailUrl});
}

/// @nodoc
class _$TreatmentProductCopyWithImpl<$Res, $Val extends TreatmentProduct>
    implements $TreatmentProductCopyWith<$Res> {
  _$TreatmentProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentProduct
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? itemCode = null,
    Object? name = null,
    Object? description = null,
    Object? categories = null,
    Object? quantity = null,
    Object? price = null,
    Object? isTopRecommendation = null,
    Object? solvedConcerns = null,
    Object? mediaUrl = null,
    Object? thumbnailUrl = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      itemCode: null == itemCode
          ? _value.itemCode
          : itemCode // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<String>,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int,
      isTopRecommendation: null == isTopRecommendation
          ? _value.isTopRecommendation
          : isTopRecommendation // ignore: cast_nullable_to_non_nullable
              as bool,
      solvedConcerns: null == solvedConcerns
          ? _value.solvedConcerns
          : solvedConcerns // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mediaUrl: null == mediaUrl
          ? _value.mediaUrl
          : mediaUrl // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentProductImplCopyWith<$Res>
    implements $TreatmentProductCopyWith<$Res> {
  factory _$$TreatmentProductImplCopyWith(_$TreatmentProductImpl value,
          $Res Function(_$TreatmentProductImpl) then) =
      __$$TreatmentProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String itemCode,
      String name,
      String description,
      List<String> categories,
      int quantity,
      int price,
      bool isTopRecommendation,
      List<String> solvedConcerns,
      String mediaUrl,
      String thumbnailUrl});
}

/// @nodoc
class __$$TreatmentProductImplCopyWithImpl<$Res>
    extends _$TreatmentProductCopyWithImpl<$Res, _$TreatmentProductImpl>
    implements _$$TreatmentProductImplCopyWith<$Res> {
  __$$TreatmentProductImplCopyWithImpl(_$TreatmentProductImpl _value,
      $Res Function(_$TreatmentProductImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentProduct
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? itemCode = null,
    Object? name = null,
    Object? description = null,
    Object? categories = null,
    Object? quantity = null,
    Object? price = null,
    Object? isTopRecommendation = null,
    Object? solvedConcerns = null,
    Object? mediaUrl = null,
    Object? thumbnailUrl = null,
  }) {
    return _then(_$TreatmentProductImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      itemCode: null == itemCode
          ? _value.itemCode
          : itemCode // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<String>,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int,
      isTopRecommendation: null == isTopRecommendation
          ? _value.isTopRecommendation
          : isTopRecommendation // ignore: cast_nullable_to_non_nullable
              as bool,
      solvedConcerns: null == solvedConcerns
          ? _value._solvedConcerns
          : solvedConcerns // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mediaUrl: null == mediaUrl
          ? _value.mediaUrl
          : mediaUrl // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$TreatmentProductImpl implements _TreatmentProduct {
  const _$TreatmentProductImpl(
      {required this.id,
      required this.itemCode,
      required this.name,
      required this.description,
      required final List<String> categories,
      required this.quantity,
      required this.price,
      required this.isTopRecommendation,
      required final List<String> solvedConcerns,
      required this.mediaUrl,
      required this.thumbnailUrl})
      : _categories = categories,
        _solvedConcerns = solvedConcerns;

  factory _$TreatmentProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$TreatmentProductImplFromJson(json);

  @override
  final String id;
  @override
  final String itemCode;
  @override
  final String name;
  @override
  final String description;
  final List<String> _categories;
  @override
  List<String> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  @override
  final int quantity;
  @override
  final int price;
  @override
  final bool isTopRecommendation;
  final List<String> _solvedConcerns;
  @override
  List<String> get solvedConcerns {
    if (_solvedConcerns is EqualUnmodifiableListView) return _solvedConcerns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_solvedConcerns);
  }

  @override
  final String mediaUrl;
  @override
  final String thumbnailUrl;

  @override
  String toString() {
    return 'TreatmentProduct(id: $id, itemCode: $itemCode, name: $name, description: $description, categories: $categories, quantity: $quantity, price: $price, isTopRecommendation: $isTopRecommendation, solvedConcerns: $solvedConcerns, mediaUrl: $mediaUrl, thumbnailUrl: $thumbnailUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentProductImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.itemCode, itemCode) ||
                other.itemCode == itemCode) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.isTopRecommendation, isTopRecommendation) ||
                other.isTopRecommendation == isTopRecommendation) &&
            const DeepCollectionEquality()
                .equals(other._solvedConcerns, _solvedConcerns) &&
            (identical(other.mediaUrl, mediaUrl) ||
                other.mediaUrl == mediaUrl) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      itemCode,
      name,
      description,
      const DeepCollectionEquality().hash(_categories),
      quantity,
      price,
      isTopRecommendation,
      const DeepCollectionEquality().hash(_solvedConcerns),
      mediaUrl,
      thumbnailUrl);

  /// Create a copy of TreatmentProduct
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentProductImplCopyWith<_$TreatmentProductImpl> get copyWith =>
      __$$TreatmentProductImplCopyWithImpl<_$TreatmentProductImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TreatmentProductImplToJson(
      this,
    );
  }
}

abstract class _TreatmentProduct implements TreatmentProduct {
  const factory _TreatmentProduct(
      {required final String id,
      required final String itemCode,
      required final String name,
      required final String description,
      required final List<String> categories,
      required final int quantity,
      required final int price,
      required final bool isTopRecommendation,
      required final List<String> solvedConcerns,
      required final String mediaUrl,
      required final String thumbnailUrl}) = _$TreatmentProductImpl;

  factory _TreatmentProduct.fromJson(Map<String, dynamic> json) =
      _$TreatmentProductImpl.fromJson;

  @override
  String get id;
  @override
  String get itemCode;
  @override
  String get name;
  @override
  String get description;
  @override
  List<String> get categories;
  @override
  int get quantity;
  @override
  int get price;
  @override
  bool get isTopRecommendation;
  @override
  List<String> get solvedConcerns;
  @override
  String get mediaUrl;
  @override
  String get thumbnailUrl;

  /// Create a copy of TreatmentProduct
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentProductImplCopyWith<_$TreatmentProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
