import 'package:freezed_annotation/freezed_annotation.dart';

part 'treatment_product.freezed.dart';
part 'treatment_product.g.dart';

@freezed
class TreatmentProduct with _$TreatmentProduct {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory TreatmentProduct({
    required String id,
    required String itemCode,
    required String name,
    required String description,
    required List<String> categories,
    required int quantity,
    required int price,
    required bool isTopRecommendation,
    required List<String> solvedConcerns,
    required String mediaUrl,
    required String thumbnailUrl,
  }) = _TreatmentProduct;

  factory TreatmentProduct.fromJson(Map<String, dynamic> json) =>
      _$TreatmentProductFromJson(json);
}
