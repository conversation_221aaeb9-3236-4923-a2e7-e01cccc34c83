// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'treatment_product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TreatmentProductImpl _$$TreatmentProductImplFromJson(
        Map<String, dynamic> json) =>
    _$TreatmentProductImpl(
      id: json['id'] as String,
      itemCode: json['item_code'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      categories: (json['categories'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      quantity: (json['quantity'] as num).toInt(),
      price: (json['price'] as num).toInt(),
      isTopRecommendation: json['is_top_recommendation'] as bool,
      solvedConcerns: (json['solved_concerns'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      mediaUrl: json['media_url'] as String,
      thumbnailUrl: json['thumbnail_url'] as String,
    );

Map<String, dynamic> _$$TreatmentProductImplToJson(
        _$TreatmentProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'item_code': instance.itemCode,
      'name': instance.name,
      'description': instance.description,
      'categories': instance.categories,
      'quantity': instance.quantity,
      'price': instance.price,
      'is_top_recommendation': instance.isTopRecommendation,
      'solved_concerns': instance.solvedConcerns,
      'media_url': instance.mediaUrl,
      'thumbnail_url': instance.thumbnailUrl,
    };
