import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze/skin_analyze.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze_recommendation/skin_analyze_recommendation.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/api_response.dart';
import 'package:euromedica_aizer/src/common/data/repositories/skin_analyze_repository.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/result.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinAnalyzeService {
  SkinAnalyzeService({
    required this.skinAnalyzeRepository,
  });

  final SkinAnalyzeRepository skinAnalyzeRepository;

  Future<Result<PaginatedDataResponse<SkinAnalyze>>> fetchAll(
    int page,
    int pageSize,
    String? name,
  ) async {
    return skinAnalyzeRepository.fetchAll(page, pageSize, name);
  }

  Future<Result<List<SkinAnalyze>>> fetchAllFromLocal() async {
    return skinAnalyzeRepository.fetchAllFromLocal();
  }

  Future<Result<SkinAnalyze?>> getDetail(String id) async {
    return skinAnalyzeRepository.getDetail(id);
  }

  Future<Result<String>> getSummary(String id) async {
    return skinAnalyzeRepository.getSummary(id);
  }

  Future<Result<String>> getSummaryFromLocal(String id) async {
    return skinAnalyzeRepository.getSummaryFromLocal(id);
  }

  Future<Result<SkinAnalyzeRecommendation>> getRecommendation(String id) async {
    return skinAnalyzeRepository.getRecommendation(id);
  }

  Future<Result<SkinAnalyzeRecommendation>> getRecommendationFromLocal(
    String id,
  ) async {
    return skinAnalyzeRepository.getRecommendationFromLocal(id);
  }

  Future<Result<List<String>>> getTopConcern(
    String id,
  ) async {
    return skinAnalyzeRepository.getTopConcern(id);
  }
}

final skinAnalyzeServiceProvider = Provider(
  (ref) => SkinAnalyzeService(
    skinAnalyzeRepository: ref.watch(skinAnalyzeRepositoryProvider),
  ),
);
