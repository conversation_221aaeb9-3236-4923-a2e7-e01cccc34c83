import 'package:euromedica_aizer/src/common/data/repositories/treatment_product_repository.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/result.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TreatmentProductService {
  TreatmentProductService(this._repository);

  final TreatmentProductRepository _repository;

  Future<Result<List<TreatmentProduct>>> getTreatmentProducts() {
    return _repository.getTreatmentProducts();
  }

  Future<Result<List<TreatmentProduct>>> searchTreatment(String searchText) {
    return _repository.searchTreatment(searchText);
  }

  Future<Result<List<TreatmentProduct>>> getProducts() {
    return _repository.getProducts();
  }
}

final treatmentProductServiceProvider = Provider<TreatmentProductService>(
  (ref) =>
      TreatmentProductService(ref.watch(treatmentProductRepositoryProvider)),
);
