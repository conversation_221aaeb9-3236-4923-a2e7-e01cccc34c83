import 'package:euromedica_aizer/src/common/data/repositories/auth_repository.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/result.dart';
import 'package:euromedica_aizer/src/common/domain/entities/user/user.dart';
import 'package:euromedica_aizer/src/common/domain/enums/auth_status.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AuthService extends ChangeNotifier {
  AuthService(this.authRepository);

  final AuthRepository authRepository;

  Future<Result<User>> login(String email, String password) {
    final result = authRepository.login(email, password);
    return result;
  }

  Future<void> logout() async {
    await authRepository.logout();
  }

  AuthStatus get authStatus {
    final userToken = authRepository.userToken;
    return userToken != null
        ? AuthStatus.authenticated
        : AuthStatus.unauthenticated;
  }

  User? get currentUser {
    return authRepository.currentUser;
  }
}

final authServiceProvider = ChangeNotifierProvider<AuthService>(
  (ref) => AuthService(ref.read(authRepositoryProvider)),
);
