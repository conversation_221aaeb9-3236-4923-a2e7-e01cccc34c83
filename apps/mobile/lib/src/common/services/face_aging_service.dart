import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/common/domain/entities/face_aging/face_aging.dart';
import 'package:euromedica_aizer/src/utils/extensions/result_extension.dart';
import 'package:euromedica_aizer/src/utils/extensions/string_extension.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FaceAgingService {
  FaceAgingService({
    required FaceAgingRepository faceAgingRepository,
  }) : _faceAgingRepository = faceAgingRepository;

  final FaceAgingRepository _faceAgingRepository;

  Future<Result<List<SkinConcern>>> getSkinAgingPredictions({
    required String skinAnalyzeId,
    required List<SkinConcern> concerns,
  }) async {
    final localSkinAgingPredictions = _getSkinAgingPredictionLocal(
      skinAnalyzeId: skinAnalyzeId,
      concerns: concerns,
    );

    if (localSkinAgingPredictions.data != null) {
      final agingData = localSkinAgingPredictions.data;

      if (agingData != null) {
        return Result.success(agingData);
      }
    }

    final remoteSkinAging = await getSkinAgingPredictionsRemote(
      skinAnalyzeId: skinAnalyzeId,
      concerns: concerns,
    );

    if (remoteSkinAging is Failure<List<SkinConcern>>) {
      return Result.failure(remoteSkinAging.error, remoteSkinAging.stackTrace);
    }

    final skinAging = (remoteSkinAging as Success<List<SkinConcern>>).data;

    return Result.success(skinAging);
  }

  Result<List<SkinConcern>?> _getSkinAgingPredictionLocal({
    required String skinAnalyzeId,
    required List<SkinConcern> concerns,
  }) {
    final result = _faceAgingRepository.getSkinAgingPredictionsFromLocal(
      skinAnalyzeId: skinAnalyzeId,
    );
    return result;
  }

  Future<Result<List<SkinConcern>>> getSkinAgingPredictionsRemote({
    required String skinAnalyzeId,
    required List<SkinConcern> concerns,
  }) async {
    try {
      final request = FaceAgingRequest(
        concerns: concerns
            .map(
              (e) => ConcernRequest(
                areas: e.faceAreas
                        ?.where(
                          (e) => !e.key.isNullOrEmpty && e.isSelected == true,
                        )
                        .map((e) => e.key)
                        .toList() ??
                    [],
                concern: e.key ?? '',
              ),
            )
            .toList(),
        isBeautify: true,
      );

      // 1. Trigger job to API
      final triggerResult = await _faceAgingRepository.getFaceAgingConcern(
        skinAnalyzeId: skinAnalyzeId,
        request: request,
      );

      String? jobId;

      final triggerSuccess = await triggerResult.when(
        success: (JobFaceAgingResponse data) async {
          jobId = data.id;
          return true;
        },
        failure: (error, stackTrace) {
          throw Exception(error);
        },
      );

      if (!triggerSuccess || jobId == null) {
        throw Exception('Failed to trigger face aging job');
      }

      // 2. Poll status every 10 seconds
      const maxAttempts = 6;
      var attempts = 0;

      while (attempts < maxAttempts) {
        await Future<void>.delayed(const Duration(seconds: 10));
        attempts++;

        final statusResult = await _faceAgingRepository.getFaceAgingStatus(
          jobId: jobId!,
        );

        final statusData = await statusResult.when(
          success: (JobFaceAgingResponse data) async {
            if (data.status == 'completed' && data.response != null) {
              // Status completed, return data
              return data.response!.generatedImages;
            } else if (data.status == 'failed') {
              throw Exception('Face aging job failed');
            }
            // Status masih processing, continue polling
            return null;
          },
          failure: (error, stackTrace) {
            throw Exception(error);
          },
        );

        // If data is already available (status completed)
        if (statusData != null) {
          final updatedConcerns = <SkinConcern>[];

          final beautifyImageUrl = statusData.firstWhere(
            (FaceAging faceAging) => faceAging.concern == 'beautify',
          );
          final beautifyImageBase64 = await _getImageBytes(
            imageUrl: beautifyImageUrl.generatedImageUrl,
          );

          for (var index = 0; index < concerns.length; index++) {
            final concern = concerns[index];
            final faceAging = statusData
                .firstWhere((faceAging) => faceAging.concern == concern.key);
            final selectedAreaImageBase64 = await _getImageBytes(
              imageUrl: faceAging.selectedAreaUrl,
            );
            final faceAgingImageBase64 = await _getImageBytes(
              imageUrl: faceAging.generatedImageUrl,
            );
            final updatedConcern = concern.copyWith(
              faceAgingImageBase64: faceAgingImageBase64,
              selectedAreaImageBase64: selectedAreaImageBase64,
              faceBeautifyingImageBase64: beautifyImageBase64,
            );
            updatedConcerns.add(updatedConcern);
          }

          // Save the updated concerns to local storage
          await _faceAgingRepository.saveSkinAgingPredictionsToLocal(
            skinConcerns: updatedConcerns,
          );

          return Result.success(updatedConcerns);
        }
      }

      // Timeout setelah 60 detik (6 attempts x 10 detik)
      throw Exception(
        'Face aging job timeout after ${maxAttempts * 10} seconds',
      );
    } on Exception catch (error, stackTrace) {
      return Result.failure(
        NetworkExceptions.defaultError(error.toString()),
        stackTrace,
      );
    }
  }

  Future<Result<List<SkinConcern>>> regenerateSkinAgingPredictions({
    required String skinAnalyzeId,
    required List<SkinConcern> concerns,
  }) async {
    final localSkinAgingPredictions = _getSkinAgingPredictionLocal(
      skinAnalyzeId: skinAnalyzeId,
      concerns: concerns,
    );

    final remoteSkinAging = await getSkinAgingPredictionsRemote(
      skinAnalyzeId: skinAnalyzeId,
      concerns: concerns,
    );

    if (remoteSkinAging is Failure<List<SkinConcern>>) {
      return Result.failure(remoteSkinAging.error, remoteSkinAging.stackTrace);
    }

    final skinAgingRemote =
        (remoteSkinAging as Success<List<SkinConcern>>).data;
    final agingDataLocal =
        List<SkinConcern>.from(localSkinAgingPredictions.data ?? []);

    // Update agingDataLocal with values from skinAging by key
    for (final remoteConcern in skinAgingRemote) {
      final index = agingDataLocal.indexWhere(
        (c) => c.key == remoteConcern.key,
      );
      if (index != -1) {
        agingDataLocal[index] = remoteConcern;
      }
    }

    await _faceAgingRepository.saveSkinAgingPredictionsToLocal(
      skinConcerns: agingDataLocal,
    );

    return Result.success(agingDataLocal);
  }

  Future<String> _getImageBytes({
    required String imageUrl,
  }) async {
    try {
      final response = await _faceAgingRepository.getImageBytes(
        imageUrl: imageUrl,
      );
      return response.when(
        success: (data) => data,
        failure: (error, _) {
          throw Exception(error);
        },
      );
    } on Exception catch (error) {
      throw Exception(error);
    }
  }
}

final faceAgingServiceProvider = Provider<FaceAgingService>(
  (ref) => FaceAgingService(
    faceAgingRepository: ref.read(faceAgingRepositoryProvider),
  ),
);
