import 'dart:developer';

class RetryMechanism {
  static Future<T> withRetry<T>(
    Future<T> Function() action, {
    required Future<T> Function(Exception, StackTrace) onError,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 2),
    bool useExponentialBackoff = true,
  }) async {
    var attempt = 0;
    var currentDelay = initialDelay;

    while (true) {
      attempt++;
      try {
        return await action();
      } on Exception catch (e, stackTrace) {
        if (attempt >= maxRetries) {
          log(
            'Max retries reached. Giving up.',
            error: e,
            stackTrace: stackTrace,
          );
          return onError.call(Exception(e.toString()), stackTrace);
        }
        log(
          'Error occurred on attempt $attempt: $e',
          error: e,
          stackTrace: stackTrace,
        );
        log('Retrying in ${currentDelay.inSeconds} seconds...');
        await Future<void>.delayed(currentDelay);
        if (useExponentialBackoff) {
          currentDelay = Duration(seconds: currentDelay.inSeconds * 2);
        }
      }
    }
  }
}
