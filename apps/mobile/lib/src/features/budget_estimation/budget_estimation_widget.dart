import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/domain/enums/budget_estimation.dart';
import 'package:euromedica_aizer/src/features/budget_estimation/budget_estimation_controller.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BudgetEstimationWidget extends ConsumerWidget {
  const BudgetEstimationWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(budgetEstimationControllerProvider);
    final controller = ref.read(budgetEstimationControllerProvider.notifier);
    return Row(
      children: BudgetEstimation.values
          .map(
            (e) => InkWell(
              onTap: () => controller.setBudgetEstimation(e),
              child: Padding(
                padding: EdgeInsets.all(AppSizes.w8),
                child: Row(
                  children: List.generate(
                    e.value,
                    (index) => Text(
                      state.budgetEstimation == e ? '●' : '○',
                      style: context.p?.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),
            ),
          )
          .toList(),
    );
  }
}
