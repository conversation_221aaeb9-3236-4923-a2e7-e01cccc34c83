import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/components/error_state.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_aging_data/skin_aging_data.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_analysis_data/skin_analysis_data.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/budget_estimation/budget_estimation_widget.dart';
import 'package:euromedica_aizer/src/features/skin_analysis/skin_analysis_result_controller.dart';
import 'package:euromedica_aizer/src/features/skin_analysis/skin_analysis_result_state.dart';
import 'package:euromedica_aizer/src/features/skin_analysis/widgets/skin_analysis_result_view.dart';
import 'package:euromedica_aizer/src/features/skin_analysis/widgets/skin_analysis_result_widget.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinAnalysisResultScreen extends ConsumerWidget {
  const SkinAnalysisResultScreen({
    required this.skinAnalysisData,
    super.key,
  });

  static const Key skinAnalysisResultSummaryKey = Key(
    'skin_analysis_result_summary',
  );
  static final GlobalKey<SkinAnalysisResultWidgetState> _skinAnalysisResultKey =
      GlobalKey();

  final SkinAnalysisData skinAnalysisData;

  bool get _isPregnantOrCancerTreatment =>
      skinAnalysisData.isPregnantOrCancerTreatment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(
      skinAnalysisResultControllerProvider(skinAnalysisData).notifier,
    );
    final state = ref.watch(
      skinAnalysisResultControllerProvider(skinAnalysisData),
    );

    final hasError = state.value.hasError ||
        state.recommendationValue.hasError ||
        state.faceAgingValue.hasError;

    return CommonScaffold(
      appBar: const CommonTopBar(
        title: 'Step 2',
      ),
      body: state.value.when(
        data: (data) => SkinAnalysisResultView(
          skinAnalyze: data,
          skinAnalysisResultKey: _skinAnalysisResultKey,
          selectedMachine: state.selectedMachine,
          summaryAnimateTarget: state.summaryAnimateTarget,
          summary: state.summary,
          parameterSkinEvaluation: controller.getSkinCategory()!,
          parameterSkinEvaluationList: state.parameterSkinEvaluation ?? [],
          onSummaryAnimateTargetChanged: controller.setSummaryAnimateTarget,
        ),
        error: (error, stackTrace) {
          return CommonErrorState(
            errorMessage: 'Failed to load skin analysis result',
            onRetry: () {
              controller
                ..getSkinAnalysisResult()
                ..getSummary();
            },
          );
        },
        loading: () {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      ),
      bottomNavBar: state.value.when(
        data: (data) {
          final skinAgingData = SkinAgingData(
            skinAnalyze: state.value.value!,
            skinConcerns: skinAnalysisData.skinConcerns,
            isPregnantOrCancerTreatment: _isPregnantOrCancerTreatment,
          );
          final nextDisabled = state.faceAgingValue.isLoading ||
              state.recommendationValue.isLoading;
          return GlassBottomNavBar(
            content: Row(
              children: [
                const BudgetEstimationWidget(),
                const Spacer(),
                CommonButton(
                  onPressed: () => context.pop(),
                  variant: ButtonVariant.outlined,
                  buttonStyle: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                  ),
                  child: const Icon(Icons.arrow_back),
                ),
                Gap.w16,
                if (hasError)
                  CommonButton(
                    onPressed: () => _onTapRetry(
                      context: context,
                      controller: controller,
                      state: state,
                    ),
                    isLoading: nextDisabled,
                    child: const Text('Retry'),
                  )
                else
                  CommonButton(
                    onPressed: () => _onTapNext(
                      context,
                      controller,
                      skinAgingData,
                    ),
                    isLoading: nextDisabled,
                    child: const Icon(Icons.arrow_forward),
                  ),
              ],
            ),
          );
        },
        error: (error, stackTrace) => const SizedBox.shrink(),
        loading: () => const SizedBox.shrink(),
      ),
    );
  }

  Future<void> _onTapRetry({
    required BuildContext context,
    required SkinAnalysisResultController controller,
    required SkinAnalysisResultState state,
  }) async {
    final results = await Future.wait([
      if (state.faceAgingValue.hasError)
        controller.getSkinAgingPredictions(skinAnalysisData),
      if (state.recommendationValue.hasError) controller.getRecommendation(),
    ]);

    final isSucceed = results.every((result) => result == true);
    if (!isSucceed && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Failed to get data. Please check connection and try again',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onTapNext(
    BuildContext context,
    SkinAnalysisResultController controller,
    SkinAgingData skinAgingData,
  ) {
    context.pushNamed(
      Routes.skinAging.name,
      extra: skinAgingData,
    );
  }
}
