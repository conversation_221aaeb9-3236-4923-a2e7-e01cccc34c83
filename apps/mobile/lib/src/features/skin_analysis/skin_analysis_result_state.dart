import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze_recommendation/skin_analyze_recommendation.dart';
import 'package:euromedica_aizer/src/common/domain/entities/parameter_skin_evaluation/parameter_skin_evaluation.dart';
import 'package:euromedica_aizer/src/common/domain/enums/skin_analysis_machine.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'skin_analysis_result_state.freezed.dart';

@freezed
class SkinAnalysisResultState with _$SkinAnalysisResultState {
  const factory SkinAnalysisResultState({
    @Default(SkinAnalysisMachine.m9) SkinAnalysisMachine selectedMachine,
    @Default(AsyncValue.data(null)) AsyncValue<SkinAnalyze?> value,
    @Default(AsyncValue.data(null)) AsyncValue<String?> summary,
    @Default(0) double summaryAnimateTarget,
    @Default(null) List<ParameterSkinEvaluation>? parameterSkinEvaluation,
    @Default(AsyncData(null))
    AsyncValue<SkinAnalyzeRecommendation?> recommendationValue,
    @Default(AsyncData(null)) AsyncValue<List<SkinConcern>?> faceAgingValue,
  }) = _SkinAnalysisResultState;
}
