// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'skin_analysis_result_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SkinAnalysisResultState {
  SkinAnalysisMachine get selectedMachine => throw _privateConstructorUsedError;
  AsyncValue<SkinAnalyze?> get value => throw _privateConstructorUsedError;
  AsyncValue<String?> get summary => throw _privateConstructorUsedError;
  double get summaryAnimateTarget => throw _privateConstructorUsedError;
  List<ParameterSkinEvaluation>? get parameterSkinEvaluation =>
      throw _privateConstructorUsedError;
  AsyncValue<SkinAnalyzeRecommendation?> get recommendationValue =>
      throw _privateConstructorUsedError;
  AsyncValue<List<SkinConcern>?> get faceAgingValue =>
      throw _privateConstructorUsedError;

  /// Create a copy of SkinAnalysisResultState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SkinAnalysisResultStateCopyWith<SkinAnalysisResultState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkinAnalysisResultStateCopyWith<$Res> {
  factory $SkinAnalysisResultStateCopyWith(SkinAnalysisResultState value,
          $Res Function(SkinAnalysisResultState) then) =
      _$SkinAnalysisResultStateCopyWithImpl<$Res, SkinAnalysisResultState>;
  @useResult
  $Res call(
      {SkinAnalysisMachine selectedMachine,
      AsyncValue<SkinAnalyze?> value,
      AsyncValue<String?> summary,
      double summaryAnimateTarget,
      List<ParameterSkinEvaluation>? parameterSkinEvaluation,
      AsyncValue<SkinAnalyzeRecommendation?> recommendationValue,
      AsyncValue<List<SkinConcern>?> faceAgingValue});
}

/// @nodoc
class _$SkinAnalysisResultStateCopyWithImpl<$Res,
        $Val extends SkinAnalysisResultState>
    implements $SkinAnalysisResultStateCopyWith<$Res> {
  _$SkinAnalysisResultStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SkinAnalysisResultState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedMachine = null,
    Object? value = null,
    Object? summary = null,
    Object? summaryAnimateTarget = null,
    Object? parameterSkinEvaluation = freezed,
    Object? recommendationValue = null,
    Object? faceAgingValue = null,
  }) {
    return _then(_value.copyWith(
      selectedMachine: null == selectedMachine
          ? _value.selectedMachine
          : selectedMachine // ignore: cast_nullable_to_non_nullable
              as SkinAnalysisMachine,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as AsyncValue<SkinAnalyze?>,
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as AsyncValue<String?>,
      summaryAnimateTarget: null == summaryAnimateTarget
          ? _value.summaryAnimateTarget
          : summaryAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      parameterSkinEvaluation: freezed == parameterSkinEvaluation
          ? _value.parameterSkinEvaluation
          : parameterSkinEvaluation // ignore: cast_nullable_to_non_nullable
              as List<ParameterSkinEvaluation>?,
      recommendationValue: null == recommendationValue
          ? _value.recommendationValue
          : recommendationValue // ignore: cast_nullable_to_non_nullable
              as AsyncValue<SkinAnalyzeRecommendation?>,
      faceAgingValue: null == faceAgingValue
          ? _value.faceAgingValue
          : faceAgingValue // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<SkinConcern>?>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SkinAnalysisResultStateImplCopyWith<$Res>
    implements $SkinAnalysisResultStateCopyWith<$Res> {
  factory _$$SkinAnalysisResultStateImplCopyWith(
          _$SkinAnalysisResultStateImpl value,
          $Res Function(_$SkinAnalysisResultStateImpl) then) =
      __$$SkinAnalysisResultStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SkinAnalysisMachine selectedMachine,
      AsyncValue<SkinAnalyze?> value,
      AsyncValue<String?> summary,
      double summaryAnimateTarget,
      List<ParameterSkinEvaluation>? parameterSkinEvaluation,
      AsyncValue<SkinAnalyzeRecommendation?> recommendationValue,
      AsyncValue<List<SkinConcern>?> faceAgingValue});
}

/// @nodoc
class __$$SkinAnalysisResultStateImplCopyWithImpl<$Res>
    extends _$SkinAnalysisResultStateCopyWithImpl<$Res,
        _$SkinAnalysisResultStateImpl>
    implements _$$SkinAnalysisResultStateImplCopyWith<$Res> {
  __$$SkinAnalysisResultStateImplCopyWithImpl(
      _$SkinAnalysisResultStateImpl _value,
      $Res Function(_$SkinAnalysisResultStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SkinAnalysisResultState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedMachine = null,
    Object? value = null,
    Object? summary = null,
    Object? summaryAnimateTarget = null,
    Object? parameterSkinEvaluation = freezed,
    Object? recommendationValue = null,
    Object? faceAgingValue = null,
  }) {
    return _then(_$SkinAnalysisResultStateImpl(
      selectedMachine: null == selectedMachine
          ? _value.selectedMachine
          : selectedMachine // ignore: cast_nullable_to_non_nullable
              as SkinAnalysisMachine,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as AsyncValue<SkinAnalyze?>,
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as AsyncValue<String?>,
      summaryAnimateTarget: null == summaryAnimateTarget
          ? _value.summaryAnimateTarget
          : summaryAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      parameterSkinEvaluation: freezed == parameterSkinEvaluation
          ? _value._parameterSkinEvaluation
          : parameterSkinEvaluation // ignore: cast_nullable_to_non_nullable
              as List<ParameterSkinEvaluation>?,
      recommendationValue: null == recommendationValue
          ? _value.recommendationValue
          : recommendationValue // ignore: cast_nullable_to_non_nullable
              as AsyncValue<SkinAnalyzeRecommendation?>,
      faceAgingValue: null == faceAgingValue
          ? _value.faceAgingValue
          : faceAgingValue // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<SkinConcern>?>,
    ));
  }
}

/// @nodoc

class _$SkinAnalysisResultStateImpl implements _SkinAnalysisResultState {
  const _$SkinAnalysisResultStateImpl(
      {this.selectedMachine = SkinAnalysisMachine.m9,
      this.value = const AsyncValue.data(null),
      this.summary = const AsyncValue.data(null),
      this.summaryAnimateTarget = 0,
      final List<ParameterSkinEvaluation>? parameterSkinEvaluation = null,
      this.recommendationValue = const AsyncData(null),
      this.faceAgingValue = const AsyncData(null)})
      : _parameterSkinEvaluation = parameterSkinEvaluation;

  @override
  @JsonKey()
  final SkinAnalysisMachine selectedMachine;
  @override
  @JsonKey()
  final AsyncValue<SkinAnalyze?> value;
  @override
  @JsonKey()
  final AsyncValue<String?> summary;
  @override
  @JsonKey()
  final double summaryAnimateTarget;
  final List<ParameterSkinEvaluation>? _parameterSkinEvaluation;
  @override
  @JsonKey()
  List<ParameterSkinEvaluation>? get parameterSkinEvaluation {
    final value = _parameterSkinEvaluation;
    if (value == null) return null;
    if (_parameterSkinEvaluation is EqualUnmodifiableListView)
      return _parameterSkinEvaluation;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final AsyncValue<SkinAnalyzeRecommendation?> recommendationValue;
  @override
  @JsonKey()
  final AsyncValue<List<SkinConcern>?> faceAgingValue;

  @override
  String toString() {
    return 'SkinAnalysisResultState(selectedMachine: $selectedMachine, value: $value, summary: $summary, summaryAnimateTarget: $summaryAnimateTarget, parameterSkinEvaluation: $parameterSkinEvaluation, recommendationValue: $recommendationValue, faceAgingValue: $faceAgingValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SkinAnalysisResultStateImpl &&
            (identical(other.selectedMachine, selectedMachine) ||
                other.selectedMachine == selectedMachine) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.summary, summary) || other.summary == summary) &&
            (identical(other.summaryAnimateTarget, summaryAnimateTarget) ||
                other.summaryAnimateTarget == summaryAnimateTarget) &&
            const DeepCollectionEquality().equals(
                other._parameterSkinEvaluation, _parameterSkinEvaluation) &&
            (identical(other.recommendationValue, recommendationValue) ||
                other.recommendationValue == recommendationValue) &&
            (identical(other.faceAgingValue, faceAgingValue) ||
                other.faceAgingValue == faceAgingValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedMachine,
      value,
      summary,
      summaryAnimateTarget,
      const DeepCollectionEquality().hash(_parameterSkinEvaluation),
      recommendationValue,
      faceAgingValue);

  /// Create a copy of SkinAnalysisResultState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SkinAnalysisResultStateImplCopyWith<_$SkinAnalysisResultStateImpl>
      get copyWith => __$$SkinAnalysisResultStateImplCopyWithImpl<
          _$SkinAnalysisResultStateImpl>(this, _$identity);
}

abstract class _SkinAnalysisResultState implements SkinAnalysisResultState {
  const factory _SkinAnalysisResultState(
          {final SkinAnalysisMachine selectedMachine,
          final AsyncValue<SkinAnalyze?> value,
          final AsyncValue<String?> summary,
          final double summaryAnimateTarget,
          final List<ParameterSkinEvaluation>? parameterSkinEvaluation,
          final AsyncValue<SkinAnalyzeRecommendation?> recommendationValue,
          final AsyncValue<List<SkinConcern>?> faceAgingValue}) =
      _$SkinAnalysisResultStateImpl;

  @override
  SkinAnalysisMachine get selectedMachine;
  @override
  AsyncValue<SkinAnalyze?> get value;
  @override
  AsyncValue<String?> get summary;
  @override
  double get summaryAnimateTarget;
  @override
  List<ParameterSkinEvaluation>? get parameterSkinEvaluation;
  @override
  AsyncValue<SkinAnalyzeRecommendation?> get recommendationValue;
  @override
  AsyncValue<List<SkinConcern>?> get faceAgingValue;

  /// Create a copy of SkinAnalysisResultState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SkinAnalysisResultStateImplCopyWith<_$SkinAnalysisResultStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
