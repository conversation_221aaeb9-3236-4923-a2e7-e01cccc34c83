import 'dart:async';

import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/domain/entities/parameter_skin_evaluation/parameter_skin_evaluation.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_analysis_data/skin_analysis_data.dart';
import 'package:euromedica_aizer/src/common/domain/enums/skin_analysis_machine.dart';
import 'package:euromedica_aizer/src/common/services/face_aging_service.dart';
import 'package:euromedica_aizer/src/common/services/parameter_skin_evaluation_service.dart';
import 'package:euromedica_aizer/src/common/services/skin_analyze_service.dart';
import 'package:euromedica_aizer/src/features/skin_analysis/skin_analysis_result_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinAnalysisResultController
    extends StateNotifier<SkinAnalysisResultState> {
  SkinAnalysisResultController({
    required this.ref,
    required this.skinAnalyzeService,
    required this.data,
    required this.parameterSkinEvaluationService,
    required this.faceAgingService,
  }) : super(const SkinAnalysisResultState()) {
    _init();
  }

  final Ref ref;
  final SkinAnalyzeService skinAnalyzeService;
  final SkinAnalysisData data;
  final ParameterSkinEvaluationService parameterSkinEvaluationService;
  final FaceAgingService faceAgingService;

  String get skinAnalyzeId => data.skinAnalyzeId;

  void _init() {
    getSkinAnalysisResult();
    getSummary();
  }

  Future<void> getSkinAnalysisResult() async {
    state = state.copyWith(value: const AsyncLoading());
    final result = await skinAnalyzeService.getDetail(skinAnalyzeId);
    final parameterSkinEvaluation = await getParameterSkinEvaluation();
    result.when(
      success: (data) {
        state = state.copyWith(
          value: AsyncData(data),
          parameterSkinEvaluation: parameterSkinEvaluation,
        );
      },
      failure: (error, stackTrace) {
        state = state.copyWith(value: AsyncError(error, stackTrace));
      },
    );
    unawaited(() {
      getSkinAgingPredictions(data);
      getRecommendation();
    }());
  }

  Future<List<ParameterSkinEvaluation>> getParameterSkinEvaluation() async {
    final result = await parameterSkinEvaluationService.getAll();
    return result.when(
      success: (data) {
        return data;
      },
      failure: (error, stackTrace) {
        return [];
      },
    );
  }

  Future<void> getSummary() async {
    state = state.copyWith(summary: const AsyncLoading());
    final result = await skinAnalyzeService.getSummaryFromLocal(skinAnalyzeId);
    result.when(
      success: (data) {
        state = state.copyWith(summary: AsyncData(data));
      },
      failure: (error, stackTrace) {
        state = state.copyWith(summary: AsyncError(error, stackTrace));
      },
    );
  }

  ParameterSkinEvaluation? getSkinCategory() {
    if (state.value.hasError || state.value.value == null) return null;
    final evaluationRate = state.value.value!.evaluationRate;
    if (evaluationRate == null) return null;

    // sort parameter skin evaluation by lower point
    final sortedSkinCategory = List<ParameterSkinEvaluation>.from(
      state.parameterSkinEvaluation ?? [],
    )..sort((a, b) => a.lowerPoint.compareTo(b.lowerPoint));

    // find the skin category that matches the evaluation rate
    final skinCategory = sortedSkinCategory.firstWhere(
      (element) =>
          element.lowerPoint <= evaluationRate &&
          element.upperPoint >= evaluationRate,
    );

    // set the color of the skin category
    final colors = [
      LightColors.errorColor.toARGB32(),
      LightColors.neutralColor.shade500.toARGB32(),
      LightColors.primaryColor.toARGB32(),
    ];

    return skinCategory.copyWith(
      colorArgb: colors[sortedSkinCategory.indexOf(skinCategory)],
    );
  }

  Future<bool> getRecommendation() async {
    state = state.copyWith(recommendationValue: const AsyncLoading());
    final result = await skinAnalyzeService.getRecommendation(skinAnalyzeId);
    return result.when(
      success: (data) {
        state = state.copyWith(recommendationValue: AsyncData(data));
        return true;
      },
      failure: (error, stackTrace) {
        state = state.copyWith(
          recommendationValue: AsyncError(error, stackTrace),
        );
        return false;
      },
    );
  }

  Future<bool> getSkinAgingPredictions(SkinAnalysisData data) async {
    state = state.copyWith(faceAgingValue: const AsyncLoading());
    final result = await faceAgingService.getSkinAgingPredictionsRemote(
      skinAnalyzeId: data.skinAnalyzeId,
      concerns: data.skinConcerns,
    );
    return result.when(
      success: (data) {
        state = state.copyWith(faceAgingValue: AsyncData(data));
        return true;
      },
      failure: (error, stackTrace) {
        state = state.copyWith(faceAgingValue: AsyncError(error, stackTrace));
        return false;
      },
    );
  }

  void selectMachine(SkinAnalysisMachine machine) {
    state = state.copyWith(selectedMachine: machine);
  }

  void setSummaryAnimateTarget(double target) {
    state = state.copyWith(summaryAnimateTarget: target);
  }
}

final skinAnalysisResultControllerProvider = StateNotifierProvider.family<
    SkinAnalysisResultController, SkinAnalysisResultState, SkinAnalysisData>(
  (ref, data) => SkinAnalysisResultController(
    ref: ref,
    skinAnalyzeService: ref.read(skinAnalyzeServiceProvider),
    data: data,
    faceAgingService: ref.read(faceAgingServiceProvider),
    parameterSkinEvaluationService:
        ref.read(parameterSkinEvaluationServiceProvider),
  ),
);
