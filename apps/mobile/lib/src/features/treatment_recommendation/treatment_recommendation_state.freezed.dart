// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'treatment_recommendation_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TreatmentRecommendationState {
  double get basicPackageAnimateTarget => throw _privateConstructorUsedError;
  double get advancedPackageAnimateTarget => throw _privateConstructorUsedError;
  double get medicalTrilogyAnimateTarget => throw _privateConstructorUsedError;
  double get skincareRecommendationAnimateTarget =>
      throw _privateConstructorUsedError;
  double get paymentDetailAnimateTarget => throw _privateConstructorUsedError;
  PaymentDetail get paymentDetail => throw _privateConstructorUsedError;
  AsyncValue<SkinAnalyzeRecommendation?> get recommendation =>
      throw _privateConstructorUsedError;
  List<TreatmentPackageItem> get treatmentItems =>
      throw _privateConstructorUsedError;
  bool get showAllItems => throw _privateConstructorUsedError;
  SortBy get sortBy => throw _privateConstructorUsedError;
  List<TreatmentProduct> get searchTreatments =>
      throw _privateConstructorUsedError;
  AsyncValue<List<TreatmentProduct>?> get treatmentProducts =>
      throw _privateConstructorUsedError;
  bool get isSearchTreatment => throw _privateConstructorUsedError;
  AsyncValue<List<TreatmentProduct>?> get products =>
      throw _privateConstructorUsedError;

  /// Create a copy of TreatmentRecommendationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TreatmentRecommendationStateCopyWith<TreatmentRecommendationState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TreatmentRecommendationStateCopyWith<$Res> {
  factory $TreatmentRecommendationStateCopyWith(
          TreatmentRecommendationState value,
          $Res Function(TreatmentRecommendationState) then) =
      _$TreatmentRecommendationStateCopyWithImpl<$Res,
          TreatmentRecommendationState>;
  @useResult
  $Res call(
      {double basicPackageAnimateTarget,
      double advancedPackageAnimateTarget,
      double medicalTrilogyAnimateTarget,
      double skincareRecommendationAnimateTarget,
      double paymentDetailAnimateTarget,
      PaymentDetail paymentDetail,
      AsyncValue<SkinAnalyzeRecommendation?> recommendation,
      List<TreatmentPackageItem> treatmentItems,
      bool showAllItems,
      SortBy sortBy,
      List<TreatmentProduct> searchTreatments,
      AsyncValue<List<TreatmentProduct>?> treatmentProducts,
      bool isSearchTreatment,
      AsyncValue<List<TreatmentProduct>?> products});
}

/// @nodoc
class _$TreatmentRecommendationStateCopyWithImpl<$Res,
        $Val extends TreatmentRecommendationState>
    implements $TreatmentRecommendationStateCopyWith<$Res> {
  _$TreatmentRecommendationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TreatmentRecommendationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? basicPackageAnimateTarget = null,
    Object? advancedPackageAnimateTarget = null,
    Object? medicalTrilogyAnimateTarget = null,
    Object? skincareRecommendationAnimateTarget = null,
    Object? paymentDetailAnimateTarget = null,
    Object? paymentDetail = null,
    Object? recommendation = null,
    Object? treatmentItems = null,
    Object? showAllItems = null,
    Object? sortBy = null,
    Object? searchTreatments = null,
    Object? treatmentProducts = null,
    Object? isSearchTreatment = null,
    Object? products = null,
  }) {
    return _then(_value.copyWith(
      basicPackageAnimateTarget: null == basicPackageAnimateTarget
          ? _value.basicPackageAnimateTarget
          : basicPackageAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      advancedPackageAnimateTarget: null == advancedPackageAnimateTarget
          ? _value.advancedPackageAnimateTarget
          : advancedPackageAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      medicalTrilogyAnimateTarget: null == medicalTrilogyAnimateTarget
          ? _value.medicalTrilogyAnimateTarget
          : medicalTrilogyAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      skincareRecommendationAnimateTarget: null ==
              skincareRecommendationAnimateTarget
          ? _value.skincareRecommendationAnimateTarget
          : skincareRecommendationAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      paymentDetailAnimateTarget: null == paymentDetailAnimateTarget
          ? _value.paymentDetailAnimateTarget
          : paymentDetailAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      paymentDetail: null == paymentDetail
          ? _value.paymentDetail
          : paymentDetail // ignore: cast_nullable_to_non_nullable
              as PaymentDetail,
      recommendation: null == recommendation
          ? _value.recommendation
          : recommendation // ignore: cast_nullable_to_non_nullable
              as AsyncValue<SkinAnalyzeRecommendation?>,
      treatmentItems: null == treatmentItems
          ? _value.treatmentItems
          : treatmentItems // ignore: cast_nullable_to_non_nullable
              as List<TreatmentPackageItem>,
      showAllItems: null == showAllItems
          ? _value.showAllItems
          : showAllItems // ignore: cast_nullable_to_non_nullable
              as bool,
      sortBy: null == sortBy
          ? _value.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as SortBy,
      searchTreatments: null == searchTreatments
          ? _value.searchTreatments
          : searchTreatments // ignore: cast_nullable_to_non_nullable
              as List<TreatmentProduct>,
      treatmentProducts: null == treatmentProducts
          ? _value.treatmentProducts
          : treatmentProducts // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<TreatmentProduct>?>,
      isSearchTreatment: null == isSearchTreatment
          ? _value.isSearchTreatment
          : isSearchTreatment // ignore: cast_nullable_to_non_nullable
              as bool,
      products: null == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<TreatmentProduct>?>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TreatmentRecommendationStateImplCopyWith<$Res>
    implements $TreatmentRecommendationStateCopyWith<$Res> {
  factory _$$TreatmentRecommendationStateImplCopyWith(
          _$TreatmentRecommendationStateImpl value,
          $Res Function(_$TreatmentRecommendationStateImpl) then) =
      __$$TreatmentRecommendationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double basicPackageAnimateTarget,
      double advancedPackageAnimateTarget,
      double medicalTrilogyAnimateTarget,
      double skincareRecommendationAnimateTarget,
      double paymentDetailAnimateTarget,
      PaymentDetail paymentDetail,
      AsyncValue<SkinAnalyzeRecommendation?> recommendation,
      List<TreatmentPackageItem> treatmentItems,
      bool showAllItems,
      SortBy sortBy,
      List<TreatmentProduct> searchTreatments,
      AsyncValue<List<TreatmentProduct>?> treatmentProducts,
      bool isSearchTreatment,
      AsyncValue<List<TreatmentProduct>?> products});
}

/// @nodoc
class __$$TreatmentRecommendationStateImplCopyWithImpl<$Res>
    extends _$TreatmentRecommendationStateCopyWithImpl<$Res,
        _$TreatmentRecommendationStateImpl>
    implements _$$TreatmentRecommendationStateImplCopyWith<$Res> {
  __$$TreatmentRecommendationStateImplCopyWithImpl(
      _$TreatmentRecommendationStateImpl _value,
      $Res Function(_$TreatmentRecommendationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of TreatmentRecommendationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? basicPackageAnimateTarget = null,
    Object? advancedPackageAnimateTarget = null,
    Object? medicalTrilogyAnimateTarget = null,
    Object? skincareRecommendationAnimateTarget = null,
    Object? paymentDetailAnimateTarget = null,
    Object? paymentDetail = null,
    Object? recommendation = null,
    Object? treatmentItems = null,
    Object? showAllItems = null,
    Object? sortBy = null,
    Object? searchTreatments = null,
    Object? treatmentProducts = null,
    Object? isSearchTreatment = null,
    Object? products = null,
  }) {
    return _then(_$TreatmentRecommendationStateImpl(
      basicPackageAnimateTarget: null == basicPackageAnimateTarget
          ? _value.basicPackageAnimateTarget
          : basicPackageAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      advancedPackageAnimateTarget: null == advancedPackageAnimateTarget
          ? _value.advancedPackageAnimateTarget
          : advancedPackageAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      medicalTrilogyAnimateTarget: null == medicalTrilogyAnimateTarget
          ? _value.medicalTrilogyAnimateTarget
          : medicalTrilogyAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      skincareRecommendationAnimateTarget: null ==
              skincareRecommendationAnimateTarget
          ? _value.skincareRecommendationAnimateTarget
          : skincareRecommendationAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      paymentDetailAnimateTarget: null == paymentDetailAnimateTarget
          ? _value.paymentDetailAnimateTarget
          : paymentDetailAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      paymentDetail: null == paymentDetail
          ? _value.paymentDetail
          : paymentDetail // ignore: cast_nullable_to_non_nullable
              as PaymentDetail,
      recommendation: null == recommendation
          ? _value.recommendation
          : recommendation // ignore: cast_nullable_to_non_nullable
              as AsyncValue<SkinAnalyzeRecommendation?>,
      treatmentItems: null == treatmentItems
          ? _value._treatmentItems
          : treatmentItems // ignore: cast_nullable_to_non_nullable
              as List<TreatmentPackageItem>,
      showAllItems: null == showAllItems
          ? _value.showAllItems
          : showAllItems // ignore: cast_nullable_to_non_nullable
              as bool,
      sortBy: null == sortBy
          ? _value.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as SortBy,
      searchTreatments: null == searchTreatments
          ? _value._searchTreatments
          : searchTreatments // ignore: cast_nullable_to_non_nullable
              as List<TreatmentProduct>,
      treatmentProducts: null == treatmentProducts
          ? _value.treatmentProducts
          : treatmentProducts // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<TreatmentProduct>?>,
      isSearchTreatment: null == isSearchTreatment
          ? _value.isSearchTreatment
          : isSearchTreatment // ignore: cast_nullable_to_non_nullable
              as bool,
      products: null == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<TreatmentProduct>?>,
    ));
  }
}

/// @nodoc

class _$TreatmentRecommendationStateImpl
    implements _TreatmentRecommendationState {
  const _$TreatmentRecommendationStateImpl(
      {this.basicPackageAnimateTarget = 0,
      this.advancedPackageAnimateTarget = 0,
      this.medicalTrilogyAnimateTarget = 0,
      this.skincareRecommendationAnimateTarget = 0,
      this.paymentDetailAnimateTarget = 0,
      this.paymentDetail = const PaymentDetail(total: 0, paymentDetailList: []),
      this.recommendation = const AsyncValue.data(null),
      final List<TreatmentPackageItem> treatmentItems = const [],
      this.showAllItems = false,
      this.sortBy = SortBy.score,
      final List<TreatmentProduct> searchTreatments = const [],
      this.treatmentProducts = const AsyncValue.data(null),
      this.isSearchTreatment = false,
      this.products = const AsyncValue.data(null)})
      : _treatmentItems = treatmentItems,
        _searchTreatments = searchTreatments;

  @override
  @JsonKey()
  final double basicPackageAnimateTarget;
  @override
  @JsonKey()
  final double advancedPackageAnimateTarget;
  @override
  @JsonKey()
  final double medicalTrilogyAnimateTarget;
  @override
  @JsonKey()
  final double skincareRecommendationAnimateTarget;
  @override
  @JsonKey()
  final double paymentDetailAnimateTarget;
  @override
  @JsonKey()
  final PaymentDetail paymentDetail;
  @override
  @JsonKey()
  final AsyncValue<SkinAnalyzeRecommendation?> recommendation;
  final List<TreatmentPackageItem> _treatmentItems;
  @override
  @JsonKey()
  List<TreatmentPackageItem> get treatmentItems {
    if (_treatmentItems is EqualUnmodifiableListView) return _treatmentItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_treatmentItems);
  }

  @override
  @JsonKey()
  final bool showAllItems;
  @override
  @JsonKey()
  final SortBy sortBy;
  final List<TreatmentProduct> _searchTreatments;
  @override
  @JsonKey()
  List<TreatmentProduct> get searchTreatments {
    if (_searchTreatments is EqualUnmodifiableListView)
      return _searchTreatments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_searchTreatments);
  }

  @override
  @JsonKey()
  final AsyncValue<List<TreatmentProduct>?> treatmentProducts;
  @override
  @JsonKey()
  final bool isSearchTreatment;
  @override
  @JsonKey()
  final AsyncValue<List<TreatmentProduct>?> products;

  @override
  String toString() {
    return 'TreatmentRecommendationState(basicPackageAnimateTarget: $basicPackageAnimateTarget, advancedPackageAnimateTarget: $advancedPackageAnimateTarget, medicalTrilogyAnimateTarget: $medicalTrilogyAnimateTarget, skincareRecommendationAnimateTarget: $skincareRecommendationAnimateTarget, paymentDetailAnimateTarget: $paymentDetailAnimateTarget, paymentDetail: $paymentDetail, recommendation: $recommendation, treatmentItems: $treatmentItems, showAllItems: $showAllItems, sortBy: $sortBy, searchTreatments: $searchTreatments, treatmentProducts: $treatmentProducts, isSearchTreatment: $isSearchTreatment, products: $products)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TreatmentRecommendationStateImpl &&
            (identical(other.basicPackageAnimateTarget, basicPackageAnimateTarget) ||
                other.basicPackageAnimateTarget == basicPackageAnimateTarget) &&
            (identical(other.advancedPackageAnimateTarget,
                    advancedPackageAnimateTarget) ||
                other.advancedPackageAnimateTarget ==
                    advancedPackageAnimateTarget) &&
            (identical(other.medicalTrilogyAnimateTarget, medicalTrilogyAnimateTarget) ||
                other.medicalTrilogyAnimateTarget ==
                    medicalTrilogyAnimateTarget) &&
            (identical(other.skincareRecommendationAnimateTarget,
                    skincareRecommendationAnimateTarget) ||
                other.skincareRecommendationAnimateTarget ==
                    skincareRecommendationAnimateTarget) &&
            (identical(other.paymentDetailAnimateTarget, paymentDetailAnimateTarget) ||
                other.paymentDetailAnimateTarget ==
                    paymentDetailAnimateTarget) &&
            (identical(other.paymentDetail, paymentDetail) ||
                other.paymentDetail == paymentDetail) &&
            (identical(other.recommendation, recommendation) ||
                other.recommendation == recommendation) &&
            const DeepCollectionEquality()
                .equals(other._treatmentItems, _treatmentItems) &&
            (identical(other.showAllItems, showAllItems) ||
                other.showAllItems == showAllItems) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            const DeepCollectionEquality()
                .equals(other._searchTreatments, _searchTreatments) &&
            (identical(other.treatmentProducts, treatmentProducts) ||
                other.treatmentProducts == treatmentProducts) &&
            (identical(other.isSearchTreatment, isSearchTreatment) ||
                other.isSearchTreatment == isSearchTreatment) &&
            (identical(other.products, products) || other.products == products));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      basicPackageAnimateTarget,
      advancedPackageAnimateTarget,
      medicalTrilogyAnimateTarget,
      skincareRecommendationAnimateTarget,
      paymentDetailAnimateTarget,
      paymentDetail,
      recommendation,
      const DeepCollectionEquality().hash(_treatmentItems),
      showAllItems,
      sortBy,
      const DeepCollectionEquality().hash(_searchTreatments),
      treatmentProducts,
      isSearchTreatment,
      products);

  /// Create a copy of TreatmentRecommendationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TreatmentRecommendationStateImplCopyWith<
          _$TreatmentRecommendationStateImpl>
      get copyWith => __$$TreatmentRecommendationStateImplCopyWithImpl<
          _$TreatmentRecommendationStateImpl>(this, _$identity);
}

abstract class _TreatmentRecommendationState
    implements TreatmentRecommendationState {
  const factory _TreatmentRecommendationState(
          {final double basicPackageAnimateTarget,
          final double advancedPackageAnimateTarget,
          final double medicalTrilogyAnimateTarget,
          final double skincareRecommendationAnimateTarget,
          final double paymentDetailAnimateTarget,
          final PaymentDetail paymentDetail,
          final AsyncValue<SkinAnalyzeRecommendation?> recommendation,
          final List<TreatmentPackageItem> treatmentItems,
          final bool showAllItems,
          final SortBy sortBy,
          final List<TreatmentProduct> searchTreatments,
          final AsyncValue<List<TreatmentProduct>?> treatmentProducts,
          final bool isSearchTreatment,
          final AsyncValue<List<TreatmentProduct>?> products}) =
      _$TreatmentRecommendationStateImpl;

  @override
  double get basicPackageAnimateTarget;
  @override
  double get advancedPackageAnimateTarget;
  @override
  double get medicalTrilogyAnimateTarget;
  @override
  double get skincareRecommendationAnimateTarget;
  @override
  double get paymentDetailAnimateTarget;
  @override
  PaymentDetail get paymentDetail;
  @override
  AsyncValue<SkinAnalyzeRecommendation?> get recommendation;
  @override
  List<TreatmentPackageItem> get treatmentItems;
  @override
  bool get showAllItems;
  @override
  SortBy get sortBy;
  @override
  List<TreatmentProduct> get searchTreatments;
  @override
  AsyncValue<List<TreatmentProduct>?> get treatmentProducts;
  @override
  bool get isSearchTreatment;
  @override
  AsyncValue<List<TreatmentProduct>?> get products;

  /// Create a copy of TreatmentRecommendationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TreatmentRecommendationStateImplCopyWith<
          _$TreatmentRecommendationStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
