import 'package:chewie/chewie.dart';
import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';
import 'package:video_player/video_player.dart';

class ImageVideoPopUpWidget extends StatefulWidget {
  const ImageVideoPopUpWidget({
    required this.imageUrl,
    required this.videoUrl,
    super.key,
  });

  final String? imageUrl;
  final String? videoUrl;

  @override
  State<ImageVideoPopUpWidget> createState() => _ImageVideoPopUpWidgetState();
}

class _ImageVideoPopUpWidgetState extends State<ImageVideoPopUpWidget> {
  VideoPlayerController? _controller;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    if (widget.videoUrl != null) {
      _initializeVideoPlayer();
    } else {
      _isLoading = false;
    }
  }

  Future<void> _initializeVideoPlayer() async {
    try {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.videoUrl!),
      );

      // Add a listener for errors
      _controller?.addListener(_checkForVideoErrors);

      // Initialize with error handling
      await _controller?.initialize().then((_) {
        if (mounted) {
          setState(() {
            _chewieController = ChewieController(
              videoPlayerController: _controller!,
              allowedScreenSleep: false,
              showOptions: false,
              autoInitialize: true,
              autoPlay: true,
              materialProgressColors: ChewieProgressColors(
                playedColor: context.appColors.fgPrimaryColor,
                bufferedColor: context.appColors.fgBaseColor.withAlpha(64),
              ),
              errorBuilder: (context, errorMessage) {
                return Center(
                  child: Text(
                    errorMessage,
                    textAlign: TextAlign.center,
                  ),
                );
              },
            );
            _isLoading = false;
          });
        }
      });
    } on Exception catch (_) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    }
  }

  void _checkForVideoErrors() {
    if (_controller?.value.hasError ?? false) {
      setState(() {
        _hasError = true;
      });
    }
  }

  @override
  void dispose() {
    _controller
      ?..removeListener(_checkForVideoErrors)
      ..dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  Future<void> showVideo() {
    if (widget.videoUrl == null) {
      return Future.value();
    }

    Future.delayed(
      const Duration(milliseconds: 500),
      () => _chewieController?.play(),
    );

    return showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(),
        content: _hasError
            ? Container(
                width: AppSizes.w(200),
                height: AppSizes.h(100),
                decoration: BoxDecoration(
                  color: context.appColors.fgBaseColor,
                  borderRadius: BorderRadius.circular(AppSizes.r20),
                ),
                child: Center(
                  child: Text(
                    'Video failed to load',
                    style: TextStyle(color: context.appColors.textBaseColor),
                    textAlign: TextAlign.center,
                  ),
                ),
              )
            : _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: context.appColors.fgPrimaryColor,
                      strokeCap: StrokeCap.round,
                    ),
                  )
                : Stack(
                    children: [
                      AspectRatio(
                        aspectRatio: _controller?.value.aspectRatio ?? 16 / 9,
                        child: Chewie(
                          controller: _chewieController!,
                        ),
                      ),
                      Positioned(
                        top: AppSizes.h(12),
                        right: 12,
                        child: CommonButton(
                          variant: ButtonVariant.outlined,
                          child: const Icon(IconoirIcons.xmark),
                          onPressed: () => context.pop(),
                        ),
                      ),
                    ],
                  ),
      ),
    ).then((_) {
      _chewieController?.pause();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.videoUrl != null ? showVideo : null,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: widget.imageUrl != null
                  ? DecorationImage(
                      image: NetworkImage(widget.imageUrl!),
                      fit: BoxFit.cover,
                    )
                  : DecorationImage(
                      image: AssetImage(Assets.images.treatmentImage.path),
                      fit: BoxFit.cover,
                    ),
            ),
          ),
          if (widget.videoUrl != null)
            Container(
              width: AppSizes.w36,
              height: AppSizes.h36,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSizes.r32),
                color: context.appColors.textBaseColor.withValues(alpha: .3),
              ),
              child: Icon(
                Icons.play_arrow,
                color: context.appColors.fgBaseColor,
              ),
            ),
        ],
      ),
    );
  }
}
