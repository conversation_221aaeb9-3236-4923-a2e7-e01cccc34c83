import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skincare_card_option/skincare_card_option.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_controller.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_state.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/skincare_card_select.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkincareRecommendationSelect extends ConsumerStatefulWidget {
  const SkincareRecommendationSelect({
    required this.skinAnalyzeId,
    super.key,
  });

  final String skinAnalyzeId;

  @override
  ConsumerState<SkincareRecommendationSelect> createState() =>
      _SkincareRecommendationSelectState();
}

class _SkincareRecommendationSelectState
    extends ConsumerState<SkincareRecommendationSelect> {
  final List<String> _selectedItems = [];

  TreatmentRecommendationController get controller => ref.read(
        treatmentRecommendationControllerProvider(widget.skinAnalyzeId)
            .notifier,
      );

  TreatmentRecommendationState get state => ref.watch(
        treatmentRecommendationControllerProvider(widget.skinAnalyzeId),
      );

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      borderRadius: BorderRadius.circular(AppSizes.h32),
      padding: EdgeInsets.all(AppSizes.w24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSizes.h8,
        children: [
          Text(
            '''SKINCARE RECOMMENDATION''',
            style: context.titleCard?.copyWith(
              fontWeight: FontWeight.w300,
            ),
          ),
          Gap.h4,
          ...state.products.when(
            data: (products) => [
              SkincareCardSelectWidget<String>(
                options: products
                        ?.map(
                          (e) => SkincareCardOption(
                            value: e.name,
                            label: e.name,
                            image: e.mediaUrl,
                            description: e.description,
                            price: e.price.toDouble(),
                          ),
                        )
                        .toList() ??
                    [],
                isMultiSelect: true,
                spacing: AppSizes.w4,
                initialSelected: _selectedItems,
                onSelect: (value) {
                  if (value != null) {
                    setState(() {
                      if (_selectedItems.contains(value)) {
                        _selectedItems.remove(value);
                      } else {
                        _selectedItems.add(value);
                      }
                    });
                    controller.updateSkincareRecommendation(_selectedItems);
                  }
                },
              ),
            ],
            error: (error, stackTrace) => [
              const Text('Failed to load products'),
            ],
            loading: () => [
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
