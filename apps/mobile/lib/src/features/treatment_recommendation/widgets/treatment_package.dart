import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/treatment_package/treatment_package_item.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_controller.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_state.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/image_video_popup.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:euromedica_aizer/src/utils/extensions/num_extension.dart';
import 'package:euromedica_aizer/src/utils/extensions/string_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TreatmentPackage extends ConsumerStatefulWidget {
  const TreatmentPackage({
    required this.items,
    required this.skinAnalyzeId,
    super.key,
  });

  final List<TreatmentPackageItem> items;
  final String skinAnalyzeId;

  @override
  ConsumerState<TreatmentPackage> createState() => _TreatmentPackageState();
}

class _TreatmentPackageState extends ConsumerState<TreatmentPackage> {
  TreatmentRecommendationController get controller => ref.read(
        treatmentRecommendationControllerProvider(widget.skinAnalyzeId)
            .notifier,
      );

  TreatmentRecommendationState get state => ref.watch(
        treatmentRecommendationControllerProvider(widget.skinAnalyzeId),
      );

  @override
  Widget build(BuildContext context) {
    final displayedItems = state.showAllItems
        ? widget.items
        : widget.items.take(controller.showItemsCount).toList();

    return GlassContainer(
      borderRadius: BorderRadius.circular(AppSizes.h32),
      padding: EdgeInsets.all(AppSizes.w24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            spacing: AppSizes.w24,
            children: [
              CommonCheckbox(
                value: controller.selectedItems.length == widget.items.length,
                onChanged: controller.toggleSelectAll,
              ),
              Text(
                'SELECT ALL',
                style: context.heading4?.copyWith(
                  fontWeight: FontWeight.w400,
                ),
              ),
              const Spacer(),
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: AppSizes.w(120),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: context.appColors.borderWhite,
                    borderRadius: BorderRadius.circular(AppSizes.h8),
                    boxShadow: AppShadows.shadowCard,
                  ),
                  child: CommonDropdown(
                    initialValue: state.sortBy.name,
                    dropdownMenuEntries:
                        controller.sortByOptions.map((e) => e.name).toList(),
                    overlayWidth: AppSizes.w(200),
                    width: AppSizes.w(60),
                    isCheckboxStyle: true,
                    overlayWidget: Assets.icons.sortIcon.svg(),
                    onSelected: (value) {
                      if (value != null) {
                        controller.setSortBy(
                          controller.sortByOptions
                              .firstWhere((e) => e.name == value),
                        );
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
          Gap.h16,
          ...displayedItems.map(
            (item) {
              return LayoutBuilder(
                builder: (context, constraints) {
                  final width = constraints.maxWidth;
                  return Container(
                    padding: EdgeInsets.only(top: AppSizes.h24),
                    width: width,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: AppSizes.w24,
                      children: [
                        CommonCheckbox(
                          value: controller.selectedItems.contains(item),
                          onChanged: (value) =>
                              controller.toggleTreatmentItem(item),
                        ),
                        SizedBox(
                          width: AppSizes.w(303),
                          height: AppSizes.h(170),
                          child: ImageVideoPopUpWidget(
                            imageUrl: item.thumbnailUrl,
                            videoUrl: item.mediaUrl,
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item.title,
                                style: context.lead,
                              ),
                              Gap.h8,
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: item.categories.map((category) {
                                  return Container(
                                    decoration: BoxDecoration(
                                      color: context.appColors.borderWhite,
                                      borderRadius:
                                          BorderRadius.circular(AppSizes.h24),
                                      border: Border.all(
                                        color: context.appColors.fgPrimaryColor,
                                      ),
                                    ),
                                    margin: EdgeInsets.only(right: AppSizes.w8),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSizes.w24,
                                      vertical: AppSizes.h4,
                                    ),
                                    child: Text(
                                      category,
                                      style: context.p,
                                    ),
                                  );
                                }).toList(),
                              ),
                              Gap.h8,
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'Concern',
                                      style: context.small?.copyWith(
                                        color: context.appColors.fgErrorColor,
                                      ),
                                    ),
                                    TextSpan(
                                      text:
                                          ': ${item.concern.join(', ').toTitleCase}',
                                      style: context.small,
                                    ),
                                  ],
                                ),
                              ),
                              Gap.h12,
                              Text(
                                item.description,
                                style: context.p,
                              ),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Rp ${item.price.thousandSeparator}',
                              style: context.numberPrice,
                            ),
                            Gap.h16,
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: AppShadows.shadowCard,
                                    color: Colors.white,
                                  ),
                                  child: IconButton(
                                    padding: const EdgeInsets.all(6),
                                    icon: const Icon(
                                      Icons.remove,
                                      color: Colors.black,
                                    ),
                                    onPressed: () {
                                      controller.decrementItemQuantity(item);
                                    },
                                  ),
                                ),
                                Gap.w8,
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: AppSizes.h12,
                                    horizontal: AppSizes.w36,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color:
                                          context.appColors.borderSubtleColor,
                                    ),
                                    borderRadius: BorderRadius.circular(
                                      AppSizes.h12,
                                    ),
                                    color: Colors.white,
                                  ),
                                  child: Text(
                                    '${item.quantity}',
                                    style: context.large,
                                  ),
                                ),
                                Gap.w8,
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: AppShadows.shadowCard,
                                    color: context.appColors.fgPrimaryColor,
                                  ),
                                  child: IconButton(
                                    padding: const EdgeInsets.all(6),
                                    icon: const Icon(
                                      Icons.add,
                                      color: Colors.white,
                                    ),
                                    onPressed: () {
                                      controller.incrementItemQuantity(item);
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
          Gap.h40,
          if (widget.items.length > controller.showItemsCount)
            GestureDetector(
              onTap: () => controller.toggleShowAllItems(),
              child: Container(
                decoration: BoxDecoration(
                  border:
                      Border.all(color: context.appColors.borderSubtleColor),
                  color: context.appColors.borderWhite,
                  borderRadius: BorderRadius.circular(AppSizes.h12),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: AppSizes.w80,
                  vertical: AppSizes.h12,
                ),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        state.showAllItems
                            ? 'See Less Treatment'
                            : 'See More Treatment',
                        style: context.p,
                      ),
                      Gap.w8,
                      Icon(
                        state.showAllItems
                            ? Icons.arrow_upward_rounded
                            : Icons.arrow_downward_rounded,
                        size: AppSizes.h16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          Gap.h24,
          Center(
            child: GestureDetector(
              onTap: () => controller.toggleSearchTreatment(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    IconoirIcons.search,
                    size: AppSizes.h16,
                  ),
                  Gap.w8,
                  Text(
                    'Search Other Treatment',
                    style: context.p?.copyWith(
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Gap.h24,
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                spacing: AppSizes.w32,
                children: [
                  Text(
                    'Total',
                    style: context.lead,
                  ),
                  Text(
                    '''Rp ${controller.selectedItemsTotal.toInt().thousandSeparator}''',
                    style: context.titleCard,
                  ),
                ],
              ),
            ],
          ),
          Gap.h32,
        ],
      ),
    );
  }
}
