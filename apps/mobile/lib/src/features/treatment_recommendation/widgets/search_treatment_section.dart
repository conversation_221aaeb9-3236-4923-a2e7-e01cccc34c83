import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/budget_estimation/budget_estimation_widget.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_controller.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/search_treatment_field.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/treatment_item.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SearchTreatmentSection extends ConsumerWidget {
  const SearchTreatmentSection({
    required this.skinAnalyzeId,
    super.key,
  });

  final String skinAnalyzeId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(
      treatmentRecommendationControllerProvider(skinAnalyzeId).notifier,
    );

    final state = ref.watch(
      treatmentRecommendationControllerProvider(skinAnalyzeId),
    );

    return GestureDetector(
      onTap: controller.toggleSearchTreatment,
      child: Scaffold(
        backgroundColor: Colors.black.withAlpha(100),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Gap.h16,
            SearchTreatmentField(skinAnalyzeId: skinAnalyzeId),
            Expanded(
              child: state.treatmentProducts.when(
                data: (products) {
                  if (products == null) {
                    return Center(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSizes.w16,
                          vertical: AppSizes.h8,
                        ),
                        child: Text(
                          'Type Treatment Name on Search Bar',
                          style: context.p,
                        ),
                      ),
                    );
                  }
                  if (products.isEmpty) {
                    return Center(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSizes.w16,
                          vertical: AppSizes.h8,
                        ),
                        child: Text(
                          'No products found',
                          style: context.p,
                        ),
                      ),
                    );
                  }
                  return GridView.builder(
                    shrinkWrap: true,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      childAspectRatio: 3.3,
                    ),
                    padding: EdgeInsets.symmetric(
                      vertical: AppSizes.h16,
                      horizontal: AppSizes.w32,
                    ),
                    itemCount: products.length,
                    itemBuilder: (context, index) {
                      return TreatmentItem(
                        treatment: products[index],
                        skinAnalyzeId: skinAnalyzeId,
                      );
                    },
                  );
                },
                error: (error, stackTrace) => Center(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSizes.w16,
                      vertical: AppSizes.h8,
                    ),
                    child: Text(
                      error.toString(),
                      style: context.p,
                    ),
                  ),
                ),
                loading: () => Center(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSizes.w16,
                      vertical: AppSizes.h8,
                    ),
                    child: const CircularProgressIndicator(),
                  ),
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: GlassBottomNavBar(
          content: Row(
            children: [
              const BudgetEstimationWidget(),
              const Spacer(),
              CommonButton(
                onPressed: controller.toggleSearchTreatment,
                variant: ButtonVariant.outlined,
                child: const Text('Back'),
              ),
              Gap.w16,
              CommonButton(
                isDisabled: state.searchTreatments.isEmpty,
                onPressed: controller.insertSearchTreatmentIntoRecommendation,
                padding: EdgeInsets.symmetric(
                  horizontal: AppSizes.w20,
                  vertical: AppSizes.h8,
                ),
                child: const Text('Add Treatment'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
