part of 'skincare_card_select.dart';

class _SkincareCardItemWidget<T> extends StatelessWidget
    implements SkincareCardOption<T> {
  const _SkincareCardItemWidget({
    required this.label,
    super.key,
    this.description,
    this.image,
    this.value,
    this.price,
    this.isActive = false,
  });

  @override
  final String label;

  @override
  final String? description;

  @override
  final String? image;

  @override
  final T? value;

  @override
  final double? price;

  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOut,
      margin: EdgeInsets.symmetric(
        vertical: isActive ? 0 : AppSizes.w12,
        horizontal: isActive ? AppSizes.w12 : AppSizes.w12,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSizes.r24),
        border: isActive
            ? Border.all(
                color: context.appColors.borderWhite,
                width: AppSizes.w(5),
              )
            : null,
        boxShadow: [
          if (isActive) ...[
            BoxShadow(
              color: context.appColors.borderBaseColor.withAlpha(76),
              blurRadius: AppSizes.r8,
              spreadRadius: AppSizes.r4,
              offset: const Offset(0, 4),
            ),
          ],
        ],
      ),
      child: InnerShadow(
        spread: .2,
        borderRadius: BorderRadius.circular(AppSizes.w(16)),
        colors: isActive
            ? [
                Colors.transparent,
                Colors.white10,
              ]
            : [
                Colors.transparent,
                Colors.transparent,
              ],
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  image: image != null
                      ? DecorationImage(
                          image: NetworkImage(image!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: context.cardColor,
                  borderRadius: BorderRadius.circular(AppSizes.r12),
                ),
                padding: EdgeInsets.all(AppSizes.w24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  spacing: AppSizes.h8,
                  children: [
                    Text(
                      label,
                      style: context.heading4?.copyWith(
                        color: context.appColors.textBaseColor,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    if (price != null)
                      Text(
                        'Rp ${price?.toInt().thousandSeparator}',
                        style: context.heading4?.copyWith(
                          color: context.appColors.textBaseColor,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    if (description != null)
                      LayoutBuilder(
                        builder: (context, constraints) {
                          return SizedBox(
                            width:
                                (constraints.maxWidth / 100).floorToDouble() *
                                    100,
                            child: Text(
                              description!,
                              style: context.large?.copyWith(
                                color: context.appColors.textBaseColor,
                              ),
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
