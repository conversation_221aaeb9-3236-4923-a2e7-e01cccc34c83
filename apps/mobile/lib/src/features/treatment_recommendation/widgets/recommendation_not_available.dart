import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RecommendationNotAvailable extends ConsumerWidget {
  const RecommendationNotAvailable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GlassContainer(
      outlineColor: context.appColors.fgErrorColor,
      borderRadius: BorderRadius.circular(AppSizes.h32),
      padding: EdgeInsets.all(AppSizes.w24),
      child: Column(
        spacing: AppSizes.h8,
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Recommendation ',
                  style: context.titleCard,
                ),
                TextSpan(
                  text: 'Not Available',
                  style: context.titleCard?.copyWith(
                    color: context.appColors.fgErrorColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Gap.h4,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSizes.w(160)),
            child: Text(
              'Unfortunately, some of the contraindications from your survey prevented us from making a treatment recommendation. You can still use skincare products on a daily basis to take care of your skin',
              style: context.p,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
