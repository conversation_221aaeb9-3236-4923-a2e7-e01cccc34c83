import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SearchTreatmentField extends ConsumerWidget {
  const SearchTreatmentField({
    required this.skinAnalyzeId,
    super.key,
  });

  final String skinAnalyzeId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(
      treatmentRecommendationControllerProvider(skinAnalyzeId).notifier,
    );
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(40),
        boxShadow: AppShadows.shadowCard,
      ),
      padding: EdgeInsets.symmetric(horizontal: AppSizes.w12),
      margin: EdgeInsets.symmetric(horizontal: AppSizes.w32),
      child: TextField(
        controller: controller.searchController,
        onChanged: (value) {
          if (value.isNotEmpty) {
            controller.searchTreatment(searchText: value);
          }
        },
        decoration: InputDecoration(
          prefixIcon: const Icon(Icons.search),
          hintText: 'Skincare treatment',
          suffixIcon: controller.searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    controller.searchController.clear();
                  },
                  icon: const Icon(Icons.close),
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppSizes.w24,
            vertical: AppSizes.h16,
          ),
        ),
      ),
    );
  }
}
