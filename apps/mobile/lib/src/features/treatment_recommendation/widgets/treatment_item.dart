import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_controller.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:euromedica_aizer/src/utils/extensions/num_extension.dart';
import 'package:euromedica_aizer/src/utils/extensions/string_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TreatmentItem extends ConsumerWidget {
  const TreatmentItem({
    required this.treatment,
    required this.skinAnalyzeId,
    super.key,
  });

  final TreatmentProduct treatment;
  final String skinAnalyzeId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(
      treatmentRecommendationControllerProvider(skinAnalyzeId).notifier,
    );
    final state = ref.watch(
      treatmentRecommendationControllerProvider(skinAnalyzeId),
    );

    return GestureDetector(
      onTap: () {
        if (state.searchTreatments.contains(treatment)) {
          controller.removeSearchTreatment(treatment);
        } else {
          controller.addSearchTreatment(treatment);
        }
      },
      child: Container(
        padding: EdgeInsets.all(AppSizes.w16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizes.r16),
          color: Colors.white,
          boxShadow: AppShadows.shadowCard,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: AppSizes.w24,
          children: [
            CommonCheckbox(
              value: state.searchTreatments.contains(treatment),
              onChanged: (value) {
                if (value == true) {
                  controller.addSearchTreatment(treatment);
                } else {
                  controller.removeSearchTreatment(treatment);
                }
              },
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    treatment.name,
                    style: context.lead,
                  ),
                  Gap.h8,
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: treatment.categories.map((category) {
                      return Container(
                        decoration: BoxDecoration(
                          color: context.appColors.borderWhite,
                          borderRadius: BorderRadius.circular(AppSizes.h24),
                          border: Border.all(
                            color: context.appColors.fgPrimaryColor,
                          ),
                        ),
                        margin: EdgeInsets.only(right: AppSizes.w8),
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSizes.w24,
                          vertical: AppSizes.h4,
                        ),
                        child: Text(
                          category,
                          style: context.p,
                        ),
                      );
                    }).toList(),
                  ),
                  Gap.h8,
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: 'Concern',
                          style: context.small?.copyWith(
                            color: context.appColors.fgErrorColor,
                          ),
                        ),
                        TextSpan(
                          text:
                              ': ${treatment.solvedConcerns.join(', ').toTitleCase}',
                          style: context.small,
                        ),
                      ],
                    ),
                  ),
                  Gap.h12,
                  Text(
                    'Rp ${treatment.price.thousandSeparator}',
                    style: context.numberPrice,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
