import 'package:euromedica_aizer/src/common/data/models/entity/payment_detail/payment_detail.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_analyze_recommendation/skin_analyze_recommendation.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/treatment_package/treatment_package_item.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:euromedica_aizer/src/common/domain/enums/sort_by.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'treatment_recommendation_state.freezed.dart';

@freezed
class TreatmentRecommendationState with _$TreatmentRecommendationState {
  const factory TreatmentRecommendationState({
    @Default(0) double basicPackageAnimateTarget,
    @Default(0) double advancedPackageAnimateTarget,
    @Default(0) double medicalTrilogyAnimateTarget,
    @Default(0) double skincareRecommendationAnimateTarget,
    @Default(0) double paymentDetailAnimateTarget,
    @Default(PaymentDetail(total: 0, paymentDetailList: []))
    PaymentDetail paymentDetail,
    @Default(AsyncValue.data(null))
    AsyncValue<SkinAnalyzeRecommendation?> recommendation,
    @Default([]) List<TreatmentPackageItem> treatmentItems,
    @Default(false) bool showAllItems,
    @Default(SortBy.score) SortBy sortBy,
    @Default([]) List<TreatmentProduct> searchTreatments,
    @Default(AsyncValue.data(null))
    AsyncValue<List<TreatmentProduct>?> treatmentProducts,
    @Default(false) bool isSearchTreatment,
    @Default(AsyncValue.data(null))
    AsyncValue<List<TreatmentProduct>?> products,
  }) = _TreatmentRecommendationState;
}
