import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/budget_estimation/budget_estimation_widget.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_controller.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/intro_section.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/payment_detail_section.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/recommendation_not_available.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/search_treatment_section.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/skincare_recommendation_select.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/widgets/treatment_package.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/date_time_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:visibility_detector/visibility_detector.dart';

class TreatmentRecommendationScreen extends ConsumerWidget {
  const TreatmentRecommendationScreen({
    required this.skinAnalyze,
    super.key,
  });

  final SkinAnalyze skinAnalyze;

  static const Key treatmentRecommendationBasic =
      Key('treatment_recommendation_basic');
  static const Key treatmentRecommendationAdvanced =
      Key('treatment_recommendation_advanced');
  static const Key treatmentRecommendationMedical =
      Key('treatment_recommendation_medical');
  static const Key treatmentRecommendationSkincare =
      Key('treatment_recommendation_skincare');
  static const Key paymentDetail = Key('payment_detail');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final animateDuration = 300.ms;
    final delayDuration = 100.ms;
    const animateCurve = Curves.easeOutQuart;
    const slideBegin = -0.2;
    const double slideEnd = 0;
    const blurBegin = Offset(10, 10);
    const blurEnd = Offset.zero;
    final skinAnalyzeId = skinAnalyze.id ?? '';

    final state =
        ref.watch(treatmentRecommendationControllerProvider(skinAnalyzeId));
    final controller = ref.read(
      treatmentRecommendationControllerProvider(skinAnalyzeId).notifier,
    );

    return Stack(
      children: [
        CommonScaffold(
          appBar: const CommonTopBar(
            title: 'Step 4',
          ),
          body: ListView(
            physics: const ClampingScrollPhysics(),
            padding: EdgeInsets.symmetric(
              vertical: AppSizes.h24,
              horizontal: AppSizes.w32,
            ),
            children: [
              Column(
                children: [
                  Text(
                    'TREATMENT RECOMMENDATIONS',
                    style: context.titleCard,
                  ),
                  Gap.h8,
                  Text(
                    DateTime.now().toStringFormatted(
                      'EEEE, d MMMM yyyy [HH:mm]',
                    ),
                    style: context.p,
                  ),
                ],
              ),
              Gap.h32,
              state.recommendation.when(
                data: (recommendation) {
                  if (recommendation == null ||
                      recommendation.treatments == null ||
                      recommendation.treatments!.isEmpty) {
                    return const RecommendationNotAvailable();
                  }

                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IntroSection(
                        name: skinAnalyze.name ?? '',
                        message: recommendation.summary ?? '',
                      ),
                      Gap.h32,
                      TreatmentPackage(
                        skinAnalyzeId: skinAnalyzeId,
                        items: controller.getAllTreatmentItems(),
                      ),
                    ],
                  );
                },
                loading: () => const Center(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                error: (error, stackTrace) => Center(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Failed to load treatment recommendations',
                          style: context.p,
                        ),
                        Gap.h16,
                        CommonButton(
                          onPressed: () {
                            ref.invalidate(
                              treatmentRecommendationControllerProvider(
                                skinAnalyzeId,
                              ),
                            );
                          },
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSizes.w24,
                            vertical: AppSizes.h12,
                          ),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Gap.h32,
              VisibilityDetector(
                key: treatmentRecommendationSkincare,
                onVisibilityChanged: (info) {
                  if (info.visibleFraction > .25) {
                    controller.setSkincareRecommendationAnimateTarget();
                  }
                },
                child: SkincareRecommendationSelect(
                  skinAnalyzeId: skinAnalyzeId,
                )
                    .animate(
                      target: state.skincareRecommendationAnimateTarget,
                      autoPlay: false,
                    )
                    .slideX(
                      begin: slideBegin,
                      end: slideEnd,
                      delay: delayDuration,
                      duration: animateDuration,
                      curve: animateCurve,
                    )
                    .fadeIn(
                      delay: delayDuration,
                      duration: animateDuration,
                      curve: animateCurve,
                    )
                    .blur(
                      begin: blurBegin,
                      end: blurEnd,
                      delay: delayDuration,
                      duration: animateDuration,
                      curve: animateCurve,
                    ),
              ),
              Gap.h32,
              VisibilityDetector(
                key: paymentDetail,
                onVisibilityChanged: (info) {
                  if (info.visibleFraction > .25) {
                    controller.setPaymentDetailAnimateTarget();
                  }
                },
                child: PaymentDetailSection(skinAnalyzeId: skinAnalyzeId),
              ),
              Gap.h64,
            ],
          ),
          bottomNavBar: GlassBottomNavBar(
            content: Row(
              children: [
                const BudgetEstimationWidget(),
                const Spacer(),
                CommonButton(
                  onPressed: () => context.pop(),
                  variant: ButtonVariant.outlined,
                  child: const Icon(Icons.arrow_back),
                ),
                Gap.w16,
                CommonButton(
                  isDisabled: !state.recommendation.hasValue,
                  onPressed: () {
                    controller.clearHiveData();
                    context.goNamed(Routes.welcome.name);
                  },
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSizes.w20,
                    vertical: AppSizes.h8,
                  ),
                  child: const Text('Finish'),
                ),
              ],
            ),
          ),
        ),
        if (state.isSearchTreatment)
          SearchTreatmentSection(skinAnalyzeId: skinAnalyzeId),
      ],
    );
  }
}
