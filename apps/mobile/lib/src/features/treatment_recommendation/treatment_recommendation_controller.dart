import 'dart:async';

import 'package:euromedica_aizer/src/common/data/models/entity/payment_detail/payment_detail.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/payment_detail/payment_detail_item.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/treatment_package/treatment_package_item.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:euromedica_aizer/src/common/domain/enums/sort_by.dart';
import 'package:euromedica_aizer/src/common/services/skin_analyze_service.dart';
import 'package:euromedica_aizer/src/common/services/treatment_product_service.dart';
import 'package:euromedica_aizer/src/features/budget_estimation/budget_estimation_controller.dart';
import 'package:euromedica_aizer/src/features/treatment_recommendation/treatment_recommendation_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TreatmentRecommendationController
    extends StateNotifier<TreatmentRecommendationState> {
  TreatmentRecommendationController({
    required this.skinAnalyzeService,
    required this.treatmentProductService,
    required this.skinAnalyzeId,
    required this.hiveService,
    required this.minCost,
    required this.maxCost,
  }) : super(const TreatmentRecommendationState()) {
    _init();
  }

  final SkinAnalyzeService skinAnalyzeService;
  final TreatmentProductService treatmentProductService;
  final String skinAnalyzeId;
  final HiveService hiveService;
  final int minCost;
  final int maxCost;

  List<SortBy> sortByOptions = [SortBy.score, SortBy.checklist];

  List<TreatmentPackageItem> get selectedItems =>
      state.treatmentItems.where((element) => element.isChecked).toList();

  double get selectedItemsTotal => selectedItems.fold<double>(
        0,
        (sum, item) => sum + (item.price * item.quantity),
      );

  int get showItemsCount => 5;

  TextEditingController searchController = TextEditingController();

  Timer? _searchDebounceTimer;
  final int _searchDebounceMilliseconds = 500;

  void _init() {
    _initializePaymentDetail();
    _getProducts();
    _getRecommendation();
  }

  Future<void> _getRecommendation() async {
    state = state.copyWith(recommendation: const AsyncValue.loading());

    final result =
        await skinAnalyzeService.getRecommendationFromLocal(skinAnalyzeId);
    result.when(
      success: (recommendation) {
        final treatments = recommendation.treatments ?? [];

        for (final treatment in treatments) {
          final totalPrice = state.treatmentItems.fold<double>(
            0,
            (sum, item) => sum + (item.price * item.quantity),
          );

          final item = TreatmentPackageItem(
            id: treatment.id ?? '',
            title: treatment.name ?? '',
            description: treatment.description ?? '',
            price: treatment.price ?? 0,
            thumbnailUrl: treatment.thumbnailUrl,
            mediaUrl: treatment.mediaUrl,
            categories: treatment.categories ?? [],
            concern: treatment.solvedConcerns ?? [],
            score: treatment.totalScore ?? 0,
          );

          if ((treatment.price ?? 0) + totalPrice <= maxCost) {
            state = state.copyWith(
              treatmentItems: [
                ...state.treatmentItems,
                item.copyWith(
                  quantity: 1,
                  isChecked: true,
                ),
              ],
            );
          } else {
            state = state.copyWith(
              treatmentItems: [
                ...state.treatmentItems,
                item,
              ],
            );
          }
        }

        updatePaymentDetail();

        state = state.copyWith(
          recommendation: AsyncValue.data(
            recommendation.copyWith(
              treatments: treatments,
            ),
          ),
        );
      },
      failure: (error, stackTrace) {
        state = state.copyWith(
          recommendation: AsyncValue.error(error, stackTrace),
        );
      },
    );
  }

  List<TreatmentPackageItem> getAllTreatmentItems() {
    final allTreatments = List<TreatmentPackageItem>.from(state.treatmentItems);

    if (state.sortBy == SortBy.checklist) {
      allTreatments.sort((a, b) {
        if (a.isChecked && !b.isChecked) return -1;
        if (!a.isChecked && b.isChecked) return 1;
        return b.score.compareTo(a.score);
      });
    } else if (state.sortBy == SortBy.score) {
      allTreatments.sort((a, b) => b.score.compareTo(a.score));
    }

    return allTreatments;
  }

  void _initializePaymentDetail() {
    state = state.copyWith(
      paymentDetail: const PaymentDetail(
        total: 0,
        paymentDetailList: [],
      ),
    );
  }

  void updatePaymentDetail() {
    final currentPaymentDetail = state.paymentDetail;

    final currentItems =
        List<PaymentDetailItem>.from(currentPaymentDetail.paymentDetailList)
          ..clear();

    if (state.treatmentItems.where((element) => element.isChecked).isNotEmpty) {
      for (final item
          in state.treatmentItems.where((element) => element.isChecked)) {
        currentItems.add(
          PaymentDetailItem(
            name: item.title,
            price: (item.price * item.quantity).toDouble(),
          ),
        );
      }
    }

    final filteredCurrentItems = currentItems.toSet().toList();

    // Calculate new total
    final newTotal = filteredCurrentItems.fold<double>(
      0,
      (sum, item) => sum + (item.price - (item.promo ?? 0)),
    );

    state = state.copyWith(
      paymentDetail: PaymentDetail(
        total: newTotal.toInt().toDouble(),
        paymentDetailList: filteredCurrentItems,
      ),
      treatmentItems: state.treatmentItems,
    );
  }

  void incrementItemQuantity(TreatmentPackageItem item) {
    state = state.copyWith(
      treatmentItems: state.treatmentItems.map(
        (element) {
          if (element.id == item.id) {
            return element.copyWith(
              quantity: element.quantity + 1,
              isChecked: true,
            );
          }
          return element;
        },
      ).toList(),
    );

    updatePaymentDetail();
  }

  void decrementItemQuantity(TreatmentPackageItem item) {
    state = state.copyWith(
      treatmentItems: state.treatmentItems.map((element) {
        if (element.id == item.id) {
          return element.copyWith(
            quantity: element.quantity - 1,
            isChecked: element.quantity - 1 == 0 ? false : true,
          );
        }
        return element;
      }).toList(),
    );

    updatePaymentDetail();
  }

  void toggleTreatmentItem(TreatmentPackageItem item) {
    state = state.copyWith(
      treatmentItems: state.treatmentItems.map((element) {
        if (element.id == item.id) {
          return element.copyWith(
            isChecked: !element.isChecked,
            quantity: !element.isChecked
                ? element.quantity == 0
                    ? 1
                    : element.quantity
                : 0,
          );
        }
        return element;
      }).toList(),
    );

    updatePaymentDetail();
  }

  void toggleSelectAll(bool? isSelectedAll) {
    if (isSelectedAll == null) return;

    state = state.copyWith(
      treatmentItems: state.treatmentItems.map((element) {
        if (isSelectedAll) {
          return element.copyWith(
            isChecked: true,
            quantity: element.quantity == 0 ? 1 : element.quantity,
          );
        } else {
          return element.copyWith(
            isChecked: false,
            quantity: 0,
          );
        }
      }).toList(),
    );

    updatePaymentDetail();
  }

  void updateSkincareRecommendation(List<String> selectedItems) {
    final currentPaymentDetail = state.paymentDetail;
    final skincareProducts = state.products.valueOrNull;
    final currentItems = List<PaymentDetailItem>.from(
      currentPaymentDetail.paymentDetailList,
    )..removeWhere(
        (item) =>
            skincareProducts?.map((e) => e.name).contains(item.name) ?? false,
      );

    final selectedItemsProducts = skincareProducts
            ?.where((e) => selectedItems.contains(e.name))
            .toList() ??
        [];

    currentItems.addAll(
      selectedItemsProducts.map(
        (e) => PaymentDetailItem(
          name: e.name,
          price: e.price.toDouble(),
        ),
      ),
    );

    // Calculate new total
    final newTotal = currentItems.fold<double>(
      0,
      (sum, item) => sum + (item.price - (item.promo ?? 0)),
    );

    state = state.copyWith(
      paymentDetail: PaymentDetail(
        total: newTotal,
        paymentDetailList: currentItems,
      ),
    );
  }

  void setBasicPackageAnimateTarget() {
    state = state.copyWith(
      basicPackageAnimateTarget: 1,
    );
  }

  void setAdvancedPackageAnimateTarget() {
    state = state.copyWith(
      advancedPackageAnimateTarget: 1,
    );
  }

  void setMedicalTrilogyAnimateTarget() {
    state = state.copyWith(
      medicalTrilogyAnimateTarget: 1,
    );
  }

  void setSkincareRecommendationAnimateTarget() {
    state = state.copyWith(
      skincareRecommendationAnimateTarget: 1,
    );
  }

  void setPaymentDetailAnimateTarget() {
    state = state.copyWith(
      paymentDetailAnimateTarget: 1,
    );
  }

  void toggleShowAllItems() {
    state = state.copyWith(
      showAllItems: !state.showAllItems,
    );
  }

  void setSortBy(SortBy value) {
    state = state.copyWith(
      sortBy: value,
    );
  }

  void clearHiveData() => hiveService.clearCacheData();

  void toggleSearchTreatment() {
    state = state.copyWith(
      isSearchTreatment: !state.isSearchTreatment,
    );

    if (!state.isSearchTreatment) {
      state = state.copyWith(
        treatmentProducts: const AsyncValue.data([]),
        searchTreatments: [],
      );
      searchController.clear();
    }
  }

  Future<void> searchTreatment({required String searchText}) async {
    state = state.copyWith(treatmentProducts: const AsyncValue.loading());

    _searchDebounceTimer?.cancel();

    _searchDebounceTimer = Timer(
      Duration(milliseconds: _searchDebounceMilliseconds),
      () async {
        final result =
            await treatmentProductService.searchTreatment(searchText);

        result.when(
          success: (products) {
            state =
                state.copyWith(treatmentProducts: AsyncValue.data(products));
          },
          failure: (error, stackTrace) {
            state = state.copyWith(
              treatmentProducts: AsyncValue.error(error, stackTrace),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  void addSearchTreatment(TreatmentProduct treatment) {
    state = state.copyWith(
      searchTreatments: [...state.searchTreatments, treatment],
    );
  }

  void removeSearchTreatment(TreatmentProduct treatment) {
    state = state.copyWith(
      searchTreatments: state.searchTreatments
          .where((element) => element != treatment)
          .toList(),
    );
  }

  void insertSearchTreatmentIntoRecommendation() {
    for (final treatment in state.searchTreatments) {
      if (!state.treatmentItems.any((element) => element.id == treatment.id)) {
        final item = TreatmentPackageItem(
          id: treatment.id,
          title: treatment.name,
          description: treatment.description,
          price: treatment.price,
          categories: treatment.categories,
          concern: treatment.solvedConcerns,
          quantity: 1,
          isChecked: true,
          mediaUrl: treatment.mediaUrl,
          thumbnailUrl: treatment.thumbnailUrl,
        );

        state = state.copyWith(
          treatmentItems: [...state.treatmentItems, item],
        );
      } else {
        state = state.copyWith(
          treatmentItems: state.treatmentItems
              .map(
                (element) => element.id == treatment.id
                    ? element.copyWith(
                        quantity: element.quantity + 1,
                        isChecked: true,
                      )
                    : element,
              )
              .toList(),
        );
      }

      updatePaymentDetail();
    }

    state = state.copyWith(
      searchTreatments: [],
      isSearchTreatment: false,
      treatmentProducts: const AsyncValue.data([]),
    );
  }

  Future<void> _getProducts() async {
    state = state.copyWith(products: const AsyncValue.loading());

    final result = await treatmentProductService.getProducts();
    result.when(
      success: (products) {
        state = state.copyWith(products: AsyncValue.data(products));
      },
      failure: (error, stackTrace) {
        state = state.copyWith(
          products: AsyncValue.error(error, stackTrace),
        );
      },
    );
  }
}

final treatmentRecommendationControllerProvider =
    StateNotifierProvider.autoDispose.family<TreatmentRecommendationController,
        TreatmentRecommendationState, String>(
  (ref, skinAnalyzeId) {
    final budgetState = ref.watch(budgetEstimationControllerProvider);
    return TreatmentRecommendationController(
      skinAnalyzeService: ref.read(skinAnalyzeServiceProvider),
      treatmentProductService: ref.read(treatmentProductServiceProvider),
      skinAnalyzeId: skinAnalyzeId,
      hiveService: ref.read(hiveServiceProvider),
      minCost: budgetState.budgetEstimation.minCost,
      maxCost: budgetState.budgetEstimation.maxCost,
    );
  },
);
