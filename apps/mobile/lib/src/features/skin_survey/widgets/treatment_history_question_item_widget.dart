import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/components/dropdown_search.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/survey_question_item_widget.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TreatmentHistoryQuestionItemWidget extends ConsumerWidget {
  const TreatmentHistoryQuestionItemWidget({
    required this.questionId,
    required this.questionText,
    required this.options,
    required this.skinAnalyzeId,
    required this.category,
    super.key,
    this.onSelect,
    this.showTreatmentHistory = false,
  });
  final String questionId;
  final String questionText;
  final List<Option> options;
  final void Function(Option option)? onSelect;
  final String skinAnalyzeId;
  final bool showTreatmentHistory;
  final String category;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(skinSurveyControllerProvider(skinAnalyzeId));
    final controller = ref.read(
      skinSurveyControllerProvider(skinAnalyzeId).notifier,
    );
    final questions = state.apiQuestions;
    if (questions.isEmpty) {
      return const SizedBox.shrink();
    }

    final selectedAnswerIndex = controller.getSelectedAnswerIndex(questionId);
    final childQuestions = controller.getSelectedAnswerChildQuestions(
      questionId,
      selectedAnswerIndex,
    );

    final childQuestionText =
        childQuestions.isNotEmpty ? childQuestions.first.question : null;

    return GlassContainer(
      borderRadius: BorderRadius.circular(AppSizes.r32),
      padding: EdgeInsets.symmetric(
        horizontal: AppSizes.w24,
        vertical: AppSizes.h40,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            questionText,
            style: context.heading4?.copyWith(fontWeight: FontWeight.w300),
          ),
          Text(
            'Please select one',
            style: context.p,
          ),
          Gap.h16,
          Center(
            child: Wrap(
              spacing: 20,
              runSpacing: 20,
              children: List.generate(options.length, (index) {
                final option = options[index];
                final isSelected = selectedAnswerIndex.contains(index);
                final cardColor = isSelected
                    ? context.appColors.fgPrimaryColor
                    : context.appColors.textOncolor;
                final textColor = isSelected
                    ? context.appColors.textOncolor
                    : context.appColors.textBaseColor;
                return OptionWidget(
                  cardColor: cardColor,
                  textColor: textColor,
                  onTap: () {
                    final parentQuestionAnswer = state.apiQuestions
                        .firstWhere(
                          (question) => question.parentQuestionId == questionId,
                        )
                        .parentQuestionAnswer;

                    controller
                      ..updateHaveTreatmentHistory(
                        questionText: questionText,
                        value: parentQuestionAnswer == index,
                      )
                      ..selectOption(
                        id: questionId,
                        questionText: questionText,
                        optionText: option.mainText,
                        allowMultiple: false,
                        category: category,
                      );

                    onSelect?.call(option);
                  },
                  title: option.mainText,
                  description: option.subText,
                );
              }),
            ),
          ),
          Gap.h36,
          if (childQuestions.isNotEmpty) ...[
            Text(
              'What treatments have you undergone and when did you have them?',
              style: context.heading4?.copyWith(fontWeight: FontWeight.w300),
            ),
            ...List.generate(state.treatmentHistory.length, (index) {
              final item = state.treatmentHistory[index];
              return Column(
                key: ValueKey(item.order),
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gap.h16,
                  Text(
                    'Treatment ${index + 1}',
                    style: context.large,
                  ),
                  Gap.h8,
                  Row(
                    spacing: AppSizes.w16,
                    children: [
                      Expanded(
                        child: DropdownSearch(
                          items: state.treatmentProducts
                              .map((e) => e.name)
                              .toList(),
                          itemBuilder: (context, item) => Text(item),
                          selectedItem: item.treatment,
                          onChanged: (value) {
                            if (childQuestionText == null) return;
                            controller.updateTreatmentHistoryItem(
                              id: questionId,
                              questionText: childQuestionText,
                              order: item.order,
                              treatment: value,
                            );
                          },
                        ),
                      ),
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(AppSizes.r8),
                            border: Border.all(
                              color: context.appColors.borderSubtleColor,
                            ),
                          ),
                          child: CommonDatePicker(
                            initialDate: item.date != null
                                ? DateTime.fromMillisecondsSinceEpoch(
                                    item.date!,
                                  )
                                : null,
                            key: ValueKey(item.order),
                            hint: 'Select Date',
                            onSelected: (value) {
                              if (childQuestionText == null) return;
                              controller.updateTreatmentHistoryItem(
                                id: questionId,
                                questionText: childQuestionText,
                                order: item.order,
                                date: value?.millisecondsSinceEpoch,
                              );
                            },
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: AppSizes.w12,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.zero,
                                borderSide: BorderSide(
                                  color: context.appColors.borderSubtleColor,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.zero,
                                borderSide: BorderSide(
                                  color: context.appColors.borderSubtleColor,
                                ),
                              ),
                              focusedBorder: const OutlineInputBorder(
                                borderRadius: BorderRadius.zero,
                                borderSide: BorderSide.none,
                              ),
                              suffixIcon: Icon(
                                IconoirIcons.calendar,
                                color: context.appColors.borderSubtleColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                      if (index > 0)
                        CommonButton(
                          onPressed: () {
                            if (childQuestionText == null) return;
                            controller.removeTreatmentHistoryItem(
                              item.order,
                              childQuestionText,
                              questionId,
                            );
                          },
                          variant: ButtonVariant.outlined,
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSizes.w16,
                          ),
                          height: AppSizes.h48,
                          child: Icon(
                            IconoirIcons.trash,
                            size: AppSizes.h24,
                            color: context.appColors.fgErrorColor,
                          ),
                        )
                      else
                        SizedBox(width: AppSizes.w56),
                    ],
                  ),
                ],
              );
            }),
            Gap.h16,
            Center(
              child: CommonButton(
                isStretch: false,
                onPressed: controller.addTreatmentHistoryItem,
                variant: ButtonVariant.outlined,
                padding: EdgeInsets.symmetric(
                  horizontal: AppSizes.w16,
                ),
                height: AppSizes.h48,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  spacing: AppSizes.w8,
                  children: [
                    Text(
                      'Add more Treatment',
                      style: context.large,
                    ),
                    Icon(
                      IconoirIcons.plus,
                      size: AppSizes.h20,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
