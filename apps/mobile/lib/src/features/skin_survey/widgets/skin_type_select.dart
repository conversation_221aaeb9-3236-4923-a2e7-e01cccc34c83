import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_card_option/skin_card_option.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinTypeSelect extends ConsumerWidget {
  const SkinTypeSelect({
    required this.skinAnalyzeId,
    required this.question,
    super.key,
  });

  final String skinAnalyzeId;
  final SkinSurvey question;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(
      skinSurveyControllerProvider(skinAnalyzeId).notifier,
    );
    final state = ref.watch(skinSurveyControllerProvider(skinAnalyzeId));

    final answers = question.answers;
    final questionText = question.question;

    String getOptionSkinImage(String answer) {
      switch (answer) {
        case 'Kulit tipis':
          return Assets.images.skinTypes.thin.path;
        case 'Kulit normal':
          return Assets.images.skinTypes.normal.path;
        case 'Kulit tebal':
          return Assets.images.skinTypes.thick.path;
        default:
          return Assets.images.skinTypes.thin.path;
      }
    }

    final options = answers
        .map(
          (e) => SkinCardOption(
            value: e.title,
            image: getOptionSkinImage(e.title),
            label: e.title,
            description: e.description,
          ),
        )
        .toList();

    return GlassContainer(
      borderRadius: BorderRadius.circular(AppSizes.h32),
      padding: EdgeInsets.all(AppSizes.w24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSizes.h8,
        children: [
          Text(
            questionText,
            style: context.heading4?.copyWith(
              fontWeight: FontWeight.w300,
            ),
          ),
          Text(
            'Select your issue',
            style: context.p,
          ),
          Gap.h4,
          SkinCardSelect<String>(
            options: options,
            spacing: AppSizes.w4,
            selected: state.selectedSkinTypes,
            onSelect: (value) {
              if (value != null) {
                controller.updateSelectedSkinTypes(
                  [value],
                  questionText,
                  question.id,
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
