import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_screen.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/survey_question_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:visibility_detector/visibility_detector.dart';

class DynamicQuestionWidget extends ConsumerWidget {
  const DynamicQuestionWidget({
    required this.skinAnalyzeId,
    required this.parentQuestions,
    super.key,
  });
  final String skinAnalyzeId;
  final List<SkinSurvey> parentQuestions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller =
        ref.read(skinSurveyControllerProvider(skinAnalyzeId).notifier);

    return Column(
      children: [
        ...parentQuestions.where((question) => !question.isStatic).map(
              (question) => VisibilityDetector(
                onVisibilityChanged: (info) {
                  if (info.visibleFraction > .25) {
                    controller.setQuestionAnimateTarget(
                      parentQuestions.indexOf(question),
                    );
                  }
                },
                key: Key(question.id),
                child: Container(
                  margin: EdgeInsets.only(bottom: AppSizes.h64),
                  child: SurveyQuestionItemWidget(
                    questionId: question.id,
                    questionText: question.question,
                    options: question.answers
                        .map(
                          (answer) => Option(
                            mainText: answer.title,
                            subText: answer.description,
                            imageUrl: answer.imageUrl,
                          ),
                        )
                        .toList(),
                    allowMultiple: question.isMultiple,
                    skinAnalyzeId: skinAnalyzeId,
                    category: question.category,
                  ),
                )
                    .animate(
                      target: controller.shouldAnimateQuestion(
                        parentQuestions.indexOf(question),
                      )
                          ? 1
                          : 0,
                      autoPlay: false,
                    )
                    .slideX(
                      begin: slideBegin,
                      end: slideEnd,
                      delay: delayDuration,
                      duration: animateDuration,
                      curve: animateCurve,
                    )
                    .fadeIn(
                      delay: delayDuration,
                      duration: animateDuration,
                      curve: animateCurve,
                    )
                    .blur(
                      begin: blurBegin,
                      end: blurEnd,
                      delay: delayDuration,
                      duration: animateDuration,
                      curve: animateCurve,
                    ),
              ),
            ),
        Gap.h64
      ],
    );
  }
}
