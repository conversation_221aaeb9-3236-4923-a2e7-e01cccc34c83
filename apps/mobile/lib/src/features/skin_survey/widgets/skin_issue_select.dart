import 'package:collection/collection.dart';
import 'package:euromedica_aizer/src/app/constants/skin_issue_options.dart';
import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_card_option/skin_card_option.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_concern/skin_concern.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_state.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/skin_specific_area_dialog.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/string_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinIssueSelect extends ConsumerWidget {
  const SkinIssueSelect({
    required this.skinAnalyzeId,
    required this.question,
    super.key,
  });

  final String skinAnalyzeId;
  final SkinSurvey question;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller =
        ref.read(skinSurveyControllerProvider(skinAnalyzeId).notifier);
    final state = ref.watch(skinSurveyControllerProvider(skinAnalyzeId));

    final options = skinIssueOptions.map((e) {
      if (state.selectedSkinIssues.any((element) => element.name == e.label)) {
        final value = state.selectedSkinIssues
            .firstWhere((element) => element.name == e.label);
        final selectedFaceAreas =
            value.faceAreas?.where((e) => e.isSelected).length ?? 0;
        final countSelectedFaceAreas =
            selectedFaceAreas > 3 ? 3 : selectedFaceAreas;
        return SkinCardOption<SkinConcern>(
          label: e.label,
          description: countSelectedFaceAreas > 0
              ? 'Area selected: $countSelectedFaceAreas'
              : null,
          image: e.image,
          value: value,
        );
      }
      return e;
    }).toList();

    return GlassContainer(
      borderRadius: BorderRadius.circular(AppSizes.h32),
      padding: EdgeInsets.all(AppSizes.w24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSizes.h8,
        children: [
          Text(
            question.question,
            style: context.heading4?.copyWith(
              fontWeight: FontWeight.w300,
            ),
          ),
          Text(
            'Select your issue',
            style: context.p,
          ),
          Gap.h4,
          SkinCardSelect<SkinConcern>(
            options: options,
            crossAxisCount: 4,
            childAspectRatio: 1.4,
            spacing: AppSizes.w4,
            isMultiSelect: true,
            selected: state.selectedSkinIssues,
            onSelect: (value) => _onSelectSkinConcern(
              context: context,
              state: state,
              controller: controller,
              value: value,
            ),
            onRemove: (value) async {
              if (value != null) {
                final currentIssues =
                    List<SkinConcern>.from(state.selectedSkinIssues)
                      ..removeWhere((e) => e.name == value.name);
                controller.updateSelectedSkinIssues(
                  currentIssues,
                  question.question,
                  question.id,
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Future<void> _onSelectSkinConcern({
    required BuildContext context,
    required SkinSurveyState state,
    required SkinSurveyController controller,
    SkinConcern? value,
  }) async {
    if (value == null) return;

    final currentIssues = List<SkinConcern>.from(state.selectedSkinIssues);

    final currentValue = state.selectedSkinIssues.firstWhere(
      (e) => e.name == value.name,
      orElse: () => value,
    );

    // If key === null, it means no concern selected
    if (!value.key.isNullOrEmpty) {
      final result = await showSpecificAreaSelectionDialog(
        context: context,
        faceAreas: currentValue.faceAreas ?? [],
      );

      currentIssues.removeWhere((e) => e.key.isNullOrEmpty);

      if (result != null) {
        if (result.firstWhereOrNull((e) => e.isSelected) != null) {
          final updatedValue = value.copyWith(faceAreas: result);
          controller.updateSelectedSkinIssues(
            [...currentIssues.where((e) => e.key != value.key), updatedValue],
            question.question,
            question.id,
          );
        } else {
          currentIssues.removeWhere((e) => e.name == value.name);
          controller.updateSelectedSkinIssues(
            currentIssues,
            question.question,
            question.id,
          );
        }
      }
    } else if (value.key.isNullOrEmpty) {
      currentIssues
        ..clear()
        ..add(value);
      controller.updateSelectedSkinIssues(
        currentIssues,
        question.question,
        question.id,
      );
    }
  }
}
