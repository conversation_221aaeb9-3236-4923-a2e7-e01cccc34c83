import 'package:auto_size_text/auto_size_text.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/device_size.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SurveyQuestionItemWidget extends ConsumerWidget {
  const SurveyQuestionItemWidget({
    required this.questionId,
    required this.questionText,
    required this.options,
    required this.skinAnalyzeId,
    required this.category,
    super.key,
    this.allowMultiple = false,
    this.onSelect,
  });
  final String questionId;
  final String questionText;
  final List<Option> options;
  final void Function(Option option)? onSelect;
  final bool allowMultiple;
  final String skinAnalyzeId;
  final String category;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(skinSurveyControllerProvider(skinAnalyzeId));
    final controller = ref.read(
      skinSurveyControllerProvider(skinAnalyzeId).notifier,
    );
    final questions = state.apiQuestions;
    if (questions.isEmpty) {
      return const SizedBox.shrink();
    }

    final selectedAnswerIndex = controller.getSelectedAnswerIndex(questionId);
    final childQuestions = controller.getSelectedAnswerChildQuestions(
      questionId,
      selectedAnswerIndex,
    );

    return GlassContainer(
      borderRadius: BorderRadius.circular(AppSizes.r32),
      padding: EdgeInsets.symmetric(
        horizontal: AppSizes.w24,
        vertical: AppSizes.h40,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            questionText,
            style: context.heading4?.copyWith(fontWeight: FontWeight.w300),
          ),
          Text(
            'Please select ${allowMultiple ? 'one or more' : 'one'}',
            style: context.p,
          ),
          Gap.h16,
          Center(
            child: Wrap(
              spacing: 20,
              runSpacing: 20,
              children: List.generate(options.length, (index) {
                final option = options[index];
                final isSelected = selectedAnswerIndex.contains(index);
                final cardColor = isSelected
                    ? context.appColors.fgPrimaryColor
                    : context.appColors.textOncolor;
                final textColor = isSelected
                    ? context.appColors.textOncolor
                    : context.appColors.textBaseColor;
                return OptionWidget(
                  cardColor: cardColor,
                  textColor: textColor,
                  onTap: () {
                    onSelect?.call(option);
                    controller.selectOption(
                      id: questionId,
                      questionText: questionText,
                      optionText: option.mainText,
                      allowMultiple: allowMultiple,
                      category: category,
                    );
                  },
                  title: option.mainText,
                  description: option.subText == '' ? null : option.subText,
                );
              }),
            ),
          ),
          if (childQuestions.isNotEmpty) ...[
            Gap.h32,
            ...childQuestions.map((childQuestion) {
              final childSelectedAnswers =
                  state.selectedAnswers[childQuestion.question]?.answers ?? [];

              return ChildQuestionWidget(
                childSelectedAnswers: childSelectedAnswers,
                controller: controller,
                childQuestion: childQuestion,
              );
            }),
          ],
        ],
      ),
    );
  }
}

class ChildQuestionWidget extends StatelessWidget {
  const ChildQuestionWidget({
    required this.childSelectedAnswers,
    required this.controller,
    required this.childQuestion,
    super.key,
  });

  final List<String> childSelectedAnswers;
  final SkinSurveyController controller;
  final SkinSurvey childQuestion;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: AppSizes.h24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            childQuestion.question,
            style: context.heading4?.copyWith(fontWeight: FontWeight.w300),
          ),
          Text(
            'Please select ${childQuestion.isMultiple ? 'one or more' : 'one'}',
            style: context.p,
          ),
          Gap.h16,
          Center(
            child: Wrap(
              spacing: 20,
              runSpacing: 20,
              children: List.generate(childQuestion.answers.length, (index) {
                final option = childQuestion.answers[index];
                final isSelected = childSelectedAnswers.contains(option.title);
                final cardColor = isSelected
                    ? context.appColors.fgPrimaryColor
                    : context.appColors.textOncolor;
                final textColor = isSelected
                    ? context.appColors.textOncolor
                    : context.appColors.textBaseColor;

                return OptionWidget(
                  cardColor: cardColor,
                  textColor: textColor,
                  onTap: () {
                    controller.selectOption(
                      id: childQuestion.id,
                      questionText: childQuestion.question,
                      optionText: option.title,
                      allowMultiple: childQuestion.isMultiple,
                      category: childQuestion.category,
                    );
                  },
                  title: option.title,
                  description: option.description,
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
}

class OptionWidget extends StatelessWidget {
  const OptionWidget({
    required this.cardColor,
    required this.textColor,
    required this.onTap,
    required this.title,
    this.description,
    super.key,
  });

  final Color cardColor;
  final String title;
  final String? description;
  final Color textColor;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: context.width * 0.40,
      height: (context.width * 0.48) * 0.14,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSizes.h24,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.r16),
            color: cardColor,
            boxShadow: AppShadows.shadowCard,
            border: Border.all(
              color: context.appColors.borderSubtleColor,
            ),
          ),
          child: Column(
            crossAxisAlignment: description != null
                ? CrossAxisAlignment.start
                : CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              FittedBox(
                fit: BoxFit.fitHeight,
                child: AutoSizeText(
                  title,
                  style: context.lead?.copyWith(
                    color: textColor,
                    fontSize: 18,
                  ),
                  minFontSize: 14,
                  maxLines: description != null ? 1 : 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign:
                      description != null ? TextAlign.start : TextAlign.center,
                ),
              ),
              if (description != null)
                Flexible(
                  child: AutoSizeText(
                    description ?? '',
                    style: context.p?.copyWith(
                      color: textColor,
                      fontSize: 15,
                    ),
                    maxFontSize: 15,
                    maxLines: 2,
                    textAlign: TextAlign.start,
                  ),
                )
              else
                const SizedBox.shrink(),
            ],
          ),
        ),
      ),
    );
  }
}
