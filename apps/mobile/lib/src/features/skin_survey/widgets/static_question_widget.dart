import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_screen.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/redirect_sa_result_dialog.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/skin_issue_select.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/survey_question_item_widget.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/treatment_history_question_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:visibility_detector/visibility_detector.dart';

class StaticQuestionWidget extends ConsumerWidget {
  const StaticQuestionWidget({
    required this.skinAnalyzeId,
    required this.customStaticQuestions,
    required this.parentQuestions,
    super.key,
  });

  final String skinAnalyzeId;
  final List<String> customStaticQuestions;
  final List<SkinSurvey> parentQuestions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller =
        ref.read(skinSurveyControllerProvider(skinAnalyzeId).notifier);
    final state = ref.watch(skinSurveyControllerProvider(skinAnalyzeId));

    return Column(
      children: [
        if (state.apiQuestions.any(
          (question) => question.id == customStaticQuestions[0],
        )) ...[
          ...parentQuestions
              .where(
                (question) => question.id == customStaticQuestions[0],
              )
              .map(
                (question) => SurveyQuestionItemWidget(
                  questionId: question.id,
                  questionText: question.question,
                  category: question.category,
                  onSelect: (option) {
                    if (option.mainText == question.answers.first.title) {
                      showRedirectSaResultDialog(
                        context: context,
                        questionText: question.question,
                        skinAnalyzeId: skinAnalyzeId,
                      );
                    }
                  },
                  options: question.answers
                      .map(
                        (e) => Option(
                          mainText: e.title,
                          subText: e.description,
                        ),
                      )
                      .toList(),
                  skinAnalyzeId: skinAnalyzeId,
                ),
              ),
          Gap.h64,
        ],

        // Question: Pilih jenis kulit Anda
        if (state.apiQuestions.any(
          (question) => question.id == customStaticQuestions[1],
        )) ...[
          ...parentQuestions
              .where(
                (question) => question.id == customStaticQuestions[1],
              )
              .map(
                (question) => SurveyQuestionItemWidget(
                  questionId: question.id,
                  questionText: question.question,
                  category: question.category,
                  options: question.answers
                      .map(
                        (e) => Option(
                          mainText: e.title,
                          subText: e.description,
                        ),
                      )
                      .toList(),
                  skinAnalyzeId: skinAnalyzeId,
                ),
              ),
          Gap.h64,
        ],

        // Question section:
        // Skin issue question
        if (state.apiQuestions.any(
          (question) => question.id == customStaticQuestions[2],
        )) ...[
          VisibilityDetector(
            onVisibilityChanged: (info) {
              if (info.visibleFraction > .25) {
                controller.setSkinIssueSelectAnimateTarget();
              }
            },
            key: SkinSurveyScreen.surveySkinIssueKey,
            child: SkinIssueSelect(
              skinAnalyzeId: skinAnalyzeId,
              question: parentQuestions
                  .where(
                    (question) => question.id == customStaticQuestions[2],
                  )
                  .first,
            )
                .animate(
                  target: state.skinIssueSelectAnimateTarget,
                  autoPlay: false,
                )
                .slideX(
                  begin: slideBegin,
                  end: slideEnd,
                  delay: delayDuration,
                  duration: animateDuration,
                  curve: animateCurve,
                )
                .fadeIn(
                  delay: delayDuration,
                  duration: animateDuration,
                  curve: animateCurve,
                )
                .blur(
                  begin: blurBegin,
                  end: blurEnd,
                  delay: delayDuration,
                  duration: animateDuration,
                  curve: animateCurve,
                ),
          ),
          Gap.h64,
        ],

        // Question section:
        // Do you have any specific concerns
        if (state.apiQuestions.any(
          (question) => question.id == customStaticQuestions[3],
        )) ...[
          ...parentQuestions
              .where(
                (question) => question.id == customStaticQuestions[3],
              )
              .map(
                (question) => SurveyQuestionItemWidget(
                  questionId: question.id,
                  questionText: question.question,
                  category: question.category,
                  options: question.answers
                      .map(
                        (e) => Option(
                          mainText: e.title,
                          subText: e.description,
                        ),
                      )
                      .toList(),
                  skinAnalyzeId: skinAnalyzeId,
                ),
              ),
          Gap.h64,
        ],

        // Question: Apakah anda memiliki tujuan lain?
        if (state.apiQuestions.any(
          (question) => question.id == customStaticQuestions[4],
        )) ...[
          ...parentQuestions
              .where(
                (question) => question.id == customStaticQuestions[4],
              )
              .map(
                (question) => SurveyQuestionItemWidget(
                  questionId: question.id,
                  questionText: question.question,
                  category: question.category,
                  options: question.answers
                      .map(
                        (e) => Option(
                          mainText: e.title,
                          subText: e.description,
                        ),
                      )
                      .toList(),
                  skinAnalyzeId: skinAnalyzeId,
                ),
              ),
          Gap.h64,
        ],

        // Question section:
        // Do you have any medical treatment within six months
        if (state.apiQuestions.any(
          (question) => question.id == customStaticQuestions[5],
        )) ...[
          ...parentQuestions
              .where(
                (question) => question.id == customStaticQuestions[5],
              )
              .map(
                (question) => TreatmentHistoryQuestionItemWidget(
                  questionId: question.id,
                  questionText: question.question,
                  showTreatmentHistory: state.haveTreatmentHistory,
                  category: question.category,
                  options: question.answers
                      .map(
                        (e) => Option(
                          mainText: e.title,
                          subText: e.description,
                        ),
                      )
                      .toList(),
                  skinAnalyzeId: skinAnalyzeId,
                ),
              ),
          Gap.h64,
        ]
      ],
    );
  }
}
