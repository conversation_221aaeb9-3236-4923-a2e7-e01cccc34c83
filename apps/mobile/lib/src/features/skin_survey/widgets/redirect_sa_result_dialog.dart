import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/data/models/models.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_analysis_data/skin_analysis_data.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

Future<void> showRedirectSaResultDialog({
  required BuildContext context,
  required String questionText,
  required String skinAnalyzeId,
}) {
  return showDialog<void>(
    context: context,
    builder: (context) {
      return Dialog(
        backgroundColor: Colors.transparent,
        child: _RedirectSaResultDialog(
          skinAnalyzeId: skinAnalyzeId,
          questionText: questionText,
        ),
      );
    },
  );
}

class _RedirectSaResultDialog extends ConsumerWidget {
  const _RedirectSaResultDialog({
    required this.skinAnalyzeId,
    required this.questionText,
  });

  final String skinAnalyzeId;
  final String questionText;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(skinSurveyControllerProvider(skinAnalyzeId));
    final skinconcerns = [...SkinConcern.skinConcerns];
    final topConcerns = state.topConcernsValue.valueOrNull;
    final topConcernsWithFaceAreas = skinconcerns
        .where((e) => topConcerns?.contains(e.topConcernKey) ?? false)
        .map(
          (e) => e.copyWith(
            faceAreas: FaceArea.faceAreas
                .map((e) => e.copyWith(isSelected: true))
                .toList(),
          ),
        )
        .toList();

    return Container(
      padding: EdgeInsets.all(AppSizes.w24),
      width: AppSizes.w(800),
      decoration: BoxDecoration(
        color: context.appColors.textOncolor,
        borderRadius: BorderRadius.circular(AppSizes.r12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '''We sincerely apologize, but if you are currently pregnant, you are not eligible to receive any of the treatments offered at our clinic.''',
            style: context.lead?.copyWith(
              fontWeight: FontWeight.w300,
            ),
          ),
          Gap.h16,
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final controller = ref.read(
                    skinSurveyControllerProvider(skinAnalyzeId).notifier,
                  );
                  return CommonButton(
                    onPressed: () {
                      controller.resetPregnantOrCancerAnswer(questionText);
                      Navigator.of(context).pop();
                    },
                    variant: ButtonVariant.outlined,
                    child: Text(
                      'Back',
                      style: context.p,
                    ),
                  );
                },
              ),
              Gap.w16,
              CommonButton(
                onPressed: () => {
                  Navigator.of(context).pop(),
                  context.pushNamed(
                    Routes.skinAnalysisResult.name,
                    extra: SkinAnalysisData(
                      skinAnalyzeId: skinAnalyzeId,
                      skinConcerns: topConcernsWithFaceAreas,
                      isPregnantOrCancerTreatment: true,
                    ),
                  ),
                },
                child: Text(
                  'Go to Skin Analysis Result',
                  style: context.p?.copyWith(
                    color: context.appColors.textOncolor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
