import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/user_survey_results/user_survey_results.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/user_survey_response.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_history_item/treatment_history_item.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'skin_survey_state.freezed.dart';

@freezed
class SkinSurveyState with _$SkinSurveyState {
  const factory SkinSurveyState({
    @Default([]) List<FaceArea> faceAreas,
    @Default([]) List<SkinSurvey> apiQuestions,
    @Default({}) Map<String, UserSurveyResult> selectedAnswers,
    @Default(false) bool isLoading,
    @Default(AsyncValue.data(null))
    AsyncValue<UserSurveyResponse?> submitUserSurvey,
    String? error,
    @Default(0) double skinIssueSelectAnimateTarget,
    @Default(0) double skinTypeSelectAnimateTarget,
    @Default(0) double spesificAreaAnimateTarget,
    @Default(0) int currentAnimatedQuestionIndex,
    @Default([]) List<String> selectedSkinTypes,
    @Default([]) List<SkinConcern> selectedSkinIssues,
    @Default({}) Map<String, bool> playedAnimations,
    @Default([]) List<TreatmentHistoryItem> treatmentHistory,
    @Default(false) bool haveTreatmentHistory,
    @Default([]) List<TreatmentProduct> treatmentProducts,
    @Default(AsyncData(null)) AsyncValue<List<String>?> topConcernsValue,
  }) = _SkinSurveyState;
}
