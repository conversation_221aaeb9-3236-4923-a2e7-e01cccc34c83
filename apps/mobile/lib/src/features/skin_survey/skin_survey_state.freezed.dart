// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'skin_survey_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SkinSurveyState {
  List<FaceArea> get faceAreas => throw _privateConstructorUsedError;
  List<SkinSurvey> get apiQuestions => throw _privateConstructorUsedError;
  Map<String, UserSurveyResult> get selectedAnswers =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  AsyncValue<UserSurveyResponse?> get submitUserSurvey =>
      throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  double get skinIssueSelectAnimateTarget => throw _privateConstructorUsedError;
  double get skinTypeSelectAnimateTarget => throw _privateConstructorUsedError;
  double get spesificAreaAnimateTarget => throw _privateConstructorUsedError;
  int get currentAnimatedQuestionIndex => throw _privateConstructorUsedError;
  List<String> get selectedSkinTypes => throw _privateConstructorUsedError;
  List<SkinConcern> get selectedSkinIssues =>
      throw _privateConstructorUsedError;
  Map<String, bool> get playedAnimations => throw _privateConstructorUsedError;
  List<TreatmentHistoryItem> get treatmentHistory =>
      throw _privateConstructorUsedError;
  bool get haveTreatmentHistory => throw _privateConstructorUsedError;
  List<TreatmentProduct> get treatmentProducts =>
      throw _privateConstructorUsedError;
  AsyncValue<List<String>?> get topConcernsValue =>
      throw _privateConstructorUsedError;

  /// Create a copy of SkinSurveyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SkinSurveyStateCopyWith<SkinSurveyState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkinSurveyStateCopyWith<$Res> {
  factory $SkinSurveyStateCopyWith(
          SkinSurveyState value, $Res Function(SkinSurveyState) then) =
      _$SkinSurveyStateCopyWithImpl<$Res, SkinSurveyState>;
  @useResult
  $Res call(
      {List<FaceArea> faceAreas,
      List<SkinSurvey> apiQuestions,
      Map<String, UserSurveyResult> selectedAnswers,
      bool isLoading,
      AsyncValue<UserSurveyResponse?> submitUserSurvey,
      String? error,
      double skinIssueSelectAnimateTarget,
      double skinTypeSelectAnimateTarget,
      double spesificAreaAnimateTarget,
      int currentAnimatedQuestionIndex,
      List<String> selectedSkinTypes,
      List<SkinConcern> selectedSkinIssues,
      Map<String, bool> playedAnimations,
      List<TreatmentHistoryItem> treatmentHistory,
      bool haveTreatmentHistory,
      List<TreatmentProduct> treatmentProducts,
      AsyncValue<List<String>?> topConcernsValue});
}

/// @nodoc
class _$SkinSurveyStateCopyWithImpl<$Res, $Val extends SkinSurveyState>
    implements $SkinSurveyStateCopyWith<$Res> {
  _$SkinSurveyStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SkinSurveyState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? faceAreas = null,
    Object? apiQuestions = null,
    Object? selectedAnswers = null,
    Object? isLoading = null,
    Object? submitUserSurvey = null,
    Object? error = freezed,
    Object? skinIssueSelectAnimateTarget = null,
    Object? skinTypeSelectAnimateTarget = null,
    Object? spesificAreaAnimateTarget = null,
    Object? currentAnimatedQuestionIndex = null,
    Object? selectedSkinTypes = null,
    Object? selectedSkinIssues = null,
    Object? playedAnimations = null,
    Object? treatmentHistory = null,
    Object? haveTreatmentHistory = null,
    Object? treatmentProducts = null,
    Object? topConcernsValue = null,
  }) {
    return _then(_value.copyWith(
      faceAreas: null == faceAreas
          ? _value.faceAreas
          : faceAreas // ignore: cast_nullable_to_non_nullable
              as List<FaceArea>,
      apiQuestions: null == apiQuestions
          ? _value.apiQuestions
          : apiQuestions // ignore: cast_nullable_to_non_nullable
              as List<SkinSurvey>,
      selectedAnswers: null == selectedAnswers
          ? _value.selectedAnswers
          : selectedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, UserSurveyResult>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      submitUserSurvey: null == submitUserSurvey
          ? _value.submitUserSurvey
          : submitUserSurvey // ignore: cast_nullable_to_non_nullable
              as AsyncValue<UserSurveyResponse?>,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      skinIssueSelectAnimateTarget: null == skinIssueSelectAnimateTarget
          ? _value.skinIssueSelectAnimateTarget
          : skinIssueSelectAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      skinTypeSelectAnimateTarget: null == skinTypeSelectAnimateTarget
          ? _value.skinTypeSelectAnimateTarget
          : skinTypeSelectAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      spesificAreaAnimateTarget: null == spesificAreaAnimateTarget
          ? _value.spesificAreaAnimateTarget
          : spesificAreaAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      currentAnimatedQuestionIndex: null == currentAnimatedQuestionIndex
          ? _value.currentAnimatedQuestionIndex
          : currentAnimatedQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedSkinTypes: null == selectedSkinTypes
          ? _value.selectedSkinTypes
          : selectedSkinTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedSkinIssues: null == selectedSkinIssues
          ? _value.selectedSkinIssues
          : selectedSkinIssues // ignore: cast_nullable_to_non_nullable
              as List<SkinConcern>,
      playedAnimations: null == playedAnimations
          ? _value.playedAnimations
          : playedAnimations // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      treatmentHistory: null == treatmentHistory
          ? _value.treatmentHistory
          : treatmentHistory // ignore: cast_nullable_to_non_nullable
              as List<TreatmentHistoryItem>,
      haveTreatmentHistory: null == haveTreatmentHistory
          ? _value.haveTreatmentHistory
          : haveTreatmentHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      treatmentProducts: null == treatmentProducts
          ? _value.treatmentProducts
          : treatmentProducts // ignore: cast_nullable_to_non_nullable
              as List<TreatmentProduct>,
      topConcernsValue: null == topConcernsValue
          ? _value.topConcernsValue
          : topConcernsValue // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<String>?>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SkinSurveyStateImplCopyWith<$Res>
    implements $SkinSurveyStateCopyWith<$Res> {
  factory _$$SkinSurveyStateImplCopyWith(_$SkinSurveyStateImpl value,
          $Res Function(_$SkinSurveyStateImpl) then) =
      __$$SkinSurveyStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<FaceArea> faceAreas,
      List<SkinSurvey> apiQuestions,
      Map<String, UserSurveyResult> selectedAnswers,
      bool isLoading,
      AsyncValue<UserSurveyResponse?> submitUserSurvey,
      String? error,
      double skinIssueSelectAnimateTarget,
      double skinTypeSelectAnimateTarget,
      double spesificAreaAnimateTarget,
      int currentAnimatedQuestionIndex,
      List<String> selectedSkinTypes,
      List<SkinConcern> selectedSkinIssues,
      Map<String, bool> playedAnimations,
      List<TreatmentHistoryItem> treatmentHistory,
      bool haveTreatmentHistory,
      List<TreatmentProduct> treatmentProducts,
      AsyncValue<List<String>?> topConcernsValue});
}

/// @nodoc
class __$$SkinSurveyStateImplCopyWithImpl<$Res>
    extends _$SkinSurveyStateCopyWithImpl<$Res, _$SkinSurveyStateImpl>
    implements _$$SkinSurveyStateImplCopyWith<$Res> {
  __$$SkinSurveyStateImplCopyWithImpl(
      _$SkinSurveyStateImpl _value, $Res Function(_$SkinSurveyStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SkinSurveyState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? faceAreas = null,
    Object? apiQuestions = null,
    Object? selectedAnswers = null,
    Object? isLoading = null,
    Object? submitUserSurvey = null,
    Object? error = freezed,
    Object? skinIssueSelectAnimateTarget = null,
    Object? skinTypeSelectAnimateTarget = null,
    Object? spesificAreaAnimateTarget = null,
    Object? currentAnimatedQuestionIndex = null,
    Object? selectedSkinTypes = null,
    Object? selectedSkinIssues = null,
    Object? playedAnimations = null,
    Object? treatmentHistory = null,
    Object? haveTreatmentHistory = null,
    Object? treatmentProducts = null,
    Object? topConcernsValue = null,
  }) {
    return _then(_$SkinSurveyStateImpl(
      faceAreas: null == faceAreas
          ? _value._faceAreas
          : faceAreas // ignore: cast_nullable_to_non_nullable
              as List<FaceArea>,
      apiQuestions: null == apiQuestions
          ? _value._apiQuestions
          : apiQuestions // ignore: cast_nullable_to_non_nullable
              as List<SkinSurvey>,
      selectedAnswers: null == selectedAnswers
          ? _value._selectedAnswers
          : selectedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, UserSurveyResult>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      submitUserSurvey: null == submitUserSurvey
          ? _value.submitUserSurvey
          : submitUserSurvey // ignore: cast_nullable_to_non_nullable
              as AsyncValue<UserSurveyResponse?>,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      skinIssueSelectAnimateTarget: null == skinIssueSelectAnimateTarget
          ? _value.skinIssueSelectAnimateTarget
          : skinIssueSelectAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      skinTypeSelectAnimateTarget: null == skinTypeSelectAnimateTarget
          ? _value.skinTypeSelectAnimateTarget
          : skinTypeSelectAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      spesificAreaAnimateTarget: null == spesificAreaAnimateTarget
          ? _value.spesificAreaAnimateTarget
          : spesificAreaAnimateTarget // ignore: cast_nullable_to_non_nullable
              as double,
      currentAnimatedQuestionIndex: null == currentAnimatedQuestionIndex
          ? _value.currentAnimatedQuestionIndex
          : currentAnimatedQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedSkinTypes: null == selectedSkinTypes
          ? _value._selectedSkinTypes
          : selectedSkinTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedSkinIssues: null == selectedSkinIssues
          ? _value._selectedSkinIssues
          : selectedSkinIssues // ignore: cast_nullable_to_non_nullable
              as List<SkinConcern>,
      playedAnimations: null == playedAnimations
          ? _value._playedAnimations
          : playedAnimations // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      treatmentHistory: null == treatmentHistory
          ? _value._treatmentHistory
          : treatmentHistory // ignore: cast_nullable_to_non_nullable
              as List<TreatmentHistoryItem>,
      haveTreatmentHistory: null == haveTreatmentHistory
          ? _value.haveTreatmentHistory
          : haveTreatmentHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      treatmentProducts: null == treatmentProducts
          ? _value._treatmentProducts
          : treatmentProducts // ignore: cast_nullable_to_non_nullable
              as List<TreatmentProduct>,
      topConcernsValue: null == topConcernsValue
          ? _value.topConcernsValue
          : topConcernsValue // ignore: cast_nullable_to_non_nullable
              as AsyncValue<List<String>?>,
    ));
  }
}

/// @nodoc

class _$SkinSurveyStateImpl implements _SkinSurveyState {
  const _$SkinSurveyStateImpl(
      {final List<FaceArea> faceAreas = const [],
      final List<SkinSurvey> apiQuestions = const [],
      final Map<String, UserSurveyResult> selectedAnswers = const {},
      this.isLoading = false,
      this.submitUserSurvey = const AsyncValue.data(null),
      this.error,
      this.skinIssueSelectAnimateTarget = 0,
      this.skinTypeSelectAnimateTarget = 0,
      this.spesificAreaAnimateTarget = 0,
      this.currentAnimatedQuestionIndex = 0,
      final List<String> selectedSkinTypes = const [],
      final List<SkinConcern> selectedSkinIssues = const [],
      final Map<String, bool> playedAnimations = const {},
      final List<TreatmentHistoryItem> treatmentHistory = const [],
      this.haveTreatmentHistory = false,
      final List<TreatmentProduct> treatmentProducts = const [],
      this.topConcernsValue = const AsyncData(null)})
      : _faceAreas = faceAreas,
        _apiQuestions = apiQuestions,
        _selectedAnswers = selectedAnswers,
        _selectedSkinTypes = selectedSkinTypes,
        _selectedSkinIssues = selectedSkinIssues,
        _playedAnimations = playedAnimations,
        _treatmentHistory = treatmentHistory,
        _treatmentProducts = treatmentProducts;

  final List<FaceArea> _faceAreas;
  @override
  @JsonKey()
  List<FaceArea> get faceAreas {
    if (_faceAreas is EqualUnmodifiableListView) return _faceAreas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_faceAreas);
  }

  final List<SkinSurvey> _apiQuestions;
  @override
  @JsonKey()
  List<SkinSurvey> get apiQuestions {
    if (_apiQuestions is EqualUnmodifiableListView) return _apiQuestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_apiQuestions);
  }

  final Map<String, UserSurveyResult> _selectedAnswers;
  @override
  @JsonKey()
  Map<String, UserSurveyResult> get selectedAnswers {
    if (_selectedAnswers is EqualUnmodifiableMapView) return _selectedAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_selectedAnswers);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final AsyncValue<UserSurveyResponse?> submitUserSurvey;
  @override
  final String? error;
  @override
  @JsonKey()
  final double skinIssueSelectAnimateTarget;
  @override
  @JsonKey()
  final double skinTypeSelectAnimateTarget;
  @override
  @JsonKey()
  final double spesificAreaAnimateTarget;
  @override
  @JsonKey()
  final int currentAnimatedQuestionIndex;
  final List<String> _selectedSkinTypes;
  @override
  @JsonKey()
  List<String> get selectedSkinTypes {
    if (_selectedSkinTypes is EqualUnmodifiableListView)
      return _selectedSkinTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedSkinTypes);
  }

  final List<SkinConcern> _selectedSkinIssues;
  @override
  @JsonKey()
  List<SkinConcern> get selectedSkinIssues {
    if (_selectedSkinIssues is EqualUnmodifiableListView)
      return _selectedSkinIssues;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedSkinIssues);
  }

  final Map<String, bool> _playedAnimations;
  @override
  @JsonKey()
  Map<String, bool> get playedAnimations {
    if (_playedAnimations is EqualUnmodifiableMapView) return _playedAnimations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_playedAnimations);
  }

  final List<TreatmentHistoryItem> _treatmentHistory;
  @override
  @JsonKey()
  List<TreatmentHistoryItem> get treatmentHistory {
    if (_treatmentHistory is EqualUnmodifiableListView)
      return _treatmentHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_treatmentHistory);
  }

  @override
  @JsonKey()
  final bool haveTreatmentHistory;
  final List<TreatmentProduct> _treatmentProducts;
  @override
  @JsonKey()
  List<TreatmentProduct> get treatmentProducts {
    if (_treatmentProducts is EqualUnmodifiableListView)
      return _treatmentProducts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_treatmentProducts);
  }

  @override
  @JsonKey()
  final AsyncValue<List<String>?> topConcernsValue;

  @override
  String toString() {
    return 'SkinSurveyState(faceAreas: $faceAreas, apiQuestions: $apiQuestions, selectedAnswers: $selectedAnswers, isLoading: $isLoading, submitUserSurvey: $submitUserSurvey, error: $error, skinIssueSelectAnimateTarget: $skinIssueSelectAnimateTarget, skinTypeSelectAnimateTarget: $skinTypeSelectAnimateTarget, spesificAreaAnimateTarget: $spesificAreaAnimateTarget, currentAnimatedQuestionIndex: $currentAnimatedQuestionIndex, selectedSkinTypes: $selectedSkinTypes, selectedSkinIssues: $selectedSkinIssues, playedAnimations: $playedAnimations, treatmentHistory: $treatmentHistory, haveTreatmentHistory: $haveTreatmentHistory, treatmentProducts: $treatmentProducts, topConcernsValue: $topConcernsValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SkinSurveyStateImpl &&
            const DeepCollectionEquality()
                .equals(other._faceAreas, _faceAreas) &&
            const DeepCollectionEquality()
                .equals(other._apiQuestions, _apiQuestions) &&
            const DeepCollectionEquality()
                .equals(other._selectedAnswers, _selectedAnswers) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.submitUserSurvey, submitUserSurvey) ||
                other.submitUserSurvey == submitUserSurvey) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.skinIssueSelectAnimateTarget,
                    skinIssueSelectAnimateTarget) ||
                other.skinIssueSelectAnimateTarget ==
                    skinIssueSelectAnimateTarget) &&
            (identical(other.skinTypeSelectAnimateTarget, skinTypeSelectAnimateTarget) ||
                other.skinTypeSelectAnimateTarget ==
                    skinTypeSelectAnimateTarget) &&
            (identical(other.spesificAreaAnimateTarget, spesificAreaAnimateTarget) ||
                other.spesificAreaAnimateTarget == spesificAreaAnimateTarget) &&
            (identical(other.currentAnimatedQuestionIndex,
                    currentAnimatedQuestionIndex) ||
                other.currentAnimatedQuestionIndex ==
                    currentAnimatedQuestionIndex) &&
            const DeepCollectionEquality()
                .equals(other._selectedSkinTypes, _selectedSkinTypes) &&
            const DeepCollectionEquality()
                .equals(other._selectedSkinIssues, _selectedSkinIssues) &&
            const DeepCollectionEquality()
                .equals(other._playedAnimations, _playedAnimations) &&
            const DeepCollectionEquality()
                .equals(other._treatmentHistory, _treatmentHistory) &&
            (identical(other.haveTreatmentHistory, haveTreatmentHistory) ||
                other.haveTreatmentHistory == haveTreatmentHistory) &&
            const DeepCollectionEquality()
                .equals(other._treatmentProducts, _treatmentProducts) &&
            (identical(other.topConcernsValue, topConcernsValue) ||
                other.topConcernsValue == topConcernsValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_faceAreas),
      const DeepCollectionEquality().hash(_apiQuestions),
      const DeepCollectionEquality().hash(_selectedAnswers),
      isLoading,
      submitUserSurvey,
      error,
      skinIssueSelectAnimateTarget,
      skinTypeSelectAnimateTarget,
      spesificAreaAnimateTarget,
      currentAnimatedQuestionIndex,
      const DeepCollectionEquality().hash(_selectedSkinTypes),
      const DeepCollectionEquality().hash(_selectedSkinIssues),
      const DeepCollectionEquality().hash(_playedAnimations),
      const DeepCollectionEquality().hash(_treatmentHistory),
      haveTreatmentHistory,
      const DeepCollectionEquality().hash(_treatmentProducts),
      topConcernsValue);

  /// Create a copy of SkinSurveyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SkinSurveyStateImplCopyWith<_$SkinSurveyStateImpl> get copyWith =>
      __$$SkinSurveyStateImplCopyWithImpl<_$SkinSurveyStateImpl>(
          this, _$identity);
}

abstract class _SkinSurveyState implements SkinSurveyState {
  const factory _SkinSurveyState(
          {final List<FaceArea> faceAreas,
          final List<SkinSurvey> apiQuestions,
          final Map<String, UserSurveyResult> selectedAnswers,
          final bool isLoading,
          final AsyncValue<UserSurveyResponse?> submitUserSurvey,
          final String? error,
          final double skinIssueSelectAnimateTarget,
          final double skinTypeSelectAnimateTarget,
          final double spesificAreaAnimateTarget,
          final int currentAnimatedQuestionIndex,
          final List<String> selectedSkinTypes,
          final List<SkinConcern> selectedSkinIssues,
          final Map<String, bool> playedAnimations,
          final List<TreatmentHistoryItem> treatmentHistory,
          final bool haveTreatmentHistory,
          final List<TreatmentProduct> treatmentProducts,
          final AsyncValue<List<String>?> topConcernsValue}) =
      _$SkinSurveyStateImpl;

  @override
  List<FaceArea> get faceAreas;
  @override
  List<SkinSurvey> get apiQuestions;
  @override
  Map<String, UserSurveyResult> get selectedAnswers;
  @override
  bool get isLoading;
  @override
  AsyncValue<UserSurveyResponse?> get submitUserSurvey;
  @override
  String? get error;
  @override
  double get skinIssueSelectAnimateTarget;
  @override
  double get skinTypeSelectAnimateTarget;
  @override
  double get spesificAreaAnimateTarget;
  @override
  int get currentAnimatedQuestionIndex;
  @override
  List<String> get selectedSkinTypes;
  @override
  List<SkinConcern> get selectedSkinIssues;
  @override
  Map<String, bool> get playedAnimations;
  @override
  List<TreatmentHistoryItem> get treatmentHistory;
  @override
  bool get haveTreatmentHistory;
  @override
  List<TreatmentProduct> get treatmentProducts;
  @override
  AsyncValue<List<String>?> get topConcernsValue;

  /// Create a copy of SkinSurveyState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SkinSurveyStateImplCopyWith<_$SkinSurveyStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
