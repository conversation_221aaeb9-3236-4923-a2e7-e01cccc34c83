import 'dart:async';

import 'package:collection/collection.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/user_survey_results/user_survey_results.dart';
import 'package:euromedica_aizer/src/common/data/models/requests/user_survey_request.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_history_item/treatment_history_item.dart';
import 'package:euromedica_aizer/src/common/services/skin_analyze_service.dart';
import 'package:euromedica_aizer/src/common/services/skin_survey_service.dart';
import 'package:euromedica_aizer/src/common/services/treatment_product_service.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_state.dart';
import 'package:euromedica_aizer/src/utils/datetime_format.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinSurveyController extends StateNotifier<SkinSurveyState> {
  SkinSurveyController(
    this._treatmentProductService,
    this._skinSurveyService,
    this._skinAnalyzeService,
    this._skinAnalyzeId,
  ) : super(const SkinSurveyState()) {
    _init();
    fetchSurveyQuestions();
  }

  final SkinSurveyService _skinSurveyService;
  final TreatmentProductService _treatmentProductService;
  final SkinAnalyzeService _skinAnalyzeService;
  final String _skinAnalyzeId;

  void _init() {
    state = state.copyWith(faceAreas: FaceArea.faceAreas);
    getTopConcerns();
  }

  void updateSurveyData(List<FaceArea> faceAreas) {
    state = state.copyWith(faceAreas: faceAreas);
  }

  void updateSelectedSkinTypes(
    List<String> selectedTypes,
    String questionText,
    String id,
  ) {
    state = state.copyWith(
      selectedSkinTypes: selectedTypes,
      selectedAnswers: {
        ...state.selectedAnswers,
        questionText: UserSurveyResult(
          id: id,
          question: questionText,
          answers: selectedTypes,
          category: 'informational',
        ),
      },
    );
  }

  void updateSelectedSkinIssues(
    List<SkinConcern> selectedIssues,
    String questionText,
    String id,
  ) {
    if (selectedIssues.isNotEmpty) {
      final selectedAreas = selectedIssues.map((e) {
        return e.faceAreas
            ?.where((faceArea) => faceArea.isSelected)
            .map((fa) => '${e.name} - ${fa.title}')
            .toList();
      }).toList();
      final selectedAreasString = selectedIssues.first.key == null
          // condition: force insert 'No Concern' instead of empty list
          ? [
              'No Concern',
            ]
          // condition: if selected issues is not empty
          // then set the selected areas on single list
          : selectedAreas.reduce((value, element) {
              return [...(value ?? []), ...(element ?? [])];
            });
      state = state.copyWith(
        selectedSkinIssues: selectedIssues,
        selectedAnswers: {
          ...state.selectedAnswers,
          questionText: UserSurveyResult(
            id: id,
            question: questionText,
            answers: selectedAreasString ?? [],
            category: 'contraindication',
          ),
        },
      );
      // condition: if selected issues is empty then set empty list
    } else {
      state = state.copyWith(
        selectedSkinIssues: selectedIssues,
        selectedAnswers: {
          ...state.selectedAnswers,
          questionText: UserSurveyResult(
            id: id,
            question: questionText,
            answers: [],
            category: 'informational',
          ),
        },
      );
    }
  }

  Future<void> fetchSurveyQuestions() async {
    try {
      state = state.copyWith(isLoading: true);

      final result = await _skinSurveyService.getSurveyQuestions();
      result.when(
        success: (questions) {
          state = state.copyWith(
            apiQuestions: questions,
            isLoading: false,
            error: '',
          );
        },
        failure: (error, stackTrace) {
          state = state.copyWith(
            error: error.toString(),
            isLoading: false,
          );
        },
      );

      final treatmentProducts =
          await _treatmentProductService.getTreatmentProducts();
      treatmentProducts.when(
        success: (products) {
          state = state.copyWith(
            treatmentProducts: products,
            isLoading: false,
          );
        },
        failure: (error, stackTrace) {
          state = state.copyWith(
            error: error.toString(),
            isLoading: false,
          );
        },
      );
    } on Exception catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  Future<bool> submitUserSurvey() async {
    // prepare data request
    final userSurveyRequest = UserSurveyRequest(
      skinAnalyzeId: _skinAnalyzeId,
      results: state.selectedAnswers.entries.map((e) {
        return UserSurveyResult(
          id: e.value.id,
          question: e.value.question,
          answers: e.value.answers,
          category: e.value.category,
        );
      }).toList(),
    );

    // set loading state
    state = state.copyWith(submitUserSurvey: const AsyncValue.loading());

    // submit user survey
    final result = await _skinSurveyService.submitUserSurvey(userSurveyRequest);
    return result.when(
      success: (data) {
        state = state.copyWith(submitUserSurvey: AsyncValue.data(data));
        _getSummary();
        return true;
      },
      failure: (error, stackTrace) {
        state = state.copyWith(
          submitUserSurvey: AsyncValue.error(error, stackTrace),
        );
        return false;
      },
    );
  }

  void selectOption({
    required String id,
    required String questionText,
    required String optionText,
    required String category,
    required bool allowMultiple,
  }) {
    final questions = state.apiQuestions;
    if (questions.isEmpty) return;

    final currentSelections = {...state.selectedAnswers};
    final questionSelections =
        List<String>.from(currentSelections[questionText]?.answers ?? []);

    if (allowMultiple) {
      if (questionSelections.contains(optionText)) {
        questionSelections.remove(optionText);
      } else {
        questionSelections.add(optionText);
      }
    } else {
      questionSelections
        ..clear()
        ..add(optionText);
    }

    // remove child questions if parent question is answered
    for (final question in questions) {
      if (question.parentQuestionId == id) {
        currentSelections.remove(question.question);
      }
    }

    currentSelections[questionText] = UserSurveyResult(
      id: id,
      question: questionText,
      answers: questionSelections,
      category: category,
    );

    state = state.copyWith(
      selectedAnswers: currentSelections,
    );
  }

  List<int> getSelectedAnswerIndex(String parentId) {
    final parentQuestion = state.apiQuestions.firstWhere(
      (question) => question.id == parentId,
    );
    final parentAnswers =
        state.selectedAnswers[parentQuestion.question]?.answers;
    final selectedAnswerIndex = parentAnswers?.map((answer) {
          return parentQuestion.answers
              .indexWhere((option) => option.title == answer);
        }).toList() ??
        [];
    return selectedAnswerIndex;
  }

  List<SkinSurvey> getSelectedAnswerChildQuestions(
    String parentId,
    List<int> selectedAnswerIndex,
  ) {
    final childQuestions = state.apiQuestions
        .where(
          (question) =>
              question.parentQuestionId == parentId &&
              selectedAnswerIndex.contains(question.parentQuestionAnswer),
        )
        .toList();

    return childQuestions;
  }

  void _getSummary() {
    _skinAnalyzeService.getSummary(_skinAnalyzeId);
  }

  void resetPregnantOrCancerAnswer(String questionText) {
    state = state.copyWith(
      selectedAnswers: {
        ...state.selectedAnswers,
        questionText: UserSurveyResult(
          id: '',
          question: questionText,
          answers: [],
          category: 'informational',
        ),
      },
    );
  }

  // Treatment History
  // Section start
  void updateHaveTreatmentHistory({
    required String questionText,
    bool value = false,
  }) {
    if (value) {
      final questionId = state.apiQuestions
          .firstWhere(
            (question) => question.question == questionText,
          )
          .id;
      state = state.copyWith(
        selectedAnswers: {
          ...state.selectedAnswers,
          questionText: UserSurveyResult(
            id: questionId,
            question: questionText,
            answers: [],
            category: 'informational',
          ),
        },
        haveTreatmentHistory: value,
        treatmentHistory: [
          const TreatmentHistoryItem(order: 1),
        ],
      );
    } else {
      final newSelectedAnswers = {...state.selectedAnswers}
        ..remove(questionText);
      state = state.copyWith(
        haveTreatmentHistory: value,
        selectedAnswers: newSelectedAnswers,
        treatmentHistory: [],
      );
    }
  }

  void addTreatmentHistoryItem() {
    final lastOrder = state.treatmentHistory.lastOrNull?.order ?? 0;
    state = state.copyWith(
      treatmentHistory: [
        ...state.treatmentHistory,
        TreatmentHistoryItem(order: lastOrder + 1),
      ],
    );
  }

  void updateTreatmentHistoryItem({
    required int order,
    required String id,
    required String questionText,
    int? date,
    String? treatment,
  }) {
    final updatedTreatmentHistory = state.treatmentHistory
        .map(
          (item) => item.order == order
              ? TreatmentHistoryItem(
                  order: item.order,
                  treatment: treatment ?? item.treatment,
                  date: date ?? item.date,
                )
              : item,
        )
        .toList();
    final treatmentHistoryString = updatedTreatmentHistory.map((item) {
      return '''${item.treatment ?? 'No Treatment'} - ${item.date != null ? formatDateTimeWithoutTime(item.date!) : 'No Date'}''';
    }).toList();

    state = state.copyWith(
      treatmentHistory: updatedTreatmentHistory,
      selectedAnswers: {
        ...state.selectedAnswers,
        questionText: UserSurveyResult(
          id: id,
          question: questionText,
          answers: treatmentHistoryString,
          category: 'informational',
        ),
      },
    );
  }

  void removeTreatmentHistoryItem(
    int order,
    String questionText,
    String id,
  ) {
    final updatedTreatmentHistory =
        state.treatmentHistory.where((item) => item.order != order).toList();
    final treatmentHistoryString = updatedTreatmentHistory.map((item) {
      return '''${item.treatment ?? 'No Treatment'} - ${item.date != null ? formatDateTimeWithoutTime(item.date!) : 'No Date'}''';
    }).toList();

    state = state.copyWith(
      treatmentHistory: updatedTreatmentHistory,
      selectedAnswers: {
        ...state.selectedAnswers,
        questionText: UserSurveyResult(
          id: id,
          question: questionText,
          answers: treatmentHistoryString,
          category: 'informational',
        ),
      },
    );
  }
  // Treatment History section end

  // Screen Animation
  // Section start
  void setSkinIssueSelectAnimateTarget() {
    final playedAnimations = Map<String, bool>.from(state.playedAnimations);
    if (playedAnimations['skinIssue'] != true) {
      playedAnimations['skinIssue'] = true;
      state = state.copyWith(
        skinIssueSelectAnimateTarget: 1,
        playedAnimations: playedAnimations,
      );
    }
  }

  void setSkinTypeSelectAnimateTarget() {
    final playedAnimations = Map<String, bool>.from(state.playedAnimations);
    if (playedAnimations['skinType'] != true) {
      playedAnimations['skinType'] = true;
      state = state.copyWith(
        skinTypeSelectAnimateTarget: 1,
        playedAnimations: playedAnimations,
      );
    }
  }

  void setSpesificAreaAnimateTarget() {
    final playedAnimations = Map<String, bool>.from(state.playedAnimations);
    if (playedAnimations['specificArea'] != true) {
      playedAnimations['specificArea'] = true;
      state = state.copyWith(
        spesificAreaAnimateTarget: 1,
        playedAnimations: playedAnimations,
      );
    }
  }

  void setQuestionAnimateTarget(int index) {
    final playedAnimations = Map<String, bool>.from(state.playedAnimations);
    final questionKey = 'question_$index';
    if (playedAnimations[questionKey] != true) {
      playedAnimations[questionKey] = true;
      state = state.copyWith(
        currentAnimatedQuestionIndex: index + 1,
        playedAnimations: playedAnimations,
      );
    }
  }

  void playAllAnimations() {
    final playedAnimations = Map<String, bool>.from(state.playedAnimations);
    playedAnimations['skinIssue'] = true;
    playedAnimations['skinType'] = true;
    playedAnimations['specificArea'] = true;

    // Play all question animations
    final questions = state.apiQuestions;
    for (var i = 0; i < questions.length; i++) {
      playedAnimations['question_$i'] = true;
    }

    state = state.copyWith(
      skinIssueSelectAnimateTarget: 1,
      skinTypeSelectAnimateTarget: 1,
      spesificAreaAnimateTarget: 1,
      currentAnimatedQuestionIndex: questions.length,
      playedAnimations: playedAnimations,
    );
  }

  bool shouldAnimateQuestion(int index) {
    return index <= state.currentAnimatedQuestionIndex;
  }
  // Screen Animation end

  Future<void> getTopConcerns() async {
    final result = await _skinAnalyzeService.getTopConcern(
      _skinAnalyzeId,
    );
    result.when(
      success: (concerns) {
        state = state.copyWith(topConcernsValue: AsyncData(concerns));
      },
      failure: (error, stackTrace) {
        state = state.copyWith(
          topConcernsValue: AsyncValue.error(
            error,
            stackTrace,
          ),
        );
      },
    );
  }
}

final skinSurveyControllerProvider = StateNotifierProvider.autoDispose
    .family<SkinSurveyController, SkinSurveyState, String>(
  (ref, skinAnalyzeId) => SkinSurveyController(
    ref.read(treatmentProductServiceProvider),
    ref.read(skinSurveyServiceProvider),
    ref.read(skinAnalyzeServiceProvider),
    skinAnalyzeId,
  ),
);
