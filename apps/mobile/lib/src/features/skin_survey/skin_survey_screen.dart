import 'package:collection/collection.dart';
import 'package:euromedica_aizer/src/app/constants/static_questions.dart';
import 'package:euromedica_aizer/src/app/themes/themes.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/components/error_state.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_analysis_data/skin_analysis_data.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/budget_estimation/budget_estimation_widget.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_state.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/dynamic_question_widget.dart';
import 'package:euromedica_aizer/src/features/skin_survey/widgets/static_question_widget.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/string_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final animateDuration = 300.ms;
final delayDuration = 100.ms;
const animateCurve = Curves.easeOutQuart;
const slideBegin = -0.2;
const double slideEnd = 0;
const blurBegin = Offset(10, 10);
const blurEnd = Offset.zero;

class SkinSurveyScreen extends ConsumerWidget {
  const SkinSurveyScreen({
    required this.skinAnalyzeId,
    super.key,
  });

  static const Key surveySkinIssueKey = Key('survey_skin_issue');
  static const Key surveySkinTypeKey = Key('survey_skin_type');
  static const Key surveySpesificAreaKey = Key('survey_spesific_key');

  final String skinAnalyzeId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(skinSurveyControllerProvider(skinAnalyzeId));
    final questions = state.apiQuestions;
    final isLoading = state.isLoading;
    final controller =
        ref.read(skinSurveyControllerProvider(skinAnalyzeId).notifier);

    // Filter out child questions to prevent duplication
    final parentQuestions = questions
        .where((q) => q.parentQuestionId == null)
        .sortedByCompare((q) => q.createdAt, (q1, q2) => q1.compareTo(q2))
        .sortedByCompare((q) => q.questionOrder, (q1, q2) => q1.compareTo(q2))
        .toList();

    // Check if all parent questions and their visible child questions
    // are answered
    final isAllQuestionsAnswered = _isAllQuestionsAnswered(
      state: state,
      parentQuestions: parentQuestions,
      controller: controller,
    );

    return CommonScaffold(
      appBar: const CommonTopBar(
        title: 'Step 1',
      ),
      body: Builder(
        builder: (_) {
          if (!state.error.isNullOrEmpty) {
            return CommonErrorState(
              errorMessage: 'Failed to load survey questions',
              onRetry: controller.fetchSurveyQuestions,
            );
          }

          if (isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              if (notification is ScrollEndNotification) {
                final metrics = notification.metrics;
                if (metrics.pixels >= metrics.maxScrollExtent) {
                  controller.playAllAnimations();
                }
              }
              return true;
            },
            child: ListView(
              physics: const ClampingScrollPhysics(),
              padding: EdgeInsets.symmetric(
                vertical: AppSizes.h24,
                horizontal: AppSizes.w32,
              ),
              children: [
                Column(
                  children: [
                    Text(
                      'SURVEY',
                      style: context.titleCard,
                    ),
                    Gap.h8,
                    Text(
                      '''Only 1 minute to provide a more optimal analysis result.''',
                      style: context.p,
                    ),
                  ],
                ),
                Gap.h64,

                // static questions
                StaticQuestionWidget(
                  skinAnalyzeId: skinAnalyzeId,
                  customStaticQuestions: customStaticQuestions,
                  parentQuestions: parentQuestions,
                ),

                // dynamic questions
                DynamicQuestionWidget(
                  skinAnalyzeId: skinAnalyzeId,
                  parentQuestions: parentQuestions,
                ),
              ],
            ),
          );
        },
      ),
      bottomNavBar: GlassBottomNavBar(
        content: Row(
          children: [
            const BudgetEstimationWidget(),
            const Spacer(),
            CommonButton(
              onPressed: () => context.pop(),
              variant: ButtonVariant.outlined,
              buttonStyle: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
              ),
              child: const Icon(Icons.arrow_back),
            ),
            Gap.w16,
            CommonButton(
              onPressed: () => _onTapNext(context, state, ref),
              isDisabled: !isAllQuestionsAnswered,
              isLoading: state.submitUserSurvey.isLoading,
              child: const Icon(Icons.arrow_forward),
            ),
          ],
        ),
      ),
    );
  }

  bool _isAllQuestionsAnswered({
    required SkinSurveyState state,
    required List<SkinSurvey> parentQuestions,
    required SkinSurveyController controller,
  }) {
    return parentQuestions.every((parentQuestion) {
      final parentAnswers = state.selectedAnswers[parentQuestion.question];

      // Check if parent question is answered
      if (parentAnswers?.answers.isEmpty ?? true) return false;

      // Treatment history interval validation
      if (parentQuestion.id == customStaticQuestions[5] &&
          state.haveTreatmentHistory) {
        return state.treatmentHistory.every((item) {
          return item.treatment != null && item.date != null;
        });
      }

      // Check if visible child questions are answered
      final childQuestions = controller.getSelectedAnswerChildQuestions(
        parentQuestion.id,
        controller.getSelectedAnswerIndex(parentQuestion.id),
      );

      if (childQuestions.isNotEmpty) {
        return childQuestions.every((childQuestion) {
          final childAnswers =
              state.selectedAnswers[childQuestion.question]?.answers;
          return childAnswers?.isNotEmpty ?? false;
        });
      }

      return true;
    });
  }

  Future<void> _onTapNext(
    BuildContext context,
    SkinSurveyState state,
    WidgetRef ref,
  ) async {
    final isSucceed = await ref
        .read(skinSurveyControllerProvider(skinAnalyzeId).notifier)
        .submitUserSurvey();

    if (!isSucceed && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Failed to submit survey. Please check connection and try again',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (!context.mounted) return;

    final skinAnalysisData = SkinAnalysisData(
      skinAnalyzeId: skinAnalyzeId,
      skinConcerns: _selectedSkinConcerns(state),
    );

    await context.pushNamed(
      Routes.skinAnalysisResult.name,
      extra: skinAnalysisData,
    );
  }

  List<SkinConcern> _selectedSkinConcerns(SkinSurveyState state) {
    // If key === null, it means no concern selected
    if (state.selectedSkinIssues.any((e) => e.key == null)) {
      final allFaceAreas = FaceArea.faceAreas
          .where((e) => e.key != '')
          .map((e) => e.copyWith(isSelected: true))
          .toList();
      return SkinConcern.skinConcerns
          .map((e) => e.copyWith(faceAreas: allFaceAreas))
          .toList();
    }

    final skinConcernMap = {
      for (final sc in SkinConcern.skinConcerns)
        sc.topConcernKey?.toLowerCase(): sc,
    };

    final topConcerns = state.topConcernsValue.valueOrNull
            ?.map(
              (topConcern) =>
                  skinConcernMap[topConcern.toLowerCase()]?.copyWith(
                faceAreas: FaceArea.faceAreas
                    .map((e) => e.copyWith(isSelected: true))
                    .toList(),
              ),
            )
            .whereType<SkinConcern>()
            .toList() ??
        [];

    final combined = {
      for (final sc in [...topConcerns, ...state.selectedSkinIssues])
        sc.key: sc,
    }.values.toList();

    return combined;
  }
}
