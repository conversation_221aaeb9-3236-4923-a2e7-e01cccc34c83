import 'dart:developer';

import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/sources/local/hive_service.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_aging_data/skin_aging_data.dart';
import 'package:euromedica_aizer/src/common/services/face_aging_service.dart';
import 'package:euromedica_aizer/src/features/skin_aging/skin_aging_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinAginController extends StateNotifier<SkinAgingState> {
  SkinAginController({
    required FaceAgingService faceAgingService,
    required SkinAgingData data,
    required HiveService hiveService,
  })  : _faceAgingService = faceAgingService,
        _data = data,
        _hiveService = hiveService,
        super(const SkinAgingState()) {
    init();
  }

  final FaceAgingService _faceAgingService;
  final SkinAgingData _data;
  final HiveService _hiveService;

  Future<void> init() async {
    try {
      state = state.copyWith(faceAgingValue: const AsyncValue.loading());
      final updatedSkinConcerns = await _getFaceAgingByConcern(
        _data.skinConcerns,
      );

      state = state.copyWith(
        faceAgingValue: const AsyncValue.data(null),
        skinConcerns: updatedSkinConcerns,
        selectedSkinConcern: updatedSkinConcerns.first,
      );
    } on Exception catch (e, st) {
      log('Error in SkinAginController: $e', error: e, stackTrace: st);
      state = state.copyWith(faceAgingValue: AsyncValue.error(e, st));
    }
  }

  Future<List<SkinConcern>> _getFaceAgingByConcern(
    List<SkinConcern> concerns,
  ) async {
    final result = await _faceAgingService.getSkinAgingPredictions(
      skinAnalyzeId: _data.skinAnalyze.id ?? '',
      concerns: concerns,
    );
    return result.when(
      success: (data) => data,
      failure: Future.error,
    );
  }

  Future<void> setSkinConcern(SkinConcern skinConcern) async {
    state = state.copyWith(
      selectedSkinConcern: skinConcern,
    );
  }

  Future<void> regenerate() async {
    state = state.copyWith(regenerateValue: const AsyncLoading());
    final result = await AsyncValue.guard<SkinConcern>(() async {
      final result = await _faceAgingService.regenerateSkinAgingPredictions(
        skinAnalyzeId: _data.skinAnalyze.id ?? '',
        concerns: [state.selectedSkinConcern!],
      );
      return result.when(
        success: (data) => data.firstWhere(
          (concern) => concern.key == state.selectedSkinConcern?.key,
        ),
        failure: Future.error,
      );
    });
    if (result.hasError) {
      state = state.copyWith(regenerateValue: result);
      return;
    }
    final updatedSkinConcerns = state.skinConcerns.map((concern) {
      if (concern.key == state.selectedSkinConcern?.key) {
        return result.value!;
      }
      return concern;
    }).toList();
    state = state.copyWith(
      regenerateValue: result,
      skinConcerns: updatedSkinConcerns,
      selectedSkinConcern: result.value,
    );
  }

  void clearHiveData() => _hiveService.clearCacheData();
}

final skinAgingControllerProvider = StateNotifierProvider.autoDispose
    .family<SkinAginController, SkinAgingState, SkinAgingData>((ref, data) {
  final faceAgingService = ref.read(faceAgingServiceProvider);
  final hiveService = ref.read(hiveServiceProvider);
  return SkinAginController(
    data: data,
    faceAgingService: faceAgingService,
    hiveService: hiveService,
  );
});
