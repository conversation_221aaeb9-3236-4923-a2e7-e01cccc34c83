import 'dart:convert';
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/button.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/skin_aging/widgets/skin_aging_comparison_fullscreen_dialog.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';

class SkinAgingComparisonSectionWidget extends StatelessWidget {
  const SkinAgingComparisonSectionWidget({
    required this.backgroundImageUrl,
    required this.imageBase64,
    required this.titles,
    super.key,
    this.bacgkroundColor,
    this.photoShadow,
    this.addDarkLayer = false,
  });

  final List<InlineSpan> titles;
  final String backgroundImageUrl;
  final String imageBase64;
  final Color? bacgkroundColor;
  final List<BoxShadow>? photoShadow;
  final bool addDarkLayer;

  DecorationImage _decorationImageFromBase64(String base64String) {
    final bytes = base64Decode(base64String);
    final ImageProvider imageProvider = MemoryImage(bytes);
    return DecorationImage(
      image: imageProvider,
      fit: BoxFit.cover,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        color: bacgkroundColor,
        padding: EdgeInsets.all(AppSizes.w28),
        child: Column(
          children: [
            Text.rich(
              TextSpan(children: titles),
              textAlign: TextAlign.center,
            ),
            Gap.h16,
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: context.appColors.textOncolor,
                    width: AppSizes.w4,
                  ),
                  boxShadow: photoShadow,
                  borderRadius: BorderRadius.circular(AppSizes.r36),
                ),
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(AppSizes.r36),
                        child: Stack(
                          children: [
                            CachedNetworkImage(
                              imageUrl: backgroundImageUrl,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                            BackdropFilter(
                              filter: ImageFilter.blur(
                                sigmaX: 4,
                                sigmaY: 4,
                              ),
                              child: Container(
                                color: Colors.transparent,
                              ),
                            ),
                            if (addDarkLayer)
                              Container(
                                color:
                                    context.appColors.textBaseColor.withValues(
                                  alpha: 0.25,
                                ),
                              ),
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  image:
                                      _decorationImageFromBase64(imageBase64),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      left: AppSizes.w8,
                      bottom: AppSizes.w12,
                      child: CommonButton(
                        onPressed: () {
                          showSkinAgingComparisonFullscreenDialog(
                            context,
                            titles: titles,
                            backgroundImageUrl: backgroundImageUrl,
                            imageBase64: imageBase64,
                            bacgkroundColor: bacgkroundColor,
                          );
                        },
                        padding: EdgeInsets.all(AppSizes.w8),
                        variant: ButtonVariant.outlined,
                        child: const Icon(
                          IconoirIcons.expand,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
