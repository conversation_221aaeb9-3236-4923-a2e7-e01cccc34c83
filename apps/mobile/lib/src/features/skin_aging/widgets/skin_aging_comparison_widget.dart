import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/features/skin_aging/widgets/skin_aging_comparison_section_widget.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';

class SkinAgingComparisonWidget extends StatelessWidget {
  const SkinAgingComparisonWidget({
    super.key,
    this.concern,
    this.backgroundImageUrl,
  });

  final SkinConcern? concern;
  final String? backgroundImageUrl;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(AppSizes.r24),
      child: Row(
        children: [
          SkinAgingComparisonSectionWidget(
            titles: [
              TextSpan(
                text: 'Current Skin',
                style: context.large,
              ),
              TextSpan(
                text: '\n',
                style: context.large,
              ),
              TextSpan(
                text: ' Condition',
                style: context.large,
              ),
            ],
            backgroundImageUrl: backgroundImageUrl ?? '',
            imageBase64: concern?.selectedAreaImageBase64 ?? '',
            photoShadow: AppShadows.photoShadow,
            bacgkroundColor: context.appColors.textOncolor,
          ),
          SkinAgingComparisonSectionWidget(
            titles: [
              TextSpan(
                text: 'Skin Aging ',
                style: context.large,
              ),
              const TextSpan(text: '\n'),
              TextSpan(
                text: 'Prediction ',
                style: context.large,
              ),
              TextSpan(
                text: 'without SKIN+',
                style: context.large?.copyWith(
                  color: context.appColors.fgErrorColor,
                ),
              ),
            ],
            backgroundImageUrl: backgroundImageUrl ?? '',
            imageBase64: concern?.faceAgingImageBase64 ?? '',
            photoShadow: const [
              BoxShadow(
                color: Color(0x40EF4444),
                offset: Offset(0, 2),
                blurRadius: 8,
                spreadRadius: 8,
              ),
            ],
            bacgkroundColor: context.appColors.fgErrorSurfaceColor,
            addDarkLayer: true,
          ),
          SkinAgingComparisonSectionWidget(
            titles: [
              TextSpan(
                text: 'Skin Aging',
                style: context.large,
              ),
              const TextSpan(text: '\n'),
              TextSpan(
                text: ' Prediction ',
                style: context.large,
              ),
              TextSpan(
                text: 'with SKIN+',
                style: context.large?.copyWith(
                  color: context.appColors.fgPrimaryColor,
                ),
              ),
            ],
            photoShadow: const [
              BoxShadow(
                color: Color(0x400095DA),
                offset: Offset(0, 2),
                blurRadius: 8,
                spreadRadius: 8,
              ),
            ],
            bacgkroundColor: context.appColors.fgPrimarySurfaceColor,
            backgroundImageUrl: backgroundImageUrl ?? '',
            imageBase64: concern?.faceBeautifyingImageBase64 ?? '',
          ),
        ],
      ),
    );
  }
}
