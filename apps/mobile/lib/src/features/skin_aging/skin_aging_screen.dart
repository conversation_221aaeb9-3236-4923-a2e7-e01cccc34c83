import 'package:euromedica_aizer/src/app/config/config.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/components/error_state.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_aging_data/skin_aging_data.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/features/budget_estimation/budget_estimation_widget.dart';
import 'package:euromedica_aizer/src/features/skin_aging/skin_aging_controller.dart';
import 'package:euromedica_aizer/src/features/skin_aging/widgets/skin_aging_comparison_widget.dart';
import 'package:euromedica_aizer/src/features/skin_aging/widgets/skin_aging_concern_widget.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:euromedica_aizer/src/utils/extensions/data_extension/skin_analyze_extension.dart';
import 'package:euromedica_aizer/src/utils/extensions/string_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SkinAgingScreen extends ConsumerWidget {
  const SkinAgingScreen({
    required this.skinAgingData,
    super.key,
  });

  final SkinAgingData skinAgingData;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(
      skinAgingControllerProvider(skinAgingData).notifier,
    );
    final state = ref.watch(skinAgingControllerProvider(skinAgingData));
    final createdAtFormatted = skinAgingData.skinAnalyze
            .createdAtFormatted('EEEE, d MMMM yyyy [HH:mm]') ??
        '-';

    final isPregnantOrCancerTreatment =
        skinAgingData.isPregnantOrCancerTreatment;

    void onTapNext(
      BuildContext context,
      SkinAgingData skinAgingData,
    ) {
      context.pushNamed(
        Routes.treatmentRecommendation.name,
        extra: skinAgingData.skinAnalyze,
      );

      // if user is pregnant or undergoing cancer treatment,
      // redirect to welcome screen (skip skin aging and treatment recommendation)
      if (isPregnantOrCancerTreatment) {
        controller.clearHiveData();
        context.pushReplacementNamed(Routes.welcome.name);
        return;
      }
    }

    return CommonScaffold(
      appBar: const CommonTopBar(
        title: 'Step 3',
      ),
      body: state.faceAgingValue.when(
        data: (data) {
          return ListView(
            padding: EdgeInsets.all(AppSizes.w32),
            children: [
              Column(
                children: [
                  Text(
                    'SKIN AGING PREDICTION',
                    style: context.titleCard,
                  ),
                  Gap.h8,
                  Text(
                    createdAtFormatted,
                    style: context.p,
                  ),
                ],
              ),
              Gap.h40,
              GlassContainer(
                borderRadius: BorderRadius.circular(AppSizes.r32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '''Hi, ${skinAgingData.skinAnalyze.name?.toTitleCase ?? ''}!''',
                                style: context.heading4?.copyWith(
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                              Gap.h8,
                              Row(
                                spacing: AppSizes.w24,
                                children: [
                                  Expanded(
                                    child: Text(
                                      '''Here is an illustration of your facial condition if you consistently\nundergo treatment or choose not to undergo treatment over time. ''',
                                      style: context.p,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        if ([Flavor.dev, Flavor.stg].contains(F.flavor))
                          Column(
                            children: [
                              CommonButton(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSizes.w(44),
                                ),
                                prefixIcon: Icon(
                                  Icons.cached,
                                  size: AppSizes.h16,
                                ),
                                variant: ButtonVariant.outlined,
                                onPressed: controller.regenerate,
                                isDisabled: state.regenerateValue.isLoading,
                                child: const Text('Re-Generate Skin Aging'),
                              ),
                              Gap.h8,
                              Row(
                                children: [
                                  const Icon(IconoirIcons.infoCircle),
                                  Gap.w8,
                                  Text(
                                    '''Your photo will be analyzed by AI to\npredict aging. Results may vary.''',
                                    style: context.small?.copyWith(
                                      color: context.appColors.textSubtleColor,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                      ],
                    ),
                    Gap.h24,
                    SizedBox(
                      height: AppSizes.h(450),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SkinAgingConcernWidget(
                            skinConcerns: state.skinConcerns,
                            selectedSkinConcern: state.selectedSkinConcern,
                            onSkinConcernSelected: controller.setSkinConcern,
                          ),
                          Gap.w24,
                          Expanded(
                            flex: 8,
                            child: IndexedStack(
                              index: state.selectedSkinConcern != null
                                  ? state.skinConcerns
                                      .indexOf(state.selectedSkinConcern!)
                                  : 0,
                              children: state.skinConcerns.map((skinConcern) {
                                if (skinConcern.key ==
                                        state.selectedSkinConcern?.key &&
                                    state.regenerateValue.isLoading) {
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                }
                                return SkinAgingComparisonWidget(
                                  concern: skinConcern,
                                  backgroundImageUrl: skinAgingData
                                      .skinAnalyze.skinAnalysisFace,
                                );
                              }).toList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Gap.h64,
            ],
          );
        },
        error: (error, stackTrace) => CommonErrorState(
          errorMessage: 'Failed to load skin aging prediction',
          onRetry: controller.init,
        ),
        loading: () {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      ),
      bottomNavBar: GlassBottomNavBar(
        content: Row(
          children: [
            const BudgetEstimationWidget(),
            const Spacer(),
            CommonButton(
              onPressed: () => context.pop(),
              variant: ButtonVariant.outlined,
              child: const Icon(Icons.arrow_back),
            ),
            Gap.w16,
            CommonButton(
              onPressed: () => onTapNext(context, skinAgingData),
              child: isPregnantOrCancerTreatment
                  ? const Text('Finish')
                  : const Icon(Icons.arrow_forward),
            ),
          ],
        ),
      ),
    );
  }
}
