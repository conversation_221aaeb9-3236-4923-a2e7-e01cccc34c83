import 'package:euromedica_aizer/src/common/domain/entities/user/user.dart';
import 'package:euromedica_aizer/src/common/services/auth_service.dart';
import 'package:euromedica_aizer/src/features/profile/profile_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ProfileController extends StateNotifier<ProfileState> {
  ProfileController({
    required this.authService,
  }) : super(const ProfileState()) {
    getLoggedInUser();
  }
  final AuthService authService;

  User? getLoggedInUser() {
    state = state.copyWith(
      user: const AsyncValue.loading(),
    );
    final result = authService.currentUser;
    state = state.copyWith(
      user: AsyncValue.data(result),
    );
    return result;
  }

  Future<void> logout() async {
    await authService.logout();
  }
}

final profileControllerProvider =
    StateNotifierProvider.autoDispose<ProfileController, ProfileState>(
  (ref) => ProfileController(
    authService: ref.watch(authServiceProvider),
  ),
);
