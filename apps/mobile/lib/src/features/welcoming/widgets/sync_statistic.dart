import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/domain/enums/notification_status.dart';
import 'package:euromedica_aizer/src/features/welcoming/notification/notification_controller.dart';
import 'package:euromedica_aizer/src/features/welcoming/widgets/semi_circular_bar.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SyncStatisticWidget extends ConsumerWidget {
  const SyncStatisticWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(notificationControllerProvider);

    return state.value.when(
      data: (data) {
        final successCount = data?.data
                ?.where(
                  (ms) => ms.data.status == NotificationStatus.success.value,
                )
                .length ??
            0;
        final failedCount = data?.data
                ?.where(
                  (ms) => ms.data.status == NotificationStatus.failed.value,
                )
                .length ??
            0;
        final total = data?.totalData ?? 0;

        return Container(
          padding: EdgeInsets.all(AppSizes.w24),
          height: double.infinity,
          width: double.infinity,
          decoration: BoxDecoration(
            color: context.appColors.fgBaseColor,
            borderRadius: BorderRadius.circular(AppSizes.r16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 6,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            spacing: AppSizes.w40,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: AppSizes.w(250),
                child: SemiCircularBarWidget(
                  successCount: successCount,
                  failedCount: failedCount,
                  total: total,
                ),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: AppSizes.w12,
                children: [
                  StatisticIndicatorWidget(
                    value: successCount.toString(),
                    percentage: (successCount / (total == 0 ? 1 : total) * 100)
                        .toStringAsFixed(0),
                    status: NotificationStatus.success,
                  ),
                  StatisticIndicatorWidget(
                    value: failedCount.toString(),
                    percentage: (failedCount / (total == 0 ? 1 : total) * 100)
                        .toStringAsFixed(0),
                    status: NotificationStatus.failed,
                  ),
                ],
              ),
            ],
          ),
        );
      },
      loading: () {
        return Container(
          padding: EdgeInsets.all(AppSizes.w24),
          height: double.infinity,
          width: double.infinity,
          decoration: BoxDecoration(
            color: context.appColors.fgBaseColor,
            borderRadius: BorderRadius.circular(AppSizes.r16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 6,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Center(
            child: Text('Loading...'),
          ),
        );
      },
      error: (error, stackTrace) {
        return Center(
          child: Text(error.toString()),
        );
      },
    );
  }
}

class StatisticIndicatorWidget extends StatelessWidget {
  const StatisticIndicatorWidget({
    required this.value,
    required this.status,
    required this.percentage,
    super.key,
  });

  final String value;
  final String percentage;
  final NotificationStatus status;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: AppSizes.h16,
          width: AppSizes.h16,
          decoration: BoxDecoration(
            color: status == NotificationStatus.success
                ? context.appColors.fgSuccessColor
                : context.appColors.fgErrorColor,
            borderRadius: BorderRadius.circular(
              AppSizes.r12,
            ),
          ),
        ),
        Gap.w8,
        SizedBox(
          width: AppSizes.w72,
          child: Text(status.name, style: context.small),
        ),
        Text(
          value,
          style: context.lead?.copyWith(
            color: status == NotificationStatus.success
                ? context.appColors.fgSuccessColor
                : context.appColors.fgErrorColor,
          ),
        ),
        Gap.w8,
        Text(
          '($percentage%)',
          style: context.small,
        ),
      ],
    );
  }
}
