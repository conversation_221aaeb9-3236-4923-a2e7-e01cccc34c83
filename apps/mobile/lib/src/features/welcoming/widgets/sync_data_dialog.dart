import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_size.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/common/domain/enums/notification_status.dart';
import 'package:euromedica_aizer/src/features/welcoming/notification/notification_controller.dart';
import 'package:euromedica_aizer/src/features/welcoming/welcoming_controller.dart';
import 'package:euromedica_aizer/src/features/welcoming/widgets/sync_item.dart';
import 'package:euromedica_aizer/src/features/welcoming/widgets/sync_statistic.dart';
import 'package:euromedica_aizer/src/features/welcoming/widgets/widgets.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/datetime_format.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/device_size.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_iconoir_ttf/flutter_iconoir_ttf.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

Future<dynamic> showSyncDataDialog(BuildContext context) {
  final searchController = TextEditingController();
  return showDialog(
    useSafeArea: false,
    context: context,
    barrierColor: const Color(0xFF0095DA).withAlpha(60),
    builder: (innerContext) {
      return Consumer(
        builder: (context, ref, child) {
          final state = ref.watch(welcomingControllerProvider);
          final controller = ref.read(welcomingControllerProvider.notifier);
          final notificationController =
              ref.read(notificationControllerProvider.notifier);

          final lastSyncDate =
              state.lastSync != null ? formatDateTime(state.lastSync!) : '';

          searchController.text = state.name ?? '';

          final height = context.height;

          return WelcomeDialogWidget(
            title: 'Select Data',
            subTitle: Row(
              children: [
                Expanded(
                  child: Text(
                    'Last Updated $lastSyncDate',
                    style: context.p?.copyWith(
                      color: context.appColors.textPlaceholderColor,
                    ),
                  ),
                ),
                Gap.w16,
                CommonButton(
                  key: WelcomeDialogWidget.welcomeDialogRefreshButtonKey,
                  onPressed: () {
                    controller.getAllSkinAnalyzes();
                    notificationController.getNotification();
                  },
                  isStretch: false,
                  size: ButtonSize.lg,
                  elevation: 3,
                  variant: ButtonVariant.outlined,
                  prefixIcon: const Icon(
                    IconoirIcons.refreshDouble,
                  ),
                  child: const Text(
                    'Refresh',
                  ),
                ),
              ],
            ),
            content: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(
                          height: height * 0.25,
                          child: const SyncStatisticWidget(),
                        ),
                        Gap.h12,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: context.appColors.fgBaseColor,
                                borderRadius:
                                    BorderRadius.circular(AppSizes.r8),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(25),
                                    blurRadius: 6,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              width: context.width * 0.3,
                              child: CommonTextField(
                                hintText: 'Search Name Here',
                                prefixIcon: Icon(
                                  IconoirIcons.search,
                                  size: AppSizes.h20,
                                  color: context.appColors.textSubtleColor,
                                ),
                                suffixIcon: CommonButton(
                                  onPressed: () {
                                    searchController.clear();
                                    controller.changeName('');
                                  },
                                  variant: ButtonVariant.link,
                                  child: Icon(
                                    IconoirIcons.xmark,
                                    size: AppSizes.h20,
                                    color: context.appColors.textSubtleColor,
                                  ),
                                ),
                                controller: searchController,
                                onChanged: controller.changeName,
                                decoration: const InputDecoration(
                                  contentPadding: EdgeInsets.zero,
                                  isDense: true,
                                  fillColor: Colors.transparent,
                                ),
                              ),
                            ),
                            CommonButton(
                              onPressed: () {
                                context.pushNamed(Routes.notification.name);
                              },
                              elevation: 1,
                              variant: ButtonVariant.outlined,
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    Assets.icons.pageNotificationIcon.path,
                                  ),
                                  Gap.w8,
                                  const Text('See All File Notification'),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Gap.h12,
                        SizedBox(
                          child: state.value.when(
                            data: (data) {
                              if (data!.isEmpty) {
                                return Center(
                                  child: Text(
                                    'No data available',
                                    style: context.p,
                                  ),
                                );
                              } else {
                                final sortedData = data.map((e) => e).toList()
                                  ..sort(
                                    (a, b) =>
                                        b.createdAt!.compareTo(a.createdAt!),
                                  );

                                return Column(
                                  spacing: AppSizes.h12,
                                  children: sortedData.map((e) {
                                    return SyncItemWidget(
                                      onPressed: () async {
                                        if (innerContext.mounted) {
                                          Navigator.pop(innerContext);
                                        }
                                        if (context.mounted) {
                                          await context.pushNamed(
                                            Routes.skinSurvey.name,
                                            queryParameters: {
                                              'skinAnalyzeId': e.id,
                                            },
                                          );
                                        }
                                      },
                                      name: e.name,
                                      date: e.createdAt,
                                    );
                                  }).toList(),
                                );
                              }
                            },
                            loading: () => Center(
                              child: Text('Loading...', style: context.p),
                            ),
                            error: (error, stackTrace) => Center(
                              // TODO: Handle error
                              child: Text(error.toString(), style: context.p),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Gap.h12,
                Consumer(
                  builder: (context, ref, child) {
                    final state = ref.watch(welcomingControllerProvider);
                    final controller =
                        ref.watch(welcomingControllerProvider.notifier);
                    return PaginationWidget(
                      currentPage: state.currentPage,
                      totalPages: state.totalPages,
                      onPageChanged: controller.changePage,
                      pageSize: state.pageSize,
                      onPageSizeChanged: (int? value) {
                        controller.changePageSize(value!);
                      },
                    );
                  },
                ),
              ],
            ),
          );
        },
      );
    },
  );
}

class NotificationIndicatorWidget extends StatelessWidget {
  const NotificationIndicatorWidget({
    required this.value,
    required this.status,
    required this.percentage,
    super.key,
  });

  final String value;
  final String percentage;
  final NotificationStatus status;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: AppSizes.h16,
          width: AppSizes.h16,
          decoration: BoxDecoration(
            color: status == NotificationStatus.success
                ? context.appColors.fgSuccessColor
                : context.appColors.fgErrorColor,
            borderRadius: BorderRadius.circular(
              AppSizes.r12,
            ),
          ),
        ),
        Gap.w8,
        SizedBox(
          width: AppSizes.w72,
          child: Text(status.name, style: context.small),
        ),
        Text(value,
            style: context.lead?.copyWith(
              color: status == NotificationStatus.success
                  ? context.appColors.fgSuccessColor
                  : context.appColors.fgErrorColor,
            )),
        Gap.w8,
        Text(
          '($percentage%)',
          style: context.small,
        ),
      ],
    );
  }
}
