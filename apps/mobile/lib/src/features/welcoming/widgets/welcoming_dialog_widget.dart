import 'dart:ui';

import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/button.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:flutter/material.dart';

class WelcomeDialogWidget extends StatelessWidget {
  const WelcomeDialogWidget({
    required this.content,
    required this.title,
    this.subTitle,
    super.key,
  });

  final Widget content;
  final String title;
  final Widget? subTitle;

  static const welcomeDialogCloseButtonKey = Key('welcomeDialogCloseButton');
  static const welcomeDialogRefreshButtonKey =
      Key('welcomeDialogRefreshButton');

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: AppSizes.w80,
          vertical: AppSizes.h48,
        ),
        child: Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: 6,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            padding: EdgeInsets.symmetric(
              vertical: AppSizes.h24,
              horizontal: AppSizes.w20,
            ),
            child: Column(
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: context.heading4,
                    ),
                    Gap.w16,
                    Expanded(child: subTitle ?? Container()),
                    Gap.w16,
                    CommonButton(
                      key: welcomeDialogCloseButtonKey,
                      onPressed: () => Navigator.of(context).pop(),
                      isStretch: false,
                      elevation: 3,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 14,
                        vertical: 12,
                      ),
                      variant: ButtonVariant.outlined,
                      child: const Icon(Icons.close),
                    ),
                  ],
                ),
                Gap.h12,
                Expanded(child: content),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
