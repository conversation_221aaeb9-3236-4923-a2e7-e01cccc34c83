import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/features/welcoming/welcoming_controller.dart';
import 'package:euromedica_aizer/src/features/welcoming/widgets/widgets.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class WelcomingScreen extends ConsumerWidget {
  const WelcomingScreen({super.key});
  static const Key startAnalyzeButton = Key('startAnalyzeButton');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(welcomingControllerProvider);
    final controller = ref.read(welcomingControllerProvider.notifier);
    return CommonScaffold(
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Assets.images.welcomingScreenFace
                    .image(fit: BoxFit.fitWidth),
              ),
            ),
            Gap.w20,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Assets.images.logo.image(height: AppSizes.h64),
                      GestureDetector(
                        onTap: () => context.pushNamed(Routes.profile.name),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(100),
                                color: context.primaryColor,
                              ),
                              padding: const EdgeInsets.all(8),
                              child: Assets.icons.user.svg(),
                            ),
                            Gap.w4,
                            Text(state.branchName ?? '-'),
                          ],
                        ),
                      ),
                    ],
                  ),
                  GlassContainer(
                    borderRadius: BorderRadius.circular(AppSizes.r16),
                    padding: EdgeInsets.symmetric(
                      vertical: AppSizes.h40,
                      horizontal: AppSizes.w24,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          'Welcome to',
                          style: context.heading3,
                        ),
                        Text(
                          'AIZER',
                          style: context.heading1
                              ?.copyWith(color: context.primaryColor),
                        ),
                        Gap.h32,
                        Text(
                          '''AIZER will analyze and create the best treatment plan for you.''',
                          style: context.lead?.copyWith(color: Colors.grey),
                        ),
                        Gap.h32,
                        CommonButton(
                          key: WelcomingScreen.startAnalyzeButton,
                          child: const Text(
                            'Start Analyze',
                          ),
                          onPressed: () {
                            showSyncDataDialog(context);
                            controller.getAllSkinAnalyzes();
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox.shrink(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
