import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/common/components/components.dart';
import 'package:euromedica_aizer/src/common/domain/entities/machine_sync_log/machine_sync_log.dart';
import 'package:euromedica_aizer/src/common/domain/enums/button_variant.dart';
import 'package:euromedica_aizer/src/common/domain/enums/notification_status.dart';
import 'package:euromedica_aizer/src/features/welcoming/notification/notification_controller.dart';
import 'package:euromedica_aizer/src/features/welcoming/widgets/sync_statistic.dart';
import 'package:euromedica_aizer/src/routing/routes.dart';
import 'package:euromedica_aizer/src/utils/datetime_format.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/text_styles.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_colors.dart';
import 'package:euromedica_aizer/src/utils/extensions/build_context_extension/theme_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NotificationScreen extends ConsumerWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(notificationControllerProvider);

    final lastSyncDate =
        state.lastSync != null ? formatDateTime(state.lastSync!) : '';

    return CommonScaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: AppSizes.h24,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Assets.images.logo.image(height: AppSizes.h64),
                  GestureDetector(
                    onTap: () => context.pushNamed(Routes.profile.name),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            color: context.primaryColor,
                          ),
                          padding: const EdgeInsets.all(8),
                          child: Assets.icons.user.svg(),
                        ),
                        Gap.w4,
                        Text(state.branchName ?? '-'),
                      ],
                    ),
                  ),
                ],
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: 6,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                padding: EdgeInsets.symmetric(
                  vertical: AppSizes.h24,
                  horizontal: AppSizes.w20,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  spacing: AppSizes.h16,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      spacing: AppSizes.w16,
                      children: [
                        CommonButton(
                          onPressed: () => Navigator.of(context).pop(),
                          isStretch: false,
                          variant: ButtonVariant.link,
                          child: Icon(
                            Icons.arrow_back_ios,
                            size: AppSizes.h16,
                          ),
                        ),
                        Text(
                          'Notification',
                          style: context.heading4,
                        ),
                        Expanded(
                          child: Text(
                            'Last Updated $lastSyncDate',
                            style: context.p?.copyWith(
                              color: context.appColors.textPlaceholderColor,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '${state.totalData} Notifications',
                          style: context.heading4,
                        ),
                      ],
                    ),
                    Container(
                      height: AppSizes.h(200),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: context.appColors.fgBaseColor,
                        borderRadius: BorderRadius.circular(AppSizes.r16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(25),
                            blurRadius: 6,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const SyncStatisticWidget(),
                    ),
                    ...state.value.when(
                      data: (value) {
                        final list = value?.data ?? [];
                        if (value?.data == null || list.isEmpty) {
                          return [
                            SizedBox(
                              height: AppSizes.h(140),
                              width: double.infinity,
                              child: const Center(
                                child: Text('No Data'),
                              ),
                            ),
                          ];
                        }
                        return list.map(
                          (e) => NotificationCardItem(
                            data: e,
                          ),
                        );
                      },
                      loading: () => [
                        const Text('Loading...'),
                      ],
                      error: (error, stackTrace) => [
                        Center(
                          child: Text(error.toString()),
                        ),
                      ],
                    ),
                    Gap.h12,
                    Consumer(
                      builder: (context, ref, child) {
                        final state = ref.watch(notificationControllerProvider);
                        final controller =
                            ref.watch(notificationControllerProvider.notifier);
                        return PaginationWidget(
                          currentPage: state.page,
                          totalPages: state.totalPages ?? 0,
                          onPageChanged: controller.changePage,
                          pageSize: state.pageSize,
                          onPageSizeChanged: (int? value) {
                            controller.changePageSize(value!);
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NotificationItemRow extends StatelessWidget {
  const NotificationItemRow({
    required this.label,
    required this.value,
    super.key,
  });

  final String label;
  final Widget value;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: AppSizes.w(120),
          child: Text(label),
        ),
        SizedBox(
          width: AppSizes.w8,
          child: const Text(':'),
        ),
        Expanded(
          child: value,
        ),
      ],
    );
  }
}

class NotificationCardItem extends StatelessWidget {
  const NotificationCardItem({
    required this.data,
    super.key,
  });

  final MachineSyncLog data;

  NotificationStatus get status =>
      NotificationStatus.fromName(data.data.status ?? 'success');

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSizes.r16),
        color: status == NotificationStatus.success
            ? context.appColors.fgSuccessColor.withAlpha(25)
            : context.appColors.fgErrorColor.withAlpha(25),
      ),
      padding: EdgeInsets.symmetric(
        vertical: AppSizes.h24,
        horizontal: AppSizes.w20,
      ),
      child: Column(
        children: [
          Row(
            spacing: AppSizes.w64,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  spacing: AppSizes.h16,
                  children: [
                    NotificationItemRow(
                      label: 'PDF',
                      value: Text(data.data.pdf ?? '-'),
                    ),
                    NotificationItemRow(
                      label: 'Folder Name',
                      value: Text(data.data.folderName ?? '-'),
                    ),
                    NotificationItemRow(
                      label: 'Images',
                      value: Text(
                        data.data.images?.join(', ') ?? '-',
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  spacing: AppSizes.h16,
                  children: [
                    NotificationItemRow(
                      label: 'Status',
                      value: Align(
                        alignment: Alignment.centerLeft,
                        child: Badge(
                          textColor: context.appColors.textOncolor,
                          backgroundColor: status == NotificationStatus.success
                              ? context.appColors.fgSuccessColor
                              : context.appColors.fgErrorColor,
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSizes.w12,
                            vertical: AppSizes.h4,
                          ),
                          label: Text(status.name),
                        ),
                      ),
                    ),
                    NotificationItemRow(
                      label: 'Latest Update',
                      value: Text(formatDateTime(data.createdAt)),
                    ),
                    const NotificationItemRow(
                      label: 'Branch',
                      value: Text('Blok M Plaza'),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (status == NotificationStatus.failed) ...[
            Gap.h12,
            Container(
              height: AppSizes.h48,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSizes.r8),
                border: Border.all(
                  color: context.appColors.borderDisabledColor,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Note: ',
                  ),
                  Text(data.data.message ?? ''),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
