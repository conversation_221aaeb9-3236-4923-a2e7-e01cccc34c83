import 'package:euromedica_aizer/src/common/services/auth_service.dart';
import 'package:euromedica_aizer/src/common/services/machine_sync_service.dart';
import 'package:euromedica_aizer/src/features/welcoming/notification/notification_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NotificationController extends StateNotifier<NotificationState> {
  NotificationController(this._machineSyncService, this._authService)
      : super(const NotificationState()) {
    getNotification();
  }

  final MachineSyncService _machineSyncService;
  final AuthService _authService;

  Future<void> getNotification() async {
    state = state.copyWith(
      branchName: _authService.currentUser?.name,
      value: const AsyncValue.loading(),
    );
    final response = await _machineSyncService.getAllMachineSync(
      state.page,
      state.pageSize,
    );
    response.when(
      success: (data) {
        if (!mounted) return;
        state = state.copyWith(
          value: AsyncValue.data(data),
          totalPages: data.totalPages,
          totalData: data.totalData,
          lastSync: data.lastSync,
        );
      },
      failure: (error, stackTrace) {
        state = state.copyWith(value: AsyncValue.error(error, stackTrace));
        _machineSyncService.getFromLocal();
      },
    );
  }

  void changePage(int page) {
    state = state.copyWith(page: page);
    getNotification();
  }

  void changePageSize(int pageSize) {
    state = state.copyWith(pageSize: pageSize);
    getNotification();
  }
}

final notificationControllerProvider = StateNotifierProvider.autoDispose<
    NotificationController, NotificationState>(
  (ref) => NotificationController(
    ref.read(machineSyncServiceProvider),
    ref.watch(authServiceProvider),
  ),
);
