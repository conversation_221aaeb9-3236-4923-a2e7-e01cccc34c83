import 'dart:async';

import 'package:euromedica_aizer/src/common/data/sources/local/hive_service.dart';
import 'package:euromedica_aizer/src/common/services/auth_service.dart';
import 'package:euromedica_aizer/src/common/services/skin_analyze_service.dart';
import 'package:euromedica_aizer/src/features/welcoming/welcoming_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class WelcomingController extends StateNotifier<WelcomingState> {
  WelcomingController({
    required HiveService hiveService,
    required SkinAnalyzeService skinAnalyzeService,
    required AuthService authService,
  })  : _hiveService = hiveService,
        _skinAnalyzeService = skinAnalyzeService,
        _authService = authService,
        super(const WelcomingState()) {
    getAllSkinAnalyzes();
  }

  final SkinAnalyzeService _skinAnalyzeService;
  final HiveService _hiveService;
  final AuthService _authService;
  Timer? _debounceTimer;
  final int _debounceMilliseconds = 800;

  Future<void> getAllSkinAnalyzes() async {
    state = state.copyWith(
      value: const AsyncValue.loading(),
      branchName: _authService.currentUser?.name,
    );
    final response = await _skinAnalyzeService.fetchAll(
      state.currentPage,
      state.pageSize,
      state.name,
    );

    response.when(
      success: (data) {
        state = state.copyWith(
          value: AsyncValue.data(data.content),
          lastSync: DateTime.now().millisecondsSinceEpoch,
          totalPages: data.totalPages,
        );
      },
      failure: (error, stackTrace) {
        state = state.copyWith(value: AsyncValue.error(error, stackTrace));
        getFromLocal();
      },
    );
  }

  Future<void> getFromLocal() async {
    state = state.copyWith(value: const AsyncValue.loading());
    final response = await _skinAnalyzeService.fetchAllFromLocal();
    response.when(
      success: (data) {
        state = state.copyWith(
          value: AsyncValue.data(data),
          lastSync: _hiveService.skinAnalyzeLastSync,
        );
      },
      failure: (error, stackTrace) {
        state = state.copyWith(value: AsyncValue.error(error, stackTrace));
      },
    );
  }

  void changePage(int page) {
    state = state.copyWith(currentPage: page);
    getAllSkinAnalyzes();
  }

  void changePageSize(int pageSize) {
    state = state.copyWith(pageSize: pageSize);
    getAllSkinAnalyzes();
  }

  Future<void> clearCacheData() => _hiveService.clearCacheData();

  void changeName(String name) {
    // Cancel any existing timer
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer?.cancel();
    }

    // Update the state with the new name immediately
    state = state.copyWith(name: name);

    // Set a new timer
    _debounceTimer = Timer(
      Duration(milliseconds: _debounceMilliseconds),
      getAllSkinAnalyzes,
    );
  }
}

final welcomingControllerProvider =
    StateNotifierProvider.autoDispose<WelcomingController, WelcomingState>(
  (ref) => WelcomingController(
    hiveService: ref.read(hiveServiceProvider),
    skinAnalyzeService: ref.read(skinAnalyzeServiceProvider),
    authService: ref.watch(authServiceProvider),
  ),
);
