class HiveKey {
  static const String userBox = 'userBox';
  static const String user = 'user';

  static const String userTokenBox = 'userTokenBox';
  static const String userToken = 'userToken';
  static const String userRefreshToken = 'userRefreshToken';

  static const String passwordBox = 'passwordBox';
  static const String password = 'password';

  static const String encryptedBox = 'encryptedBox';

  static const String isOnboardedBox = 'isOnboardedBox';
  static const String isOnboarded = 'isOnboarded';

  static const String isInitializedBox = 'isInitializedBox';
  static const String isInitialized = 'isInitialized';

  static const String skinAnalyzeBox = 'skinAnalyzeBox';
  static const String skinAnalyzeLastSyncBox = 'skinAnalyzeLastSyncBox';
  static const String skinAnalyzeSummaryBox = 'skinAnalyzeSummaryBox';
  static const String skinAnalyzeRecommendationBox =
      'skinAnalyzeRecommendationBox';
  static const String skinAgingPredictionsBox = 'skinAgingPredictionsBox';

  static const String machineSyncBox = 'machineSyncBox';
}
