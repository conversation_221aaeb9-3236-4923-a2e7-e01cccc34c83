import 'package:euromedica_aizer/src/common/data/data.dart';

// This is a list of all skin concerns that can be selected by the user
// This list is used to generate the skin concerns if user is pregnant or undergoing cancer treatment
// output:
// [
//   SkinConcern(
//     key: 'wrinkles',
//     faceAreas: FaceArea.faceAreas,
//   ),
//   SkinConcern(
//     key: 'acne',
//     faceAreas: FaceArea.faceAreas,
//   ),
//   ...
// ]

final List<SkinConcern> allSkinConcerns = SkinConcern.skinConcerns
    .map(
      (e) => SkinConcern(
        key: e.key,
        name: e.name,
        faceAreas: FaceArea.faceAreas
            .map((e) => e.copyWith(isSelected: true))
            .toList(),
      ),
    )
    .toList();
