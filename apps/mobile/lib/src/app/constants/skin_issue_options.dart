import 'package:euromedica_aizer/gen/assets.gen.dart';
import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/entity/skin_card_option/skin_card_option.dart';

final List<SkinCardOption<SkinConcern>> skinIssueOptions = [
  SkinCardOption(
    label: 'Pigmentation',
    image: Assets.images.skinConcerns.pigmentation.path,
    value: SkinConcern(
      name: 'Pigmentation',
      faceAreas: FaceArea.faceAreas,
      key: 'pigment',
    ),
  ),
  SkinCardOption(
    label: 'Pores',
    image: Assets.images.skinConcerns.pores.path,
    value: SkinConcern(
      name: 'Pores',
      faceAreas: FaceArea.faceAreas,
      key: 'pores',
    ),
  ),
  SkinCardOption(
    label: 'Scar',
    image: Assets.images.skinConcerns.scar.path,
    value: SkinConcern(
      name: 'Scar',
      faceAreas: FaceArea.faceAreas,
      key: 'scar',
    ),
  ),
  SkinCardOption(
    label: 'Redness',
    image: Assets.images.skinConcerns.sensitive.path,
    value: SkinConcern(
      name: 'Redness',
      faceAreas: FaceArea.faceAreas,
      key: 'redness',
    ),
  ),
  SkinCardOption(
    label: 'Acne',
    image: Assets.images.skinConcerns.acne.path,
    value: SkinConcern(
      name: 'Acne',
      faceAreas: FaceArea.faceAreas,
      key: 'acne',
    ),
  ),
  SkinCardOption(
    label: 'Wrinkle',
    image: Assets.images.skinConcerns.wrinkle.path,
    value: SkinConcern(
      name: 'Wrinkle',
      faceAreas: FaceArea.faceAreas,
      key: 'wrinkles',
    ),
  ),
  SkinCardOption(
    label: 'No Concern',
    image: Assets.images.skinConcerns.noConcern.path,
    value: SkinConcern(
      name: 'No Concern',
      faceAreas: FaceArea.faceAreas,
    ),
  ),
];
