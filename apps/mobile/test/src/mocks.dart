import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/repositories/machine_sync_log_repository.dart';
import 'package:euromedica_aizer/src/common/data/repositories/parameter_skin_evaluation_repository.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/common/services/face_aging_service.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

class MockSkinAnalyzeRepository extends Mock implements SkinAnalyzeRepository {}

class MockSkinSurveyRepository extends Mock implements SkinSurveyRepository {}

class MockMachineSyncLogRepository extends Mock
    implements MachineSyncLogRepository {}

class MockTreatmentProductRepository extends Mock
    implements TreatmentProductRepository {}

class MockUserSurveyRepository extends Mock implements UserSurveyRepository {}

class MockFaceAgingRepository extends Mock implements FaceAgingRepository {}

class MockParameterSkinEvaluationRepository extends Mock
    implements ParameterSkinEvaluationRepository {}

class MockHiveService extends Mock implements HiveService {}

class MockAuthApi extends Mock implements AuthApi {}

class MockGoRouter extends Mock implements GoRouter {}

class MockFaceAgingService extends Mock implements FaceAgingService {}
