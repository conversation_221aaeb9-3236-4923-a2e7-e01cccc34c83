import 'package:easy_localization/easy_localization.dart';
import 'package:euromedica_aizer/src/app/constants/constants.dart';
import 'package:euromedica_aizer/src/app/constants/static_questions.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_screen.dart';
import 'package:euromedica_aizer/src/localization/codegen_loader.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';

import '../helpers/test_helper.dart';
import 'basic_robot.dart';

class SkinSurveyRobot extends Robot {
  SkinSurveyRobot(super.tester);

  // Mock skin analyze ID to use in tests
  static const String mockSkinAnalyzeId = 'mock-skin-analyze-id';

  static List<SkinSurvey> staticQuestionMock = customStaticQuestions
      .map(
        (question) => SkinSurvey(
          id: question,
          parentQuestionId: null,
          question: question,
          isMultiple: false,
          questionOrder: 0,
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
          answers: const [
            SkinSurveyAnswer(
              title: 'Yes',
              description: 'Yes',
            ),
            SkinSurveyAnswer(
              title: 'No',
              description: 'No',
            ),
          ],
          parentQuestionAnswer: null,
          description: '',
          type: '',
          isStatic: true,
          category: '',
        ),
      )
      .toList();

  static List<SkinSurvey> dynamicQuestionMock = [
    SkinSurvey(
      id: '1',
      parentQuestionId: null,
      question: 'Are you a new user?',
      isMultiple: false,
      questionOrder: 1,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
      answers: const [
        SkinSurveyAnswer(
          title: 'Yes',
          description: 'Yes',
        ),
        SkinSurveyAnswer(
          title: 'No',
          description: 'No',
        ),
      ],
      parentQuestionAnswer: null,
      description: '',
      type: '',
      isStatic: false,
      category: '',
    ),
    SkinSurvey(
      id: '1',
      parentQuestionId: null,
      question: 'Are you pregnant?',
      isMultiple: false,
      questionOrder: 2,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
      answers: const [
        SkinSurveyAnswer(
          title: 'Yes',
          description: 'Yes',
        ),
        SkinSurveyAnswer(
          title: 'No',
          description: 'No',
        ),
      ],
      parentQuestionAnswer: null,
      description: '',
      type: '',
      isStatic: false,
      category: '',
    ),
  ];

  static List<SkinSurvey> mockSkinSurveyList = [
    // static questions
    ...staticQuestionMock,
    // dynamic questions
    ...dynamicQuestionMock,
  ];

  Future<void> pumpWidget({
    required ProviderContainer container,
    String skinAnalyzeId = mockSkinAnalyzeId,
  }) async {
    await tester.runAsync(() async {
      await EasyLocalization.ensureInitialized();
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: EasyLocalization(
            fallbackLocale: AppConstants.localeEN,
            supportedLocales: const [
              AppConstants.localeID,
              AppConstants.localeEN,
            ],
            path: AppConstants.translationsAssetPath,
            assetLoader: const CodegenLoader(),
            child: ScreenUtilInit(
              minTextAdapt: true,
              designSize: const Size(
                AppConstants.kMobileScreenWidth,
                AppConstants.kMobileScreenHeight,
              ),
              builder: (context, child) => MaterialApp(
                localizationsDelegates: context.localizationDelegates,
                theme: AppTheme.light,
                home: SkinSurveyScreen(skinAnalyzeId: skinAnalyzeId),
              ),
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();
    });
  }

  // Finds and taps the "Go to Next Step" button
  Future<void> tapNextButton() async {
    await tester.tap(find.text('Go to Next Step'));
    await tester.pumpAndSettle();
  }

  // Select an answer for a specific question
  Future<void> selectAnswer({
    required String questionText,
    required String answerText,
  }) async {
    // Find the question section
    final questionFinder = find.text(questionText);
    expect(questionFinder, findsOneWidget);

    // Find and tap the answer option within that question's context
    await tester.tap(find.text(answerText));
    await tester.pumpAndSettle();
  }

  // Verifies if a question is displayed on screen
  Future<bool> isQuestionVisible(String questionId) async {
    FlutterError.onError = ignoreOverflowErrors;
    final targetFinder = find.byKey(Key(questionId));

    final scrollable = find.byType(ListView);

    // Try to scroll until the widget appears, with animation frames
    var found = false;
    for (var i = 0; i < 20; i++) {
      await dragUntilVisible(targetFinder, scrollable);
      await tester.pump(
        const Duration(milliseconds: 200),
      ); // allow animation to complete
      if (tester.any(targetFinder)) {
        found = true;
        break;
      }
    }
    expect(targetFinder, findsOneWidget);

    return found;
  }

  Future<void> selectPregnancyQuestionYes() async {
    final targetFinder = find.text(
      SkinSurveyRobot.staticQuestionMock.first.question,
    );
    final answerFinder = find.text(
      SkinSurveyRobot.staticQuestionMock.first.answers.first.title,
    );
    await tester.tap(answerFinder.first);
    await tester.pumpAndSettle();
    expect(targetFinder, findsOneWidget);
  }

  // Checks if the next button is enabled
  bool isNextButtonEnabled() {
    final nextButtonFinder = find.text('Go to Next Step');
    expect(nextButtonFinder, findsOneWidget);

    final button = tester.widget<ElevatedButton>(
      find.ancestor(
        of: nextButtonFinder,
        matching: find.byType(ElevatedButton),
      ),
    );

    return button.onPressed != null;
  }
}
