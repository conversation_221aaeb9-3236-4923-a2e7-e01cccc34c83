import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/repositories/parameter_skin_evaluation_repository.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/common/domain/entities/face_aging/face_aging.dart';
import 'package:euromedica_aizer/src/common/services/face_aging_service.dart';
import 'package:euromedica_aizer/src/features/skin_analysis/skin_analysis_result_controller.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../mocks.dart';
import '../../robots/skin_analysis_robot.dart';

class FakeAgingRequest extends Fake implements FaceAgingRequest {}

class FakeSkinConcern extends Fake implements SkinConcern {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeAgingRequest());
    registerFallbackValue(FakeSkinConcern());
    registerFallbackValue(<SkinConcern>[]);
  });

  group('Skin analysis controller test', () {
    late MockAuthRepository authRepository;
    late MockSkinAnalyzeRepository skinAnalyzeRepository;
    late MockFaceAgingRepository faceAgingRepository;
    late MockHiveService hiveService;
    late MockParameterSkinEvaluationRepository
        parameterSkinEvaluationRepository;
    late ProviderContainer container;
    late MockFaceAgingService faceAgingService;

    const mockSkinAnalysisData = SkinAnalysisRobot.mockSkinAnalysisData;

    final skinAnalyzeRecommendation =
        SkinAnalysisRobot.skinAnalyzeRecommendation;

    final skinAnalyzeMock = SkinAnalysisRobot.skinAnalyzeMock;

    final parameterSkinEvaluationMock =
        SkinAnalysisRobot.parameterSkinEvaluationMock;

    setUp(() {
      authRepository = MockAuthRepository();
      skinAnalyzeRepository = MockSkinAnalyzeRepository();
      faceAgingRepository = MockFaceAgingRepository();
      parameterSkinEvaluationRepository =
          MockParameterSkinEvaluationRepository();
      faceAgingService = MockFaceAgingService();
      hiveService = MockHiveService();
      container = ProviderContainer(
        overrides: [
          authRepositoryProvider.overrideWithValue(authRepository),
          skinAnalyzeRepositoryProvider
              .overrideWithValue(skinAnalyzeRepository),
          faceAgingRepositoryProvider.overrideWithValue(faceAgingRepository),
          parameterSkinEvaluationRepositoryProvider
              .overrideWithValue(parameterSkinEvaluationRepository),
          hiveServiceProvider.overrideWithValue(hiveService),
          faceAgingServiceProvider.overrideWithValue(faceAgingService),
        ],
      );

      when(
        () => faceAgingService.getSkinAgingPredictionsRemote(
          skinAnalyzeId: any(named: 'skinAnalyzeId'),
          concerns: any(named: 'concerns'),
        ),
      ).thenAnswer(
        (_) async => const Result.success([
          SkinConcern(
            name: 'concern',
            key: 'concern',
            faceAreas: [
              FaceArea(
                key: 'upper',
                isSelected: true,
                title: 'Upper',
                number: 1,
                iconPaths: [],
              ),
            ],
            faceAgingImageBase64: 'mockBase64',
          ),
        ]),
      );

      when(
        () => skinAnalyzeRepository
            .getRecommendation(mockSkinAnalysisData.skinAnalyzeId),
      ).thenAnswer(
        (_) async => Result.success(skinAnalyzeRecommendation),
      );

      when(
        () => skinAnalyzeRepository.getDetail(
          mockSkinAnalysisData.skinAnalyzeId,
        ),
      ).thenAnswer(
        (_) async => Result.success(skinAnalyzeMock),
      );

      when(
        () => skinAnalyzeRepository.getSummaryFromLocal(
          mockSkinAnalysisData.skinAnalyzeId,
        ),
      ).thenAnswer(
        (_) async => const Result.success('Summary'),
      );

      when(
        () => faceAgingRepository.getFaceAgingConcern(
          skinAnalyzeId: any(named: 'skinAnalyzeId'),
          request: any(named: 'request'),
        ),
      ).thenAnswer(
        (_) async => const Result.success(
          JobFaceAgingResponse(
            id: 'jobId',
            status: 'completed',
            response: FaceAgingResponse(
              generatedImages: [
                FaceAging(
                  concern: 'concern',
                  generatedImageUrl: 'generatedImageUrl',
                  selectedAreaUrl: 'selectedAreaUrl',
                ),
              ],
            ),
          ),
        ),
      );

      when(
        () => faceAgingRepository.getSkinAgingPredictionsFromLocal(
          skinAnalyzeId: mockSkinAnalysisData.skinAnalyzeId,
        ),
      ).thenReturn(
        const Result.success([
          SkinConcern(
            name: 'concern',
            faceAreas: [],
          ),
        ]),
      );

      when(
        () => skinAnalyzeRepository.getRecommendationFromLocal(
          mockSkinAnalysisData.skinAnalyzeId,
        ),
      ).thenAnswer(
        (_) async => Result.success(skinAnalyzeRecommendation),
      );

      when(
        () => faceAgingRepository.getImageBytes(imageUrl: 'imageUrl'),
      ).thenAnswer(
        (_) async => const Result.success('imageBytes'),
      );

      when(
        () => parameterSkinEvaluationRepository.getAll(),
      ).thenAnswer(
        (_) async => Result.success(parameterSkinEvaluationMock),
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('Should fetch all functions on init', () async {
      container.read(
        skinAnalysisResultControllerProvider(mockSkinAnalysisData),
      );

      await Future<void>.delayed(Duration.zero);

      verify(
        () =>
            skinAnalyzeRepository.getDetail(mockSkinAnalysisData.skinAnalyzeId),
      ).called(1);
      verify(
        () => skinAnalyzeRepository
            .getSummaryFromLocal(mockSkinAnalysisData.skinAnalyzeId),
      ).called(1);
      verify(
        () => skinAnalyzeRepository.getRecommendation(
          mockSkinAnalysisData.skinAnalyzeId,
        ),
      ).called(1);
    });

    test('Should call getSkinAnalysisResult', () async {
      // call first time on init
      final controller = container.read(
        skinAnalysisResultControllerProvider(mockSkinAnalysisData).notifier,
      );

      // call second time
      await controller.getSkinAnalysisResult();

      await Future<void>.delayed(Duration.zero);

      verify(
        () =>
            skinAnalyzeRepository.getDetail(mockSkinAnalysisData.skinAnalyzeId),
      ).called(2);
    });

    test('Should call getSummary', () async {
      // call first time on init
      final controller = container.read(
        skinAnalysisResultControllerProvider(mockSkinAnalysisData).notifier,
      );

      // call second time
      await controller.getSummary();

      await Future<void>.delayed(Duration.zero);

      verify(
        () => skinAnalyzeRepository
            .getSummaryFromLocal(mockSkinAnalysisData.skinAnalyzeId),
      ).called(2);
    });

    test('Should call getRecommendation', () async {
      // call first time on init
      container
          .read(
            skinAnalysisResultControllerProvider(mockSkinAnalysisData).notifier,
          )
          // call second time
          .getRecommendation();

      await Future<void>.delayed(Duration.zero);

      verify(
        () => skinAnalyzeRepository.getRecommendation(
          mockSkinAnalysisData.skinAnalyzeId,
        ),
      ).called(2);
    });
  });
}
