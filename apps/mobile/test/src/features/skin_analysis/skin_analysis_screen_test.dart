import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/repositories/parameter_skin_evaluation_repository.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/common/domain/entities/face_aging/face_aging.dart';
import 'package:euromedica_aizer/src/features/skin_analysis/skin_analysis_result_screen.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:stack_trace/stack_trace.dart';

import '../../helpers/network_image_mock_helper.dart';
import '../../mocks.dart';
import '../../robots/skin_analysis_robot.dart';

void mockPathProvider() {
  const channel = MethodChannel('plugins.flutter.io/path_provider');

  TestWidgetsFlutterBinding.ensureInitialized();

  // ignore: deprecated_member_use
  channel.setMockMethodCallHandler((MethodCall methodCall) async {
    switch (methodCall.method) {
      case 'getTemporaryDirectory':
        return '/tmp';
      case 'getApplicationSupportDirectory':
        return '/tmp/app_support';
      case 'getApplicationDocumentsDirectory':
        return '/tmp/app_docs';
      default:
        return null;
    }
  });
}

void main() {
  late MockAuthRepository authRepository;
  late MockSkinAnalyzeRepository skinAnalyzeRepository;
  late MockFaceAgingRepository faceAgingRepository;
  late MockHiveService hiveService;
  late MockParameterSkinEvaluationRepository parameterSkinEvaluationRepository;
  late ProviderContainer container;
  late SkinAnalysisRobot robot;

  setUpAll(() {
    FlutterError.demangleStackTrace = (StackTrace stack) {
      // support stack_trace package
      if (stack is Chain) return stack.toTrace();
      return stack;
    };

    // Mock Path Provider
    mockPathProvider();

    // Mock SQLite
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    // Mock Dependencies
    authRepository = MockAuthRepository();
    skinAnalyzeRepository = MockSkinAnalyzeRepository();
    faceAgingRepository = MockFaceAgingRepository();
    hiveService = MockHiveService();
    parameterSkinEvaluationRepository = MockParameterSkinEvaluationRepository();
    container = ProviderContainer(
      overrides: [
        authRepositoryProvider.overrideWithValue(authRepository),
        skinAnalyzeRepositoryProvider.overrideWithValue(skinAnalyzeRepository),
        faceAgingRepositoryProvider.overrideWithValue(faceAgingRepository),
        hiveServiceProvider.overrideWithValue(hiveService),
        parameterSkinEvaluationRepositoryProvider
            .overrideWithValue(parameterSkinEvaluationRepository),
      ],
    );

    when(
      () => skinAnalyzeRepository.getRecommendation(
        SkinAnalysisRobot.mockSkinAnalysisData.skinAnalyzeId,
      ),
    ).thenAnswer(
      (_) async => Result.success(SkinAnalysisRobot.skinAnalyzeRecommendation),
    );

    when(
      () => skinAnalyzeRepository.getDetail(
        SkinAnalysisRobot.mockSkinAnalysisData.skinAnalyzeId,
      ),
    ).thenAnswer(
      (_) async => Result.success(SkinAnalysisRobot.skinAnalyzeMock),
    );

    when(
      () => skinAnalyzeRepository.getSummaryFromLocal(
        SkinAnalysisRobot.mockSkinAnalysisData.skinAnalyzeId,
      ),
    ).thenAnswer(
      (_) async => const Result.success('Summary text'),
    );

    when(
      () => faceAgingRepository.getFaceAgingConcern(
        skinAnalyzeId: SkinAnalysisRobot.mockSkinAnalysisData.skinAnalyzeId,
        request: const FaceAgingRequest(
          concerns: [],
          isBeautify: false,
        ),
      ),
    ).thenAnswer(
      (_) async => const Result.success(JobFaceAgingResponse(
        response: FaceAgingResponse(
          generatedImages: [
            FaceAging(
              concern: 'concern',
              generatedImageUrl: 'generatedImageUrl',
              selectedAreaUrl: 'selectedAreaUrl',
            ),
          ],
        ),
      )),
    );

    when(
      () => faceAgingRepository.getSkinAgingPredictionsFromLocal(
        skinAnalyzeId: SkinAnalysisRobot.mockSkinAnalysisData.skinAnalyzeId,
      ),
    ).thenReturn(
      const Result.success([
        SkinConcern(
          name: 'concern',
          faceAreas: [],
        ),
      ]),
    );

    when(
      () => skinAnalyzeRepository.getRecommendationFromLocal(
        SkinAnalysisRobot.mockSkinAnalysisData.skinAnalyzeId,
      ),
    ).thenAnswer(
      (_) async => Result.success(SkinAnalysisRobot.skinAnalyzeRecommendation),
    );

    when(
      () => faceAgingRepository.getImageBytes(imageUrl: 'imageUrl'),
    ).thenAnswer(
      (_) async => const Result.success('imageBytes'),
    );

    when(
      () => parameterSkinEvaluationRepository.getAll(),
    ).thenAnswer(
      (_) async =>
          Result.success(SkinAnalysisRobot.parameterSkinEvaluationMock),
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('Skin Analysis Screen Tests', () {
    // TODO: fix error cached image network on flutter test
    // testWidgets('Should render skin analysis screen', (tester) async {
    //   await withMockedNetworkImages(() async {
    //     robot = SkinAnalysisRobot(tester);
    //     await robot.pumpWidget(container: container);
    //     expect(find.byType(SkinAnalysisResultScreen), findsOneWidget);
    //   });
    // });

    // testWidgets('Should render user name', (tester) async {
    //   await withMockedNetworkImages(() async {
    //     robot = SkinAnalysisRobot(tester);
    //     await robot.pumpWidget(container: container);
    //     expect(
    //       find.text('Hi, ${SkinAnalysisRobot.skinAnalyzeMock.name}!'),
    //       findsOneWidget,
    //     );
    //   });
    // });

    // testWidgets('Should render score items', (tester) async {
    //   await withMockedNetworkImages(() async {
    //     robot = SkinAnalysisRobot(tester);
    //     await robot.pumpWidget(container: container);
    //     expect(find.byType(SkinResultScoreItem), findsWidgets);
    //   });

    //   // rgb pore
    //   expect(
    //     find.text(SkinAnalysisRobot.skinAnalyzeMock.rgbPore.toString()),
    //     findsOneWidget,
    //   );
    //   // rgb spot
    //   expect(
    //     find.text(SkinAnalysisRobot.skinAnalyzeMock.rgbSpot.toString()),
    //     findsOneWidget,
    //   );
    //   // rgb wrinkle
    //   expect(
    //     find.text(SkinAnalysisRobot.skinAnalyzeMock.rgbWrinkle.toString()),
    //     findsOneWidget,
    //   );
    // });

    // testWidgets('Should tap score item', (tester) async {
    //   await withMockedNetworkImages(() async {
    //     robot = SkinAnalysisRobot(tester);
    //     await robot.pumpWidget(container: container);
    //     await robot.tapScoreItem(itemName: 'RGB Pore');

    //     expect(find.byType(SAResultPopupDialog), findsOneWidget);
    //   });
    // });

    // testWidgets('Should show summary', (tester) async {
    //   await withMockedNetworkImages(() async {
    //     robot = SkinAnalysisRobot(tester);
    //     await robot.pumpWidget(container: container);
    //     await robot.scrollUntilVisible(
    //       find.byType(SkinResultSummaryWidget),
    //       find.byType(CustomScrollView),
    //     );
    //     expect(find.text('SUMMARY'), findsOneWidget);

    //     // wait for animation to finish
    //     await tester.pumpWidget(Container());
    //     await tester.pump(const Duration(seconds: 1));
    //   });
    // });

    // testWidgets('Should show comprehensive analysis result', (tester) async {
    //   await withMockedNetworkImages(() async {
    //     robot = SkinAnalysisRobot(tester);
    //     await robot.pumpWidget(container: container);
    //     await robot.scrollUntilVisible(
    //       find.byType(SkinResultSummaryWidget),
    //       find.byType(CustomScrollView),
    //     );
    //     expect(find.text('COMPREHENSIVE ANALYSIS RESULT'), findsOneWidget);

    //     // wait for animation to finish
    //     await tester.pumpWidget(Container());
    //     await tester.pump(const Duration(seconds: 1));
    //   });
    // });
  });
}
