import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/requests/user_survey_request.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/user_survey_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/sources.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_controller.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../mocks.dart';

void main() {
  group('Skin survey controller test', () {
    late MockAuthRepository authRepository;
    late MockSkinAnalyzeRepository skinAnalyzeRepository;
    late MockTreatmentProductRepository treatmentProductRepository;
    late MockUserSurveyRepository userSurveyRepository;
    late MockSkinSurveyRepository skinSurveyRepository;
    late ProviderContainer container;
    late MockHiveService hiveService;

    const skinAnalyzeId = 'skin_analyze_id';

    final skinSurveyListMock = [
      SkinSurvey(
        id: '1',
        parentQuestionId: null,
        question: 'Are you a new user?',
        isMultiple: false,
        questionOrder: 1,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        answers: const [
          SkinSurveyAnswer(
            title: 'Yes',
            description: 'Yes',
          ),
          SkinSurveyAnswer(
            title: 'No',
            description: 'No',
          ),
        ],
        parentQuestionAnswer: null,
        description: '',
        type: '',
        isStatic: false,
        category: '',
      ),
      SkinSurvey(
        id: '2',
        parentQuestionId: '1',
        question: 'Have you had any treatment for your skin condition?',
        isMultiple: false,
        questionOrder: 2,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        answers: const [
          SkinSurveyAnswer(
            title: 'Yes',
            description: 'Yes',
          ),
          SkinSurveyAnswer(
            title: 'No',
            description: 'No',
          ),
        ],
        parentQuestionAnswer: 0,
        description: '',
        type: '',
        isStatic: false,
        category: '',
      ),
      SkinSurvey(
        id: '3',
        parentQuestionId: null,
        question: 'Have you had any treatment for your skin condition?',
        isMultiple: false,
        questionOrder: 2,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        answers: const [
          SkinSurveyAnswer(
            title: 'Yes',
            description: 'Yes',
          ),
          SkinSurveyAnswer(
            title: 'No',
            description: 'No',
          ),
        ],
        parentQuestionAnswer: null,
        description: '',
        type: '',
        isStatic: false,
        category: '',
      ),
    ];

    const treatmentProductListMock = [
      TreatmentProduct(
        id: '1',
        itemCode: '1',
        name: 'Treatment Product',
        description: 'Treatment Product',
        categories: [],
        quantity: 0,
        price: 0,
        isTopRecommendation: false,
        solvedConcerns: [],
        mediaUrl: '',
        thumbnailUrl: '',
      ),
    ];

    const userSurveyRequestMock = UserSurveyRequest(
      skinAnalyzeId: skinAnalyzeId,
      results: [],
    );

    setUp(() {
      authRepository = MockAuthRepository();
      skinAnalyzeRepository = MockSkinAnalyzeRepository();
      treatmentProductRepository = MockTreatmentProductRepository();
      userSurveyRepository = MockUserSurveyRepository();
      skinSurveyRepository = MockSkinSurveyRepository();
      hiveService = MockHiveService();
      container = ProviderContainer(
        overrides: [
          authRepositoryProvider.overrideWithValue(authRepository),
          userSurveyRepositoryProvider.overrideWithValue(userSurveyRepository),
          skinAnalyzeRepositoryProvider
              .overrideWithValue(skinAnalyzeRepository),
          treatmentProductRepositoryProvider
              .overrideWithValue(treatmentProductRepository),
          skinSurveyRepositoryProvider.overrideWithValue(skinSurveyRepository),
          hiveServiceProvider.overrideWithValue(hiveService),
        ],
      );

      when(() => skinSurveyRepository.getSurveyQuestions()).thenAnswer(
        (_) => Future.value(
          Result<List<SkinSurvey>>.success(skinSurveyListMock),
        ),
      );

      when(
        () => userSurveyRepository.postUserSurvey(userSurveyRequestMock),
      ).thenAnswer(
        (_) => Future.value(
          const Result.success(
            UserSurveyResponse(
              id: '1',
              results: [],
            ),
          ),
        ),
      );

      when(() => treatmentProductRepository.getTreatmentProducts()).thenAnswer(
        (_) => Future.value(
          const Result<List<TreatmentProduct>>.success(
            treatmentProductListMock,
          ),
        ),
      );

      when(() => skinAnalyzeRepository.getSummary(skinAnalyzeId)).thenAnswer(
        (_) => Future.value(
          const Result<String>.success('Summary'),
        ),
      );

      when(() => skinAnalyzeRepository.getTopConcern(skinAnalyzeId)).thenAnswer(
        (_) => Future.value(
          const Result<List<String>>.success(['Concern 1', 'Concern 2']),
        ),
      );
    });

    tearDown(() {
      container.dispose();
    });

    // TODO: fix this test
    // test('Should call getSurveyQuestions', () async {
    //   // call on init and fetch skin survey questions
    //   container.read(
    //     skinSurveyControllerProvider(skinAnalyzeId),
    //   );

    //   // Wait for async fetch to complete
    //   await Future<void>.delayed(Duration.zero);

    //   verify(() => skinSurveyRepository.getSurveyQuestions()).called(1);
    //   verify(() => treatmentProductRepository.getTreatmentProducts()).called(1);
    //   verify(() => skinAnalyzeRepository.getSummary(skinAnalyzeId)).called(1);
    // });

    test('Should select option', () async {
      // Add a listener to prevent disposal
      void listener(SkinSurveyState? previous, SkinSurveyState next) {}

      container.listen(
        skinSurveyControllerProvider(skinAnalyzeId),
        listener,
      );

      // Now read the controller
      final controller = container.read(
        skinSurveyControllerProvider(skinAnalyzeId).notifier,
      );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      controller.selectOption(
        id: skinSurveyListMock.first.id,
        questionText: skinSurveyListMock.first.question,
        optionText: skinSurveyListMock.first.answers.first.title,
        allowMultiple: false,
        category: skinSurveyListMock.first.category,
      );

      // Read the state
      final updatedState = container.read(
        skinSurveyControllerProvider(skinAnalyzeId),
      );

      expect(
        updatedState
            .selectedAnswers[skinSurveyListMock.first.question]?.answers.first,
        skinSurveyListMock.first.answers.first.title,
      );
    });

    test('Should init treatment history with 1 item', () async {
      // Add a listener to prevent disposal
      void listener(SkinSurveyState? previous, SkinSurveyState next) {}

      container
        ..listen(
          skinSurveyControllerProvider(skinAnalyzeId),
          listener,
        )
        ..read(
          skinSurveyControllerProvider(skinAnalyzeId),
        );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      container
          .read(
            skinSurveyControllerProvider(skinAnalyzeId).notifier,
          )
          .updateHaveTreatmentHistory(
            value: true,
            questionText: 'Have you had any treatment for your skin condition?',
          );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      final updatedState = container.read(
        skinSurveyControllerProvider(skinAnalyzeId),
      );

      expect(
        updatedState.treatmentHistory.length,
        1,
      );
    });

    test('Should add treatment history item', () async {
      // Add a listener to prevent disposal
      void listener(SkinSurveyState? previous, SkinSurveyState next) {}

      container
        ..listen(
          skinSurveyControllerProvider(skinAnalyzeId),
          listener,
        )
        ..read(
          skinSurveyControllerProvider(skinAnalyzeId),
        );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      container
          .read(
            skinSurveyControllerProvider(skinAnalyzeId).notifier,
          )
          .updateHaveTreatmentHistory(
            value: true,
            questionText: 'Have you had any treatment for your skin condition?',
          );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      final updatedState = container.read(
        skinSurveyControllerProvider(skinAnalyzeId),
      );

      expect(
        updatedState.treatmentHistory.length,
        1,
      );
    });

    test('Should update treatment history', () async {
      // Add a listener to prevent disposal
      void listener(SkinSurveyState? previous, SkinSurveyState next) {}

      container
        ..listen(
          skinSurveyControllerProvider(skinAnalyzeId),
          listener,
        )
        ..read(
          skinSurveyControllerProvider(skinAnalyzeId),
        );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      container.read(
        skinSurveyControllerProvider(skinAnalyzeId).notifier,
      )
        ..updateHaveTreatmentHistory(
          value: true,
          questionText: 'Have you had any treatment for your skin condition?',
        )
        ..addTreatmentHistoryItem()
        ..selectOption(
          id: skinSurveyListMock.first.id,
          questionText: skinSurveyListMock.first.question,
          optionText: skinSurveyListMock.first.answers.first.title,
          allowMultiple: false,
          category: skinSurveyListMock.first.category,
        )
        ..updateTreatmentHistoryItem(
          id: skinSurveyListMock.first.id,
          order: 1,
          questionText: skinSurveyListMock.first.question,
          treatment: treatmentProductListMock.first.name,
          date: DateTime.now().millisecondsSinceEpoch,
        );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      final updatedState = container.read(
        skinSurveyControllerProvider(skinAnalyzeId),
      );

      expect(
        updatedState.treatmentHistory.length,
        2,
      );
    });

    test('Should remove treatment history item', () async {
      // Add a listener to prevent disposal
      void listener(SkinSurveyState? previous, SkinSurveyState next) {}

      container
        ..listen(
          skinSurveyControllerProvider(skinAnalyzeId),
          listener,
        )
        ..read(
          skinSurveyControllerProvider(skinAnalyzeId),
        );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      container.read(
        skinSurveyControllerProvider(skinAnalyzeId).notifier,
      )
        ..updateHaveTreatmentHistory(
          value: true,
          questionText: 'Have you had any treatment for your skin condition?',
        )
        ..addTreatmentHistoryItem()
        ..selectOption(
          id: skinSurveyListMock.first.id,
          questionText: skinSurveyListMock.first.question,
          optionText: skinSurveyListMock.first.answers.first.title,
          allowMultiple: false,
          category: skinSurveyListMock.first.category,
        )
        ..updateTreatmentHistoryItem(
          id: skinSurveyListMock.first.id,
          order: 1,
          questionText: skinSurveyListMock.first.question,
          treatment: treatmentProductListMock.first.name,
          date: DateTime.now().millisecondsSinceEpoch,
        )
        ..removeTreatmentHistoryItem(
          1,
          skinSurveyListMock.first.question,
          skinSurveyListMock.first.id,
        );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      final updatedState = container.read(
        skinSurveyControllerProvider(skinAnalyzeId),
      );

      expect(
        updatedState.treatmentHistory.length,
        1,
      );
    });

    test('Should post user survey', () async {
      container.read(
        skinSurveyControllerProvider(skinAnalyzeId),
      );

      // Wait for async fetch to complete
      await Future<void>.delayed(Duration.zero);

      await container
          .read(
            skinSurveyControllerProvider(skinAnalyzeId).notifier,
          )
          .submitUserSurvey();

      verify(
        () => userSurveyRepository.postUserSurvey(userSurveyRequestMock),
      ).called(1);
    });
  });
}
