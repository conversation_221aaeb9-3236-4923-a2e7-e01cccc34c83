import 'package:euromedica_aizer/src/common/data/data.dart';
import 'package:euromedica_aizer/src/common/data/models/requests/user_survey_request.dart';
import 'package:euromedica_aizer/src/common/data/models/responses/user_survey_response.dart';
import 'package:euromedica_aizer/src/common/data/sources/local/hive_service.dart';
import 'package:euromedica_aizer/src/common/data/sources/remote/config/result.dart';
import 'package:euromedica_aizer/src/common/domain/entities/skin_survey/skin_survey.dart';
import 'package:euromedica_aizer/src/common/domain/entities/treatment_product/treatment_product.dart';
import 'package:euromedica_aizer/src/features/skin_survey/skin_survey_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../mocks.dart';
import '../../robots/skin_survey_robot.dart';

void main() {
  group('Skin Survey Screen Tests', () {
    late MockSkinSurveyRepository skinSurveyRepository;
    late ProviderContainer container;
    late MockAuthRepository authRepository;
    late MockUserSurveyRepository userSurveyRepository;
    late MockSkinAnalyzeRepository skinAnalyzeRepository;
    late MockTreatmentProductRepository treatmentProductRepository;
    late MockHiveService hiveService;
    late SkinSurveyRobot robot;

    const treatmentProductListMock = [
      TreatmentProduct(
        id: '1',
        itemCode: '1',
        name: 'Treatment Product',
        description: 'Treatment Product',
        categories: [],
        quantity: 0,
        price: 0,
        isTopRecommendation: false,
        solvedConcerns: [],
        mediaUrl: '',
        thumbnailUrl: '',
      ),
    ];

    const userSurveyRequestMock = UserSurveyRequest(
      skinAnalyzeId: SkinSurveyRobot.mockSkinAnalyzeId,
      results: [],
    );

    setUp(() {
      authRepository = MockAuthRepository();
      userSurveyRepository = MockUserSurveyRepository();
      skinAnalyzeRepository = MockSkinAnalyzeRepository();
      treatmentProductRepository = MockTreatmentProductRepository();
      hiveService = MockHiveService();
      skinSurveyRepository = MockSkinSurveyRepository();
      container = ProviderContainer(
        overrides: [
          authRepositoryProvider.overrideWithValue(authRepository),
          userSurveyRepositoryProvider.overrideWithValue(userSurveyRepository),
          skinAnalyzeRepositoryProvider
              .overrideWithValue(skinAnalyzeRepository),
          treatmentProductRepositoryProvider
              .overrideWithValue(treatmentProductRepository),
          skinSurveyRepositoryProvider.overrideWithValue(skinSurveyRepository),
          hiveServiceProvider.overrideWithValue(hiveService),
        ],
      );

      when(() => skinSurveyRepository.getSurveyQuestions()).thenAnswer(
        (_) => Future.value(
          Result<List<SkinSurvey>>.success(SkinSurveyRobot.mockSkinSurveyList),
        ),
      );

      when(
        () => userSurveyRepository.postUserSurvey(userSurveyRequestMock),
      ).thenAnswer(
        (_) => Future.value(
          const Result.success(
            UserSurveyResponse(
              id: '1',
              results: [],
            ),
          ),
        ),
      );

      when(() => treatmentProductRepository.getTreatmentProducts()).thenAnswer(
        (_) => Future.value(
          const Result<List<TreatmentProduct>>.success(
            treatmentProductListMock,
          ),
        ),
      );

      when(
        () =>
            skinAnalyzeRepository.getSummary(SkinSurveyRobot.mockSkinAnalyzeId),
      ).thenAnswer(
        (_) => Future.value(
          const Result<String>.success('Summary'),
        ),
      );

      when(
        () => skinAnalyzeRepository
            .getTopConcern(SkinSurveyRobot.mockSkinAnalyzeId),
      ).thenAnswer(
        (_) => Future.value(
          const Result<List<String>>.success(['Concern 1', 'Concern 2']),
        ),
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('SkinSurveyScreen should render correctly', (tester) async {
      robot = SkinSurveyRobot(tester);
      await robot.pumpWidget(container: container);
      // Verify basic components
      expect(find.byType(SkinSurveyScreen), findsOneWidget);
    });

    // TODO: fix this test
    // testWidgets('Should display static questions', (tester) async {
    //   FlutterError.onError = ignoreOverflowErrors;
    //   robot = SkinSurveyRobot(tester);
    //   await robot.pumpWidget(container: container);
    //   expect(
    //     await robot.isQuestionVisible(customStaticQuestions[0]),
    //     isTrue,
    //   );
    // });

    // testWidgets('Should display dynamic questions', (tester) async {
    //   robot = SkinSurveyRobot(tester);
    //   await robot.pumpWidget(container: container);

    //   final isQuestionVisible = await robot.isQuestionVisible(
    //     SkinSurveyRobot.dynamicQuestionMock[0].question,
    //   );

    //   expect(isQuestionVisible, isTrue);

    //   await tester.pumpWidget(Container());
    //   await tester.pump(const Duration(seconds: 1));
    // });

    testWidgets('Should select pregnancy question', (tester) async {
      robot = SkinSurveyRobot(tester);
      await robot.pumpWidget(container: container);
      await robot.selectPregnancyQuestionYes();

      expect(
        find.text(
          '''We sincerely apologize, but if you are currently pregnant, you are not eligible to receive any of the treatments offered at our clinic.''',
        ),
        findsOneWidget,
      );
      await tester.pumpWidget(Container());
      await tester.pump(const Duration(seconds: 1));
    });
  });
}
