import type ApiClient from "../client";
import {
  SkinProblemIndication,
  SkinProblemIndicationBodyRequest,
  SkinProblemIndicationPaginatedResponse,
  SkinProblemIndicationRequestParams,
  SkinProblemIndicationResponse,
} from "../types/skin-problem-indication";

export default class SkinProblemIndicationService {
  constructor(private apiClient: ApiClient, private path = "/skin-problem-indication") {}

  getAll(params: SkinProblemIndicationRequestParams) {
    const { search } = params;

    const queryParams = {
      ...(search ? { name: search } : {}),
    };

    return this.apiClient._request<SkinProblemIndicationPaginatedResponse>(this.path, {
      queryParams,
    });
  }

  getDetail(uid: string) {
    return this.apiClient._request<SkinProblemIndicationResponse>(`${this.path}/${uid}`);
  }

  create(data: SkinProblemIndicationBodyRequest) {
    return this.apiClient._request<SkinProblemIndicationResponse>(this.path, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  update(uid: string, data: Partial<SkinProblemIndication>) {
    return this.apiClient._request<SkinProblemIndicationResponse>(`${this.path}/${uid}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  delete(uid: string) {
    return this.apiClient._request<SkinProblemIndicationResponse>(`${this.path}/${uid}`, {
      method: "DELETE",
    });
  }
}
