import type ApiClient from "../client";
import {
  SkinProblemBodyRequest,
  SkinProblemPaginatedResponse,
  SkinProblemRequestParams,
  SkinProblemResponse,
} from "../types/skin-problem";

export default class SkinProblemService {
  constructor(private apiClient: ApiClient, private path = "/skin-problem") {}

  getAll(params?: SkinProblemRequestParams) {
    const queryParams: Record<string, string | string[] | number> = {
      ...(params?.type ? { type: params.type } : {}),
    };
    return this.apiClient._request<SkinProblemPaginatedResponse>(this.path, {
      queryParams,
    });
  }

  getDetail(uid: string) {
    return this.apiClient._request<SkinProblemResponse>(`${this.path}/${uid}`);
  }

  create(data: SkinProblemBodyRequest) {
    return this.apiClient._request<SkinProblemResponse>(this.path, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  update(uid: string, data: SkinProblemBodyRequest) {
    return this.apiClient._request<SkinProblemResponse>(`${this.path}/${uid}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  delete(uid: string) {
    return this.apiClient._request<SkinProblemResponse>(`${this.path}/${uid}`, {
      method: "DELETE",
    });
  }
}
