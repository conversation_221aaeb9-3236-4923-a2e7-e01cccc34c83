import type ApiClient from '../client'
import type { LoginResponse } from '../types/account'

export default class AuthService {
  constructor(private apiClient: ApiClient) {}

  login(email: string, password: string) {
    return this.apiClient._request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
  }

  refresh(refreshToken: string) {
    return this.apiClient._request<LoginResponse>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ "refresh_token": refreshToken }),
    })
  }
}
