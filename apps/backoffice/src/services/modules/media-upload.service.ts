import type ApiClient from "../client";
import { PresignedUrlResponse } from "../types/media-upload";

export default class MediaUploadService {
  constructor(private apiClient: ApiClient, private path = "/media/s3/upload/presign-url") {}

  async presignUrl(filename: string) {
    return this.apiClient._request<PresignedUrlResponse>(this.path, {
      method: "POST",
      body: JSON.stringify({ filename }),
    });
  }

  async uploadFile(url: string, file: File): Promise<string> {
    await fetch(url, {
      method: "PUT",
      headers: { "Content-Type": file.type },
      body: file,
    });

    console.log(file, file.type, file.size, file.name);
    console.log(url, "upload file response");
    const s1 = url.split("?")[0];
    const s2 = s1.split("/media/")[1];
    return `media/${s2}`;
  }
}
