import type ApiClient from "../client";
import {
  TreatmentProductBodyRequest,
  TreatmentProductPaginatedResponse,
  TreatmentProductRequestParams,
  TreatmentProductResponse,
} from "../types/treatment-product";

export default class TreatmentProductService {
  constructor(private apiClient: ApiClient, private path = "/treatment-product") {}

  getAll(params: TreatmentProductRequestParams) {
    const { page, search, page_size, types, category_ids, min_price, max_price } = params;
    
    const queryParams = {
      page,
      name: search,
      page_size,
      ...(types?.length ? { types } : {}),
      ...(category_ids?.length ? { category_ids } : {}),
      ...(min_price ? { min_price } : {}),
      ...(max_price ? { max_price } : {}),
    };
    
    return this.apiClient._request<TreatmentProductPaginatedResponse>(this.path, {
      queryParams,
    });
  }

  getDetail(uid: string) {
    return this.apiClient._request<TreatmentProductResponse>(`${this.path}/${uid}`);
  }

  create(data: TreatmentProductBodyRequest) {
    return this.apiClient._request<TreatmentProductResponse>(this.path, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  update(uid: string, data:  TreatmentProductBodyRequest) {
    return this.apiClient._request<TreatmentProductResponse>(`${this.path}/${uid}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  delete(uid: string) {
    return this.apiClient._request<TreatmentProductResponse>(`${this.path}/${uid}`, {
      method: "DELETE",
    });
  }
}
