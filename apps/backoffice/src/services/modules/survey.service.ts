import type ApiClient from "../client";
import {
  SurveyBodyRequest,
  SurveyPaginatedResponse,
  SurveyRequestParams,
  SurveyResponse,
} from "../types/survey";

export default class SurveyService {
  constructor(private apiClient: ApiClient, private path = "/survey") {}

  getAll(params: SurveyRequestParams) {
    const queryParams: Record<string, string | string[] | number> = {
      page: params.page,
      name: params.search,
      page_size: params.page_size,
      is_static: "false",
      ...(params.category?.length ? { category: params.category } : {}),
      ...(params.type?.length ? { type: params.type } : {}),
    };
    return this.apiClient._request<SurveyPaginatedResponse>(this.path, {
      queryParams,
    });
  }

  getDetail(uid: string) {
    return this.apiClient._request<SurveyResponse>(`${this.path}/nested/${uid}`);
  }

  create(data: SurveyBodyRequest) {
    return this.apiClient._request<SurveyResponse>(`${this.path}/nested`, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  update(uid: string, data: SurveyBodyRequest) {
    return this.apiClient._request<SurveyResponse>(`${this.path}/nested/${uid}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  delete(uid: string) {
    return this.apiClient._request<SurveyResponse>(`${this.path}/${uid}`, {
      method: "DELETE",
    });
  }
}
