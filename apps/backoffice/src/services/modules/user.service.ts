import type ApiClient from "../client";
import { UserBodyRequest, UserPaginatedResponse, UserResponse } from "../types/user";
import { PaginationRequestParams } from "../types/request-params";

export default class UserService {
  constructor(private apiClient: ApiClient, private path = "/user") {}

  getAll(params: PaginationRequestParams) {
    return this.apiClient._request<UserPaginatedResponse>(this.path, {
      queryParams: {
        page: params.page,
        name: params.search,
        page_size: params.page_size,
      },
    });
  }

  getDetail(uid: string) {
    return this.apiClient._request<UserResponse>(`${this.path}/${uid}`);
  }

  create(data: UserBodyRequest) {
    return this.apiClient._request<UserResponse>(this.path, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  update(uid: string, data: UserBodyRequest) {
    return this.apiClient._request<UserResponse>(`${this.path}/${uid}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  delete(uid: string) {
    return this.apiClient._request<UserResponse>(`${this.path}/${uid}`, {
      method: "DELETE",
    });
  }
}
