import { ApiResponse, PaginatedApiResponse } from "./base";

export interface SkinProblemIndication {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface SkinProblemIndicationRequestParams {
  search?: string;
}

export interface SkinProblemIndicationBodyRequest
  extends Omit<SkinProblemIndication, "id" | "created_at" | "updated_at"> {}

export interface SkinProblemIndicationPaginatedResponse
  extends PaginatedApiResponse<SkinProblemIndication> {}

export interface SkinProblemIndicationResponse extends ApiResponse<SkinProblemIndication> {}
