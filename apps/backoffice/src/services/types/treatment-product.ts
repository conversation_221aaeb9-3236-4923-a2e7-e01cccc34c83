import { Answer } from "./answer";
import { ApiResponse, PaginatedApiResponse } from "./base";
import { PaginationRequestParams } from "./request-params";
import { SkinProblem } from "./skin-problem";
import { TreatmentInterval } from "./treatment-interval";
import { TreatmentProductType } from "./treatment-product-type";

export interface TreatmentProduct {
  id: string;
  item_code: string;
  name: string;
  category?: TreatmentProductCategory[];
  concern?: SkinProblem[];
  type: TreatmentProductType;
  price: number;
  description?: string;
  interval?: TreatmentInterval;
  media_url?: string;
  thumbnail_url?: string;
  notes?: string;
  quantity?: number;
  is_top_recommendation?: boolean;
  duration_top_recommendation?: number;
  survey_questions?: TreatmentProductSurveyQuestion[];
}

export interface TreatmentProductSurveyQuestion {
  id: string;
  question: string;
  selected_answer: number;
  question_order: number;
  answers: Answer[];
}

export interface TreatmentProductCategory {
  id: string;
  name: string;
}

export interface TreatmentProductRequestParams extends PaginationRequestParams {
  types?: string[];
  category_ids?: string[];
  min_price?: number;
  max_price?: number;
}

export interface TreatmentProductSurveyQuestionRequest {
  survey_question_id: string;
  selected_answer: number;
}

export interface TreatmentProductBodyRequest
  extends Omit<TreatmentProduct, "category" | "concern" | "interval" | "survey_questions"> {
  category_ids: string[];
  concern_ids: string[];
  interval_id?: string;
  survey_questions?: TreatmentProductSurveyQuestionRequest[];
}

export interface TreatmentProductPaginatedResponse extends PaginatedApiResponse<TreatmentProduct> {}

export interface TreatmentProductResponse extends ApiResponse<TreatmentProduct> {}
