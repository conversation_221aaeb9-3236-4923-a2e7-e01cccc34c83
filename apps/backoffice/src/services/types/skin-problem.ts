import { ApiResponse, PaginatedApiResponse } from "./base";
import { TreatmentConcernType } from "./treatment-concern-type";

export interface SkinProblem {
  id: string;
  name: string;
  created_at: number;
  updated_at: number;
  skin_problem_indications?: SkinProblemIndications[];
}

export interface SkinProblemIndications {
  id: string;
  name: string;
}

export interface SkinProblemRequestParams {
  type?: TreatmentConcernType;
}

export interface SkinProblemBodyRequest extends Omit<SkinProblem, "id" | "created_at" | "updated_at"> {}

export interface SkinProblemPaginatedResponse extends PaginatedApiResponse<SkinProblem> {}

export interface SkinProblemResponse extends ApiResponse<SkinProblem> {}
