export enum TreatmentConcernType {
    GENERAL = "general",
    SPECIFIC = "special",
}

export namespace TreatmentConcernType {
    export function getDisplayValue(value: TreatmentConcernType) {
        switch (value) {
            case TreatmentConcernType.GENERAL:
                return "General";
            case TreatmentConcernType.SPECIFIC:
                return "Specific";
        }
    }

    export function fromStringValue(value: string): TreatmentConcernType {
        switch (value) {
            case "General":
                return TreatmentConcernType.GENERAL;
            case "Special":
                return TreatmentConcernType.SPECIFIC;
            default:
                throw new Error("Invalid TreatmentConcernType");
        }
    }

    export function fromString(value: string): TreatmentConcernType {
        switch (value) {
            case "General":
                return TreatmentConcernType.GENERAL;
            case "Specific":
                return TreatmentConcernType.SPECIFIC;
            default:
                throw new Error("Invalid TreatmentConcernType");
        }
    }

    export function getAllDisplayValues() {
        return [TreatmentConcernType.GENERAL, TreatmentConcernType.SPECIFIC].map((type) =>
            getDisplayValue(type)
        );
    }
}   