import { type ConsolaInst<PERSON>, type <PERSON><PERSON><PERSON><PERSON><PERSON>, create<PERSON>ons<PERSON> } from "consola";
import { type $Fetch, FetchError, ofetch } from "ofetch";
import { isProduction } from "#/config";
import { isBrowser } from "./helper";
import AuthService from "./modules/auth.service";
import DEFAULT_OPTIONS from "./options";
import type { ApiClientOptions, HealthCheckResponse } from "./types/base";
import UserService from "./modules/user.service";
import { authStore, resetAuthState } from "#/context/stores/auth.store";
import TreatmentProductService from "./modules/treatment-product.service";
import SurveyService from "./modules/survey.service";
import TreatmentCategoryService from "./modules/treatment-category.service";
import TreatmentIntervalService from "./modules/treatment-interval.service";
import SkinProblemService from "./modules/skin-problem.service";
import ParameterSkinEvaluationService from "./modules/parameter-skin-evaluation.service";
import MediaUploadService from "./modules/media-upload.service";
import SkinProblemIndicationService from "./modules/skin-problem-indication.service";

interface RequestOptions extends RequestInit {
  clientInfo?: string;
  queryParams?: Record<string, string | number | string[]>;
}

const HTTPRegexp = /^http:\/\//;

export default class ApiClient {
  private static instance: ApiClient;
  private static nextInstanceID = 0;
  protected static logTag = "ApiClient";

  private instanceID: number;
  private fetcher: $Fetch;
  private clientInfo: string;

  protected baseUrl: string;
  protected logLevel: LogLevel;
  protected logger: ConsolaInstance;

  protected headers: {
    [key: string]: string;
  };

  auth: AuthService;
  user: UserService;
  treatmentProduct: TreatmentProductService;
  survey: SurveyService;
  treatmentCategory: TreatmentCategoryService;
  treatmentInterval: TreatmentIntervalService;
  skinProblem: SkinProblemService;
  parameterSkinEvaluation: ParameterSkinEvaluationService;
  mediaUpload: MediaUploadService;
  skinProblemIndication: SkinProblemIndicationService;

  constructor(options: ApiClientOptions) {
    this.instanceID = ApiClient.nextInstanceID;
    ApiClient.nextInstanceID += 1;

    /**
     * Initializes the some properties of the `ApiClient` class with a default value.
     * The `clientInfo` property is used to identify the client making requests to the API.
     * The `DEFAULT_OPTIONS` object is merged with the provided `options` object,
     * and the resulting object is used to configure the `ApiClient` instance.
     */
    const clientInfo = `ApiClient-${import.meta.env.APP_VERSION}`;
    const settings = { ...DEFAULT_OPTIONS, ...options, clientInfo };

    // By default, in DEV mode we log all requests and responses.
    // This setting can be overridden via the `logLevel` option.
    this.logLevel = options.logLevel ?? settings.logLevel;
    this.logger = createConsola({
      level: this.logLevel,
    });

    if (this.instanceID > 0 && isBrowser()) {
      this.logger.warn(
        ApiClient.logTag,
        "Multiple ApiClient instances detected in the same browser context.",
        "It may produce undefined behavior when used concurrently under the same storage key."
      );
    }

    this.baseUrl = settings.baseUrl ?? "";
    this.headers = settings.headers || {};
    this.clientInfo = settings.clientInfo;
    this.fetcher = this._createFetcher();
    this.auth = new AuthService(this);
    this.user = new UserService(this);
    this.treatmentProduct = new TreatmentProductService(this);
    this.survey = new SurveyService(this);
    this.treatmentCategory = new TreatmentCategoryService(this);
    this.treatmentInterval = new TreatmentIntervalService(this);
    this.skinProblem = new SkinProblemService(this);
    this.parameterSkinEvaluation = new ParameterSkinEvaluationService(this);
    this.mediaUpload = new MediaUploadService(this);
    this.skinProblemIndication = new SkinProblemIndicationService(this);

    if (isProduction && HTTPRegexp.test(this.baseUrl)) {
      this.logger.warn(
        ApiClient.logTag,
        "NEVER USE HTTP IN PRODUCTION. Always use HTTPS for secure operations."
      );
    }
  }

  static getInstance(options?: ApiClientOptions): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient(options || {});
    }
    return ApiClient.instance;
  }

  private _createFetcher(): $Fetch {
    const logger = this.logger;
    return ofetch.create({
      baseURL: this.baseUrl,
      async onRequest(_ctx) {
        // Do something before request is sent.
        logger.debug("[FETCHER] onRequest", _ctx.request);
      },
      async onResponse(_ctx) {
        logger.debug("[FETCHER] onResponse", _ctx.response);
      },
    });
  }

  async _requestWithoutCredentials<T>(path: string, options?: RequestOptions) {
    const headers: HeadersInit = new Headers(options?.headers);
    try {
      return await this.fetcher<T>(path, { ...options, headers });
      
    } catch (error) {
      throw error;
    }
  }

  async _request<T>(path: string, options?: RequestOptions) {
    const headers: HeadersInit = new Headers(options?.headers);
    const logger = this.logger;

    // Set default request headers
    headers.append("Accept", "application/json");
    headers.append("Content-Type", "application/json");

    const accessToken = authStore.get().accessToken;
    if (accessToken) {
      headers.append("Authorization", `Bearer ${accessToken}`);
    }

    if (options?.clientInfo && !headers.has("X-Client-Info")) {
      headers.append("X-Client-Info", options.clientInfo);
    } else {
      headers.append("X-Client-Info", this.clientInfo);
    }

    if (options?.queryParams) {
      const params = new URLSearchParams();
      Object.entries(options.queryParams).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach((item) => params.append(key, item.toString()));
        } else {
          params.append(key, value.toString());
        }
      });
      path += `?${params.toString()}`;
    }

    try {
      return await this.fetcher<T>(path, { ...options, headers });
    } catch (error) {
      if (error instanceof FetchError && error.data) {
        if (error.status === 401 && path !== "/auth/refresh") {
          logger.debug("[ApiClient] Token expired, refreshing...");
          await this._refreshToken(() => {
            this._request<T>(path, options);
          });
        } else if (error.status === 401 && path === "/auth/refresh") {
          logger.error("[ApiClient] Refresh token failed, clearing auth store.");
          resetAuthState();
          window.location.href = "/auth/login";
        }
        if (error.data.error) {
          error.message = error.data.error.message;
        }
      }
      throw error;
    }
  }

  async _refreshToken(callback?: () => void) {
    const refreshToken = authStore.get().refreshToken;
    if (!refreshToken) return;
    try {
      const response = await this.auth.refresh(refreshToken);
      if (!response?.data?.token || !response?.data?.refresh_token) {
        return;
      }
      authStore.set({
        ...authStore.get(),
        accessToken: response?.data?.token,
        refreshToken: response?.data?.refresh_token,
      });
      callback?.();
    } catch (error) {
      console.log(error);
    }
  }

  _healthCheck(): Promise<HealthCheckResponse> {
    return this._request<HealthCheckResponse>("/health");
  }
}
