import { type RouteObject, createBrowserRouter, useRoutes } from "react-router-dom";
import NotFound from "#/pages/errors/not-found";

// Application layouts
import AppLayout from "#/layouts/app-layout";
import AuthLayout from "#/layouts/auth-layout";

// Authentication pages
import SignInPage from "#/pages/auth/login";
import LogoutPage from "#/pages/auth/logout";
import ForgotPasswordPage from "#/pages/auth/recovery";
import ResetPasswordPage from "#/pages/auth/resetpass";
import SignUpPage from "#/pages/auth/signup";

// Boilerplate pages
import BoilerPlate from "#/pages/boilerplate/boilerplate";

// User pages
import UserPage from "./pages/user/page";
import UserDetailPage from "./pages/user/user-detail/page";
import CreateUserPage from "./pages/user/create-user/page";
import EditUserPage from "./pages/user/edit-user/page";

// Treatment and Product List
import TreatmentAndProductListPage from "#/pages/treatment-and-product/list/page";
import TreatmentAndProductPackagePage from "./pages/treatment-and-product/package/page";
import CreatePackagePage from "./pages/treatment-and-product/package/create-package/page";
import EditPackagePage from "./pages/treatment-and-product/package/edit-package/page";
import SurveyPage from "./pages/survey/page";
import EditSurveyPage from "./pages/survey/edit-survey/page";
import CreateSurveyPage from "./pages/survey/create-survey/page";
import CreateTreatmentProductPage from "./pages/treatment-and-product/create-treatment-product/page";
import EditTreatmentProductPage from "./pages/treatment-and-product/edit-treatment-product/page";

// Parameter Skin Evaluation
import ParameterSkinEvaluationPage from "./pages/parameter-skin-evaluation/page";
import EditParameterSkinEvaluationPage from "./pages/parameter-skin-evaluation/edit-parameter-skin-evaluation/page";

// Skin Concern
import SkinConcernGroupingPage from "./pages/skin-concern-grouping/page";
import SkinConcernIndicationPage from "./pages/skin-concern-indication/page";

const route = (path: string, { ...props }: RouteObject) => ({ path, ...props });

/**
 * Using dynamic import for the pages to reduce the bundle size.
 *
 * IMPORTANT: Ensure the imported module exports both 'Component' and 'loader'.
 * These exports are required for proper routing and data loading.
 *
 * @see https://reactrouter.com/en/route/lazy#statically-defined-properties
 */
const routes: RouteObject[] = [
  route("/", {
    element: <AppLayout />,
    children: [
      {
        path: "boilerplate",
        element: <BoilerPlate />,
        handle: {
          breadcrumb: "Boilerplate",
        },
      },
      {
        path: "user",
        handle: {
          breadcrumb: "User",
        },
        children: [
          {
            index: true,
            element: <UserPage />,
          },
          {
            path: ":id",
            element: <UserDetailPage />,
            handle: {
              breadcrumb: "User Detail",
            },
          },
          {
            path: "create",
            element: <CreateUserPage />,
            handle: {
              breadcrumb: "Create User",
            },
          },
          {
            path: "edit/:id",
            element: <EditUserPage />,
            handle: {
              breadcrumb: "Edit User",
            },
          },
        ],
      },
      {
        path: "treatment-and-product",
        handle: {
          breadcrumb: "Treatment and Product",
        },
        children: [
          {
            index: true,
            element: <TreatmentAndProductListPage />,
          },
          {
            path: "create",
            element: <CreateTreatmentProductPage />,
            handle: {
              breadcrumb: "Create Treatment Product",
            },
          },
          {
            path: "edit/:id",
            element: <EditTreatmentProductPage />,
            handle: {
              breadcrumb: "Edit Treatment Product",
            },
          },
          {
            path: "package",
            handle: {
              breadcrumb: "Package",
            },
            children: [
              {
                index: true,
                element: <TreatmentAndProductPackagePage />,
              },
              {
                path: "create",
                handle: {
                  breadcrumb: "Create Package",
                },
                element: <CreatePackagePage />,
              },
              {
                path: "edit/:id",
                handle: {
                  breadcrumb: "Edit Package",
                },
                element: <EditPackagePage />,
              },
            ],
          },
        ],
      },
      {
        path: "survey",
        handle: {
          breadcrumb: "Survey",
        },
        children: [
          {
            index: true,
            element: <SurveyPage />,
          },
          {
            path: "create",
            handle: {
              breadcrumb: "Create Survey",
            },
            element: <CreateSurveyPage />,
          },
          {
            path: "edit/:id",
            handle: {
              breadcrumb: "Edit Survey",
            },
            element: <EditSurveyPage />,
          },
        ],
      },
      {
        path: "parameter-skin-evaluation",
        handle: {
          breadcrumb: "Parameter Skin Evaluation",
        },
        children: [
          {
            index: true,
            element: <ParameterSkinEvaluationPage />,
          },
          {
            path: "edit/:id",
            handle: {
              breadcrumb: "Edit Parameter Skin Evaluation",
            },
            element: <EditParameterSkinEvaluationPage />,
          },
        ],
      },
      {
        path: "skin-concern",
        handle: {
          breadcrumb: "Skin Concern",
          href: "#",
        },
        children: [
          {
            path: "grouping",
            element: <SkinConcernGroupingPage />,
            handle: {
              breadcrumb: "Grouping",
            },
          },
          {
            path: "indication",
            element: <SkinConcernIndicationPage />,
            handle: {
              breadcrumb: "Indication",
            },
          },
        ],
      },
      { path: "logout", element: <LogoutPage /> },
      // {
      //   path: 'settings',
      //   element: <AppSettings />,
      //   children: [
      //     { index: true, lazy: () => import('#/pages/settings/general') },
      //     { path: 'account', lazy: () => import('#/pages/settings/account') },
      //   ],
      // },
    ],
  }),
  route("/auth", {
    element: <AuthLayout />,
    children: [
      { path: "login", element: <SignInPage /> },
      { path: "register", element: <SignUpPage /> },
      { path: "recovery", element: <ForgotPasswordPage /> },
      { path: "resetpass", element: <ResetPasswordPage /> },
    ],
  }),
  route("*", { element: <NotFound /> }),
];

/**
 * Creates a browser-based router instance using the provided `routes` configuration.
 * This router instance can be used with the `RouterProvider` component to render the application's routes.
 *
 * @example
 *
 * import { RouterProvider } from 'react-router-dom'
 * import { routes, browserRoutes } from './routes'
 *
 * const App = () => {
 *   return <RouterProvider router={browserRoutes} />
 * }
 *
 */
const browserRoutes = createBrowserRouter(routes);

/**
 * Renders the application's routes using the `useRoutes` hook from `react-router-dom`.
 * This component is responsible for setting up the routing structure and rendering the
 * appropriate components based on the current URL.
 *
 * @example
 *
 * import { BrowserRouter } from 'react-router-dom'
 * import AppRoutes from './routes'
 *
 * export default function App() {
 *   return (
 *     <BrowserRouter>
 *       <AppRoutes />
 *     </BrowserRouter>
 *   )
 * }
 *
 * @returns {JSX.Element} The rendered routes for the application.
 */
const AppRoutes = (): React.ReactElement | null => useRoutes(routes);

export { routes, browserRoutes };

export default AppRoutes;
