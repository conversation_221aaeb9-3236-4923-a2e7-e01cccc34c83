/**
 * Get media filename from URL
 * @param url
 * @returns
 *
 * Example:
 *
 * Input: https://s3.ap-southeast-1.amazonaws.com/euromedica-aizer/media/2025-07-04_15-00-27/image.png?AWSAccessKeyId=AKIAIOSFODNN7EXAMPLE&Signature=EXAMPLE
 * Output: media/2025-07-04_15-00-27/image.png
 */
export const getMediaFilenameFromUrl = (url?: string) => {
  if (!url) return undefined;
  const filename = url.split("?")[0].split("/media/")[1];
  return `media/${filename}`;
};

/**
 * Get filename from URL
 * @param url
 * @returns
 *
 * Example:
 *
 * Input: https://s3.ap-southeast-1.amazonaws.com/euromedica-aizer/media/2025-07-04_15-00-27/image.png?AWSAccessKeyId=AKIAIOSFODNN7EXAMPLE&Signature=EXAMPLE
 * Output: image.png
 */
export const getFilenameFromUrl = (url?: string) => {
  if (!url) return undefined;
  const filenameWithMedia = url.split("?")[0].split("/media/")[1];
  const filename = filenameWithMedia.split(/\//).pop();
  return filename;
};

/**
 * Get filename from media path
 * @param path
 * @returns
 *
 * Example:
 *
 * Input: media/2025-07-04_15-00-27/image.png
 * Output: image.png
 */
export const getFilenameFromMediaPath = (path?: string) => {
  if (!path) return undefined;
  const filename = path.split(/\//).pop();
  return filename;
};

/**
 * Sanitize filename
 * @param filename
 * @returns
 *
 * Example:
 *
 * Input: image 1 (1).png
 * Output: image_1_1.png
 */
export const sanitizeFilename = (filename: string): string => {
  // Replace spaces with underscores and remove other special characters
  return filename.replace(/\s/g, "_").replace(/[^a-zA-Z0-9._-]/g, "");
};
