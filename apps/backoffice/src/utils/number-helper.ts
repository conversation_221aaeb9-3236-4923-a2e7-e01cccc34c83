// convert number to rupiah
/**
 * Convert number to rupiah
 * @param value number to convert
 * @returns rupiah string
 *
 * example:
 * ```
 * const value = 10000;
 * convertToRupiah(value);
 * // Returns: "10.000"
 * ```
 */
export function convertToRupiah(value: number) {
  return new Intl.NumberFormat('id-ID', {
    style: 'decimal',
    currency: 'IDR',
  }).format(value)
}

// convert rupiah to number
/**
 * Convert rupiah to number
 * @param value rupiah string to convert
 * @returns number
 *
 * example:
 * ```
 * const value = "10.000";
 * convertToNumber(value);
 * // Returns: 10000
 * ```
 */
export function convertToNumber(value: string, separator: string = ".") {
  const separatorValue = separator;
  return Number(value.replace(new RegExp(`[^0-9${separatorValue}]`, 'g'), ''));
}