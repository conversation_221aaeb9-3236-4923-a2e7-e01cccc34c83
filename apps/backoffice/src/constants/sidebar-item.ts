import { UserIcon, HealthIcon, AnalyticIcon, ClipboardOutlineIcon, SkinConcernIcon } from "#/components/icons";
import { AnalyticIconOutline } from "#/components/icons/analytic-icon-outline";
import { ClipboardIcon } from "#/components/icons/clipboard-icon";
import { User } from "iconoir-react";

export interface SidebarItem {
  title: string;
  url: string;
  disabled?: boolean;
  external?: boolean;
  icon?: React.ElementType;
  activeIcon?: React.ElementType;
  label?: string;
  description?: string;
  isActive?: boolean;
  items?: SidebarItem[];
}

export interface NavItemWithChildren extends SidebarItem {
  items: NavItemWithChildren[];
}

export interface NavItemWithOptionalChildren extends SidebarItem {
  items?: NavItemWithChildren[];
}

export interface FooterItem {
  title: string;
  items: {
    title: string;
    href: string;
    external?: boolean;
  }[];
}

export type MainNavItem = NavItemWithOptionalChildren;

export type SidebarNavItem = NavItemWithChildren;

export const sidebarItems: SidebarItem[] = [
  {
    title: "User",
    url: "/user",
    icon: User,
    activeIcon: UserIcon,
  },
  {
    title: "Treatments & Products",
    url: "/treatment-and-product",
    icon: HealthIcon,
    activeIcon: HealthIcon,
  },
  {
    title: "Survey",
    url: "/survey",
    icon: ClipboardOutlineIcon,
    activeIcon: ClipboardIcon,
  },
  {
    title: "Skin Concern",
    url: "#",
    icon: SkinConcernIcon,
    activeIcon: SkinConcernIcon,
    items: [
      {
        title: "Grouping",
        url: "/skin-concern/grouping",
      },
      {
        title: "Indication",
        url: "/skin-concern/indication",
      },
    ],
  },
  {
    title: "Parameter Skin Evaluation",
    url: "/parameter-skin-evaluation",
    icon: AnalyticIconOutline,
    activeIcon: AnalyticIcon,
  }
];
