import { useStore } from "@nanostores/react";
import { createContext, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { CookiesProvider, useCookies } from "react-cookie";
import type { CookieSetOptions } from "universal-cookie";
import { useQueryClient } from "@tanstack/react-query";
import { useApiClient } from "#/context/hooks/api-client";
import {
  authStore,
  defaultAuthStoreValues,
  resetAuthState,
  saveAuthState,
} from "#/context/stores/auth.store";
import type { AuthStore } from "#/context/stores/auth.store";
import { cn } from "#/utils/helper";
import { jwtDecode } from "jwt-decode";

type AuthContext = {
  login: (identity: string, password: string) => Promise<string | null>;
  logout: () => void;
} & Pick<AuthStore, "loggedIn" | "user" | "role">;

const defaultAuthContext: AuthContext = {
  loggedIn: defaultAuthStoreValues.loggedIn,
  user: defaultAuthStoreValues.user,
  role: defaultAuthStoreValues.role,
  login: async () => null,
  logout: () => {},
};

const AuthContext = createContext(defaultAuthContext);

interface AppProviderProps {
  children: React.ReactNode;
  debugScreenSize?: boolean;
}

const COOKIE_NAME = "auth_session";
const COOKIE_LIFETIME = 60 * 60 * 24 * 7; // 7 days
const COOKIE_OPTIONS: Omit<CookieSetOptions, "maxAge"> = { path: "/", sameSite: "strict" };

/**
 * Provides the AppProvider component that manages the authentication state and context for the application.
 *
 * The AppProvider component is responsible for:
 * - Checking the authentication state from cookies and the auth store
 * - Providing a login function to authenticate the user
 * - Providing a logout function to log the user out
 * - Providing the authenticated user data and admin status in the AuthContext
 *
 * The AppProvider component should be used to wrap the entire application to make the AuthContext available.
 */
function AppProvider({ children, debugScreenSize }: AppProviderProps) {
  const [cookies, setCookie, removeCookie] = useCookies([COOKIE_NAME]);
  const apiRef = useRef(useApiClient());
  const authState = useStore(authStore);
  const queryClient = useQueryClient();

  // Prevents saveAuthState values from being overridden during token validation from cookies.
  const [pendingCheck, setPendingCheck] = useState<boolean>(false);

  const checkAuth = useCallback(() => {
    if (pendingCheck) return;

    const isLoggedIn = !!authState.accessToken;

    if (!isLoggedIn) {
      logout();
    }

    saveAuthState({
      loggedIn: isLoggedIn,
      user: isLoggedIn ? authState.user : null,
      role: isLoggedIn ? authState.role : null,
      accessToken: isLoggedIn ? authState.accessToken : null,
      refreshToken: isLoggedIn ? authState.refreshToken : null,
    });
  }, [pendingCheck, cookies.auth_session, authState.accessToken, authState.user, authState.role]);

  // Check the authentication state from cookies and the auth store
  useEffect(() => checkAuth(), [checkAuth]);

  const login = useCallback(
    async (identity: string, password: string) => {
      setPendingCheck(true);

      try {
        const result = await apiRef.current.auth.login(identity, password);

        if (!result) {
          throw new Error("An error occurred");
        }

        const decodedToken = jwtDecode<{ id: string }>(result.data?.token);
        const iduser = decodedToken?.id ?? "";

        // Fetch user detail from the backend.
        const user = await fetchUserDetail(iduser);

        // Save the authentication state in the localstorage.
        // This is used by the frontend to check the authentication status.
        saveAuthState({
          loggedIn: true,
          user,
          accessToken: result.data?.token,
          refreshToken: result.data?.refresh_token,
        });

        // Reset the entire TanStack Query cache to ensure fresh data after login
        queryClient.clear();

        // Set the cookie with a maxAge of 7 days.
        // This is used by the backend to validate the authentication status.
        setCookie(COOKIE_NAME, result.data?.token, {
          maxAge: COOKIE_LIFETIME,
          ...COOKIE_OPTIONS,
        });

        return result.data?.token || null;
      } finally {
        setPendingCheck(false);
      }
    },
    [setCookie]
  );

  const fetchUserDetail = async (iduser: string) => {
    const user = await apiRef.current.user.getDetail(iduser);
    return user?.data;
  };

  const logout = useCallback(() => {
    removeCookie(COOKIE_NAME);
    resetAuthState();
  }, [removeCookie]);

  const authContextValues = useMemo(
    () => ({ ...authState, login, logout }),
    [authState, login, logout]
  );

  return (
    <CookiesProvider defaultSetOptions={COOKIE_OPTIONS}>
      <AuthContext.Provider value={authContextValues}>
        <div className={cn(debugScreenSize && "debug-breakpoints")}>{children}</div>
      </AuthContext.Provider>
    </CookiesProvider>
  );
}

export { AuthContext };
export default AppProvider;
