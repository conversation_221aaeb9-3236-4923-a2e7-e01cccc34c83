import { FilterMenuItem } from "#/constants/filter-menu-item";
import React from "react";
import { useNavigate } from "react-router-dom";

type FilterContext = {
  filterItems: FilterMenuItem[];
  isFilterActive: boolean;
  queryParamsChanged: (newQueryParams: URLSearchParams) => void;
  resetFilter: () => void;
  removeFilter: (key: string, value: string) => void;
};

export const FilterContext = React.createContext<FilterContext | null>(null);

export interface FilterProviderProps extends React.PropsWithChildren {
  filterItems: FilterMenuItem[];
}

export const FilterProvider = React.forwardRef<HTMLDivElement, FilterProviderProps>(
  ({ children, filterItems }, ref) => {
    const navigate = useNavigate();
    const queryParams = React.useMemo(
      () => new URLSearchParams(location.search),
      [location.search]
    );

    const queryParamsChanged = (newQueryParams: URLSearchParams) => {
      // reset page to 1 when filter is changed
      newQueryParams.set("page", "1");
      navigate(`?${newQueryParams.toString()}`);
    };

    const resetFilter = () => {
      const newQueryParams = new URLSearchParams(queryParams);

      // reset page to 1 when filter is changed
      queryParams.set("page", "1");

      queryParams.forEach((_, key) => {
        if (filterItems.find((item) => item.label.toLowerCase() === key)) {
          newQueryParams.delete(key);
        }
      });
      navigate(`?${newQueryParams.toString()}`);
    };

    const removeFilter = (key: string, value: string) => {
      if (typeof value !== "string") return;

      // reset page to 1 when filter is changed
      queryParams.set("page", "1");

      const existingQueryParams = new URLSearchParams(queryParams);
      const newQueryParams = new URLSearchParams();

      existingQueryParams.forEach((existingValue, existingKey) => {
        if (!(existingKey === key && existingValue === value)) {
          newQueryParams.append(existingKey, existingValue);
        }
      });

      navigate(`?${newQueryParams.toString()}`);
    };

    const isFilterActive = React.useMemo(() => {
      let isActive = false;
      filterItems.forEach((item) => {
        const query = queryParams.get(item.label.toLowerCase());
        if (query) isActive = true;
      });
      return isActive;
    }, [queryParams]);

    return (
      <div className="flex flex-col gap-4" ref={ref}>
        <FilterContext.Provider
          value={{
            filterItems,
            queryParamsChanged,
            resetFilter,
            removeFilter,
            isFilterActive,
          }}
        >
          {children}
        </FilterContext.Provider>
      </div>
    );
  }
);
