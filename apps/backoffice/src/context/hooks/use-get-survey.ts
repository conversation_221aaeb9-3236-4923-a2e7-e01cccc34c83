import { useQuery } from "@tanstack/react-query";
import { useApiClient } from "#/context/hooks/api-client";
import { SurveyRequestParams } from "#/services/types/survey";
import { QuestionCategory } from "#/services/types/question-category";

export const useGetSurvey = (uid?: string) => {
  const apiClient = useApiClient();
  return useQuery({
    queryKey: ["survey", uid],
    queryFn: async () => apiClient.survey.getDetail(uid!),
    enabled: !!uid,
  });
};

export const useGetAllContraindicationSurveys = (enabled: boolean = true, params?: SurveyRequestParams) => {
  const apiClient = useApiClient();
  return useQuery({
    queryKey: ["surveys", params],
    queryFn: async () => apiClient.survey.getAll({
      page: 0,
      page_size: 0,
      search: "",
      category: [QuestionCategory.CONTRAINDICATION],
    }),
    enabled,
  });
};
  