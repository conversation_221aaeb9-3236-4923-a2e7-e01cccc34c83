import { useQuery } from "@tanstack/react-query";
import { useApiClient } from "#/context/hooks/api-client";
import { SkinProblemRequestParams } from "#/services/types/skin-problem";

export const useGetSkinProblem = (uid: string) => {
  const apiClient = useApiClient();
  return useQuery({
    queryKey: ["skin-problem", uid],
    queryFn: async () => apiClient.skinProblem.getDetail(uid),
  });
};

export const useGetAllSkinProblems = (
  enabled: boolean = true,
  params?: SkinProblemRequestParams
) => {
  const apiClient = useApiClient();
  return useQuery({
    queryKey: ["skin-problems", params],
    queryFn: async () => apiClient.skinProblem.getAll(params),
    enabled,
  });
};
