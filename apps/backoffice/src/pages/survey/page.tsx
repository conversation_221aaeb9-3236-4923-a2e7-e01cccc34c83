import { Input } from "#/components/base";
import { DataTable } from "#/components/data-table";
import { Plus, Search } from "iconoir-react";
import { But<PERSON> } from "shared-ui";
import { surveyColumns } from "./data-table";
import { useNavigate } from "react-router-dom";
import { usePaginationQuery } from "#/context/hooks/use-pagination";
import { SurveyPaginatedResponse } from "#/services/types/survey";
import { useApiClient } from "#/context/hooks/api-client";
import { FilterProvider } from "#/context/providers/filter-provider";
import { FilterDropdown } from "#/components/filter-dropdown";
import { FilterList } from "#/components/filter-list";
import { surveyFilter } from "./filter-items";
import { QuestionCategory } from "#/services/types/question-category";

export default function SurveyPage() {
  const navigate = useNavigate();
  const apiClient = useApiClient();
  const queryParams = new URLSearchParams(location.search);

  const { data, page, search, setSearch, queryParamsChanged } =
    usePaginationQuery<SurveyPaginatedResponse>({
      queryKey: ["survey", queryParams.toString()],
      queryFn: (params) =>
        apiClient.survey.getAll({
          ...params,
          category: queryParams.getAll("category") as QuestionCategory[],
          type: queryParams.getAll("type") as string[],
        }),
    });

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value;
    setSearch(searchValue);
  };
  const onPageChanged = (value: number) => queryParamsChanged("page", value.toString());
  const onPageSizeChanged = (value: number) => queryParamsChanged("page_size", value.toString());

  const { content, total_pages, page_size } = data?.data ?? {};

  return (
    <div className="p-6 rounded-xl bg-background shadow-md">
      <div className="flex flex-col gap-4">
        <FilterProvider filterItems={surveyFilter}>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Input
                name="search"
                placeholder="Search survey"
                className="w-64"
                prefixIcon={<Search className="size-5" />}
                value={search}
                onChange={handleSearch}
              />
              <FilterDropdown />
            </div>
            <div className="flex gap-2 items-center">
              <Button onClick={() => navigate("create")}>
                <Plus className="size-5" />
                <span className="text-small">Create Survey</span>
              </Button>
            </div>
          </div>
          <FilterList />
        </FilterProvider>
        <DataTable
          columns={surveyColumns}
          data={content ?? []}
          page={page}
          totalPage={total_pages ?? 1}
          pageSize={page_size ?? 10}
          onPageSizeChanged={onPageSizeChanged}
          onPageChanged={onPageChanged}
        />
      </div>
    </div>
  );
}
