import { TreatmentProductType } from "#/services/types/treatment-product-type";
import { z } from "zod";

// Base schema for common fields
export const baseSchema = z.object({
  id: z.string().optional(),
  item_code: z.string().min(1, { message: "Required" }),
  name: z.string().min(1, { message: "Required" }),
});

// Treatment specific schema
export const treatmentSchema = z.object({
  type: z.literal(TreatmentProductType.TREATMENT),
  media_url: z.string().optional(),
  thumbnail_url: z.string().optional(),
  category: z.array(z.string()).min(1, { message: "Required" }),
  concern: z.array(z.string().min(1, { message: "" })).default([]),
  top_recommendation: z.enum(["yes", "no"]),
  top_recommendation_end_date: z.date().optional(),
  description: z.string().min(1, { message: "Required" }),
  notes: z.string().optional(),
  treatment_interval: z.string().min(1, { message: "Required" }),
  price: z.string().min(1, { message: "Required" }),
  survey_questions: z.array(
    z.object({
      survey_question_id: z.string(),
      selected_answer: z.number(),
    })
  ),
});

// Product specific schema
export const productSchema = z.object({
  type: z.literal(TreatmentProductType.PRODUCT),
  media_url: z.string().optional(),
  description: z.string().min(1, { message: "Required" }),
  price: z.string().min(1, { message: "Required" }),
});

// Create discriminated union schemas
export const treatmentProductSchema = z
  .discriminatedUnion("type", [baseSchema.merge(treatmentSchema), baseSchema.merge(productSchema)])
  .superRefine((val, ctx) => {
    // Only apply validation for treatment type
    if (val.type === TreatmentProductType.TREATMENT) {
      // If top_recommendation is "yes", top_recommendation_end_date must be provided
      if (val.top_recommendation === "yes" && !val.top_recommendation_end_date) {
        ctx.addIssue({
          path: ["top_recommendation_end_date"],

          code: z.ZodIssueCode.custom,
          message: "End date is required when top recommendation is set to 'yes'",
        });
      }
    }
  });

// Infer types
export type TreatmentProductFormValues = z.infer<typeof treatmentProductSchema>;
export type TreatmentFormValues = z.infer<typeof baseSchema> & z.infer<typeof treatmentSchema>;
export type ProductFormValues = z.infer<typeof baseSchema> & z.infer<typeof productSchema>;
