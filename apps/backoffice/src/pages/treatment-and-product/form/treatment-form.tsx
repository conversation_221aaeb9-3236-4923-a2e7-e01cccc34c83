import { FormType } from "#/services/types/form-type";
import { UseFormReturn } from "react-hook-form";
import {
  <PERSON><PERSON>,
  <PERSON>Picker,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Textarea,
} from "shared-ui";
import RadioGroup from "#/components/radio-group";
import { Calendar } from "iconoir-react";
import { TreatmentFormValues } from "../schema/treatment-product-form-schema";
import { FileUpload } from "#/components/file-upload";
import { TreatmentCategoryField } from "./treatment-category-field";
import { TreatmentIntervalField } from "./treatment-interval-field";
import { TreatmentConcernField } from "./treatment-concern-field";
import { useUploadFile } from "../mutation/use-upload-file";
import { useState } from "react";
import { convertToRupiah } from "#/utils/number-helper";
import { TreatmentSurveyField } from "./treatment-survey-field";

interface TreatmentFormProps {
  formType?: FormType;
  form: UseFormReturn<TreatmentFormValues>; // Use any here to handle dynamic form types
  handleCancel?: () => void;
  isSubmitting?: boolean;
}

export const TreatmentForm = (props: TreatmentFormProps) => {
  props.form.watch("concern");

  const { mutateAsync: mutateMediaUrl } = useUploadFile({
    onSuccess: (data) => {
      props.form.setValue("media_url", data);
    },
  });

  const { mutateAsync: mutateThumbnailUrl } = useUploadFile({
    onSuccess: (data) => {
      props.form.setValue("thumbnail_url", data);
    },
  });

  const [displayPrice, setDisplayPrice] = useState<string>(
    props.form.getValues("price") ? convertToRupiah(Number(props.form.getValues("price"))) : ""
  );

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove all non-numeric characters for internal value
    const numericValue = e.target.value.replace(/\D/g, "");
    setDisplayPrice(numericValue ? convertToRupiah(Number(numericValue)) : "");
    props.form.setValue("price", numericValue);
  };

  const handleAddConcern = () => {
    props.form.setValue("concern", [...props.form.getValues("concern"), ""]);
  };

  return (
    <>
      <FormField
        control={props.form.control}
        name="media_url"
        render={({ field }) => (
          <FormItem>
            <div className="flex flex-col space-y-2">
              <FormLabel>Upload Video</FormLabel>
              <span className="text-subtle text-secondary">You can only upload 1 file</span>
            </div>
            <FormControl>
              <FileUpload
                value={field.value}
                onChange={async (file) => {
                  if (file) {
                    await mutateMediaUrl(file);
                  } else {
                    props.form.setValue("media_url", undefined);
                  }
                }}
                maxSizeMB={1000}
                accept="video/*"
                label="Max size (1 GB)"
              />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={props.form.control}
        name="thumbnail_url"
        render={({ field }) => (
          <FormItem>
            <div className="flex flex-col space-y-2">
              <FormLabel>Thumbnail Image</FormLabel>
              <span className="text-subtle text-secondary">You can only upload 1 image file</span>
            </div>
            <FormControl>
              <FileUpload
                value={field.value}
                onChange={async (file) => {
                  if (file) {
                    await mutateThumbnailUrl(file);
                  } else {
                    props.form.setValue("thumbnail_url", undefined);
                  }
                }}
                maxSizeMB={20}
                accept="image/*"
                label="Max size (20 MB)"
              />
            </FormControl>
          </FormItem>
        )}
      />
      <TreatmentCategoryField form={props.form} />
      <div className="border border-border rounded-3xl p-4 space-y-4">
        <span className="text-lead font-medium mb-4 block">Concern</span>
        <TreatmentConcernField form={props.form} />
        {/* Add more concern button */}
        <Button
          type="button"
          variant="outline"
          className="w-full shadow-md"
          onClick={handleAddConcern}
        >
          Add More Concern
        </Button>
      </div>
      <FormField
        control={props.form.control}
        name="top_recommendation"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Will this Treatment be Prioritized for Top Recommendation *</FormLabel>
            <FormControl>
              <RadioGroup
                isStretched
                name={field.name}
                defaultValue={field.value}
                onValueChange={field.onChange}
                options={[
                  { value: "yes", label: "Yes" },
                  { value: "no", label: "No" },
                ]}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {props.form.watch("top_recommendation") === "yes" && (
        <FormField
          control={props.form.control}
          name="top_recommendation_end_date"
          render={({ field, formState }) => (
            <FormItem className="w-1/2">
              <FormLabel>Set Recommendation End Date</FormLabel>
              <FormControl>
                <DatePicker
                  prefix={<Calendar className="size-4 text-secondary" />}
                  value={field.value}
                  onChange={(value) => field.onChange(value)}
                  placeholder="Select Date"
                  error={formState.errors.top_recommendation_end_date !== undefined}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      {/* Description */}
      <FormField
        control={props.form.control}
        name="description"
        render={({ field, formState }) => (
          <FormItem>
            <FormLabel>Description *</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Enter description"
                {...field}
                error={formState.errors.description !== undefined}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={props.form.control}
        name="notes"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Notes</FormLabel>
            <FormControl>
              <Input autoComplete="off" placeholder="Enter notes" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <TreatmentIntervalField form={props.form} />

      <TreatmentSurveyField form={props.form} />

      <FormField
        control={props.form.control}
        name="price"
        render={({ field, formState }) => (
          <FormItem>
            <FormLabel>Price (Rp) *</FormLabel>
            <FormControl>
              <Input
                placeholder="Enter price"
                type="text"
                autoComplete="off"
                {...field}
                value={displayPrice}
                onChange={(e) => {
                  field.onChange(e);
                  handlePriceChange(e);
                }}
                error={formState.errors.price !== undefined}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};
