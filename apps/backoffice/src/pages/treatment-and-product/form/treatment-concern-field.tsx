import { TrashIcon } from "#/components/icons/trash";
import { SelectDropdown, SelectDropdownValue } from "#/components/select-dropdown";
import { FormField, FormItem, FormLabel, Button, FormControl, FormMessage } from "shared-ui";
import { UseFormReturn } from "react-hook-form";
import { TreatmentFormValues } from "../schema/treatment-product-form-schema";
import { PaginatedData } from "#/services/types/base";
import { SkinProblem } from "#/services/types/skin-problem";
import { useGetAllSkinProblems } from "#/context/hooks/use-get-skin-problem";

interface TreatmentConcernFieldProps {
  form: UseFormReturn<TreatmentFormValues>;
}

export const TreatmentConcernField = (props: TreatmentConcernFieldProps) => {
  const { form } = props;
  const { data } = useGetAllSkinProblems();
  const paginatedData = data?.data || [];
  let concerns: string[] = [];
  if (paginatedData) {
    concerns =
      (paginatedData as PaginatedData<SkinProblem>).content?.map((item) => item.name) || [];
  }

  const currentConcerns = form.getValues("concern") || [];

  const availableConcerns = concerns.filter((item) => !currentConcerns.includes(item));

  const removeConcern = (indexToRemove: number) => {
    const updated = [...form.getValues("concern")];
    updated.splice(indexToRemove, 1);
    form.setValue("concern", updated);
    form.trigger("concern");
  };

  return (
    <>
      {currentConcerns.map((_, index) => (
        <FormField
          key={index}
          control={form.control}
          name={`concern.${index}`}
          render={({ field, formState }) => (
            <FormItem className="flex flex-col">
              <div className="flex items-center justify-between">
                <FormLabel>Concern {index + 1} *</FormLabel>
                {index > 0 && (
                  <Button
                    type="button"
                    variant="link"
                    size="icon"
                    onClick={() => removeConcern(index)}
                  >
                    <TrashIcon className="size-4 text-destructive" />
                  </Button>
                )}
              </div>
              <span className="text-subtle text-secondary">Please select concern</span>
              <FormControl>
                <SelectDropdown
                  trigger={
                    <SelectDropdownValue placeholder="Select Concern">
                      {field.value}
                    </SelectDropdownValue>
                  }
                  items={availableConcerns}
                  {...field}
                  onBlur={() => form.trigger(`concern.${index}`)}
                  error={formState.errors.concern?.[index] !== undefined}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      ))}
    </>
  );
};
