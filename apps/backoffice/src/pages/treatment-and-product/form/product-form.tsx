import { FormType } from "#/services/types/form-type";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Textarea,
} from "shared-ui";
import { FileUpload } from "#/components/file-upload";
import { useUploadFile } from "../mutation/use-upload-file";
import { convertToRupiah } from "#/utils/number-helper";
import { useState } from "react";

interface ProductFormProps {
  formType?: FormType;
  form: UseFormReturn<any>; // Use any here to handle dynamic form types
  handleCancel?: () => void;
  isSubmitting?: boolean;
}

export const ProductForm = (props: ProductFormProps) => {
  const { mutateAsync } = useUploadFile({
    onSuccess: (data) => {
      props.form.setValue("media_url", data);
    },
  });

  const [displayPrice, setDisplayPrice] = useState<string>(
    props.form.getValues("price") ? convertToRupiah(Number(props.form.getValues("price"))) : ""
  );

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove all non-numeric characters for internal value
    const numericValue = e.target.value.replace(/\D/g, "");
    setDisplayPrice(numericValue ? convertToRupiah(Number(numericValue)) : "");
    props.form.setValue("price", numericValue);
  };

  return (
    <div className="space-y-4">
      {/* Photo */}
      <FormField
        control={props.form.control}
        name="media_url"
        render={({ field }) => (
          <FormItem>
            <div className="flex flex-col space-y-2">
              <FormLabel>Photo</FormLabel>
              <span className="text-subtle text-secondary">You can only upload 1 file</span>
            </div>
            <FormControl>
              <FileUpload
                value={field.value}
                onChange={async (file) => {
                  if (file) {
                    await mutateAsync(file);
                  } else {
                    props.form.setValue("media_url", undefined);
                  }
                }}
                maxSizeMB={20}
                accept="image/*"
                label="Max size (20 MB)"
              />
            </FormControl>
          </FormItem>
        )}
      />
      {/* Description */}
      <FormField
        control={props.form.control}
        name="description"
        render={({ field, formState }) => (
          <FormItem>
            <FormLabel>Description *</FormLabel>
            <FormControl>
              <Textarea placeholder="Enter description" {...field} error={formState.errors.description !== undefined} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={props.form.control}
        name="price"
        render={({ field, formState }) => (
          <FormItem>
            <FormLabel>Price (Rp) *</FormLabel>
            <FormControl>
              <Input
                type="text"
                autoComplete="off"
                placeholder="Enter price"
                error={formState.errors.price !== undefined}
                {...field}
                value={displayPrice}
                onChange={(e) => {
                  field.onChange(e);
                  handlePriceChange(e);
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
