import { FormField, FormItem, FormLabel, FormMessage } from "shared-ui";
import { UseFormReturn } from "react-hook-form";
import { TreatmentFormValues } from "../schema/treatment-product-form-schema";
import { SelectDropdown, SelectDropdownValue } from "#/components/select-dropdown";
import { useGetAllTreatmentIntervals } from "#/context/hooks/use-get-treatment-interval";
import { PaginatedData } from "#/services/types/base";
import { TreatmentInterval } from "#/services/types/treatment-interval";

interface TreatmentIntervalFieldProps {
  form: UseFormReturn<TreatmentFormValues>;
}

export const TreatmentIntervalField = (props: TreatmentIntervalFieldProps) => {
  const { data } = useGetAllTreatmentIntervals();
  const paginatedData = data?.data || [];
  let treatmentIntervals: string[] = [];
  if (paginatedData) {
    treatmentIntervals =
      (paginatedData as PaginatedData<TreatmentInterval>).content?.map((item) => item.days.toString()) || [];
  }
  return (
    <FormField
      control={props.form.control}
      name="treatment_interval"
      render={({ field, formState }) => (
        <FormItem>
          <FormLabel>Treatment Interval (days) *</FormLabel>
          <SelectDropdown
            trigger={
              <SelectDropdownValue placeholder="Select Treatment Interval">
                {field.value || "Daily"}
              </SelectDropdownValue>
            }
            items={treatmentIntervals}
            {...field}
            error={formState.errors.treatment_interval !== undefined}
          />
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
