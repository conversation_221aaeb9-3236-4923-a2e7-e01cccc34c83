import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "shared-ui";
import { UseFormReturn } from "react-hook-form";
import { TreatmentFormValues } from "../schema/treatment-product-form-schema";
import { useGetAllContraindicationSurveys } from "#/context/hooks/use-get-survey";
import RadioGroup from "#/components/radio-group";
import { useEffect, useState } from "react";
import { Survey } from "#/services/types/survey";

interface TreatmentSurveyFieldProps {
  form: UseFormReturn<TreatmentFormValues>;
}

export const TreatmentSurveyField = (props: TreatmentSurveyFieldProps) => {
  const [surveys, setSurveys] = useState<Survey[]>([]);
  // Use the hook directly, not inside useRef
  const { data } = useGetAllContraindicationSurveys();

  useEffect(() => {
    // Safely access nested properties
    if (data?.data?.content) {
      setSurveys(data.data.content);
    } else {
      setSurveys([]);
    }
  }, [data]);

  // If no surveys or empty array, don't render anything
  if (!surveys || surveys.length === 0) {
    return null;
  }

  const handleSurveyChange = (id: string, answer: string) => {
    const selectedAnswerIndex = surveys
      .find((s) => s.id === id)
      ?.answers?.findIndex((a) => a.title === answer);

    if (selectedAnswerIndex === undefined) return;

    const { survey_questions } = props.form.getValues();

    let newValues;

    if (survey_questions?.some((s) => s && s.survey_question_id === id)) {
      newValues = [
        ...survey_questions.filter((s) => s && s.survey_question_id !== id),
        { survey_question_id: id, selected_answer: selectedAnswerIndex },
      ];
      props.form.setValue("survey_questions", newValues);
    } else {
      newValues = [
        ...(survey_questions?.filter((s) => s) || []),
        { survey_question_id: id, selected_answer: selectedAnswerIndex },
      ];
      props.form.setValue("survey_questions", newValues);
    }

    // Validate that all surveys have answers
    const allSurveysAnswered = surveys.every((survey) => {
      return newValues?.some((sq) => sq && sq.survey_question_id === survey.id);
    });

    // Update form validation
    if (allSurveysAnswered) {
      props.form.trigger("survey_questions");
      props.form.clearErrors("survey_questions");
    } else {
      props.form.setError("survey_questions", { message: "All survey questions must be answered" });
    }
  };

  return (
    <>
      {surveys.map((survey, index) => {
        // Skip rendering if survey is missing required properties
        if (!survey || !survey.question || !survey.answers) {
          return null;
        }

        return (
          <FormField
            key={index}
            control={props.form.control}
            name={`survey_questions.${index}`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{survey.question} *</FormLabel>
                <FormControl>
                  <RadioGroup
                    isStretched
                    name={field.name}
                    defaultValue={
                      typeof field.value?.selected_answer === "number"
                        ? survey.answers[field.value.selected_answer].title
                        : ""
                    }
                    options={
                      Array.isArray(survey.answers)
                        ? survey.answers.map((answer) => ({
                            value: answer?.title || "",
                            label: answer?.title || "",
                          }))
                        : []
                    }
                    onValueChange={(answer) => {
                      handleSurveyChange(survey.id, answer);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );
      })}
    </>
  );
};
