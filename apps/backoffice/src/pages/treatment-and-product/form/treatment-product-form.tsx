import { FormType } from "#/services/types/form-type";
import {
  <PERSON><PERSON>,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from "shared-ui";
import {
  TreatmentFormValues,
  baseSchema,
  ProductFormValues,
  treatmentSchema,
  productSchema,
} from "../schema/treatment-product-form-schema";
import { useForm, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { SelectDropdown, SelectDropdownValue } from "#/components/select-dropdown";
import { ConfirmDialog } from "#/components/confirm-dialog";
import { UserPlus, EditPencil } from "iconoir-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { TreatmentForm } from "./treatment-form";
import { ProductForm } from "./product-form";
import { TreatmentProductType } from "#/services/types/treatment-product-type";
import { useGetAllSkinProblems } from "#/context/hooks/use-get-skin-problem";
import { useGetAllTreatmentCategories } from "#/context/hooks/use-get-treatment-category";
import { useGetAllTreatmentIntervals } from "#/context/hooks/use-get-treatment-interval";
import { TreatmentProductBodyRequest } from "#/services/types/treatment-product";
import { convertToNumber } from "#/utils/number-helper";

interface TreatmentProductFormProps {
  formType?: FormType;
  defaultValues?: any;
  onSubmit: (values: TreatmentProductBodyRequest) => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
}

// Type for the form values
type FormValues = TreatmentFormValues | ProductFormValues;

export const TreatmentProductForm = (props: TreatmentProductFormProps) => {
  const { isSubmitting, formType, onSubmit, onCancel } = props;
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);

  // Initialize form with base schema
  const [currentType, setCurrentType] = useState<TreatmentProductType>(
    props.defaultValues?.type as TreatmentProductType
  );

  // Create a new form instance when the type changes
  const form = useForm<FormValues>({
    resolver: zodResolver(
      currentType === TreatmentProductType.TREATMENT
        ? baseSchema.merge(treatmentSchema)
        : baseSchema.merge(productSchema)
    ),
    defaultValues: {
      item_code: "",
      name: "",
      ...props.defaultValues,
      type: currentType,
    } as any,
    mode: "all",
  });

  // Handle type changes
  const handleTypeChange = (type: string) => {
    if (type === "") return;
    const newType = TreatmentProductType.fromString(type);
    if (newType !== currentType) {
      setCurrentType(newType);
      // Reset form with new type and clear all values except type
      form.reset({
        item_code: form.getValues("item_code"),
        name: form.getValues("name"),
        type: newType,
        ...(newType === TreatmentProductType.TREATMENT
          ? {
              concern: [""],
              survey: [],
            }
          : {
              description: "",
            }),
      } as any);
    }
  };

  const { data: categories } = useGetAllTreatmentCategories();
  const { data: concerns } = useGetAllSkinProblems();
  const { data: intervals } = useGetAllTreatmentIntervals();

  const handleSubmit = () => {
    if (onSubmit) {
      if (currentType === TreatmentProductType.TREATMENT) {
        const formValues = form.getValues() as TreatmentFormValues;

        const values = {
          ...formValues,
          price: convertToNumber(formValues.price),
          category_ids: formValues.category.map(
            (category) => categories?.data?.content?.find((c) => c.name === category)?.id
          ),
          concern_ids: formValues.concern.map(
            (concern) => concerns?.data?.content?.find((c) => c.name === concern)?.id
          ),
          interval_id: intervals?.data?.content?.find(
            (i) => i.days.toString() === formValues.treatment_interval
          )?.id,
          is_top_recommendation: formValues.top_recommendation === "yes",
          ...(formValues.top_recommendation === "yes"
            ? {
                duration_top_recommendation:
                  formValues.top_recommendation_end_date?.getTime() || Date.now(),
              }
            : { duration_top_recommendation: undefined }),
          survey_questions: formValues.survey_questions?.filter(
            (question) =>
              question?.survey_question_id !== undefined && question?.selected_answer !== undefined
          ),
        } as TreatmentProductBodyRequest;
        onSubmit(values);
      } else if (currentType === TreatmentProductType.PRODUCT) {
        const formValues = form.getValues() as ProductFormValues;
        const values = {
          ...formValues,
          price: convertToNumber(formValues.price),
        } as TreatmentProductBodyRequest;
        onSubmit(values);
      }
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    navigate(-1);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          setOpenDialog(true);
        }}
        onInvalid={(e) => console.log("Invalid form event:", e)}
        className="space-y-4"
      >
        <h1 className="text-large">*Required Information</h1>
        <FormField
          control={form.control}
          name="item_code"
          render={({ field, formState }) => (
            <FormItem>
              <FormLabel>Item Code *</FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  placeholder="Enter item code"
                  {...field}
                  error={formState.errors.item_code !== undefined}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="name"
          render={({ field, formState }) => (
            <FormItem>
              <FormLabel>Name *</FormLabel>
              <FormControl>
                <Input
                  autoComplete="off"
                  placeholder="Enter name"
                  {...field}
                  error={formState.errors.name !== undefined}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type *</FormLabel>
              <FormControl>
                <SelectDropdown
                  trigger={
                    <SelectDropdownValue placeholder="Select type">
                      {field.value == undefined
                        ? "Select type"
                        : TreatmentProductType.getDisplayValue(field.value)}
                    </SelectDropdownValue>
                  }
                  items={TreatmentProductType.getAllDisplayValues()}
                  onChange={(value) => handleTypeChange(value)}
                  value={currentType}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {currentType === TreatmentProductType.TREATMENT && (
          <TreatmentForm
            form={form as UseFormReturn<TreatmentFormValues>}
            formType={formType}
            handleCancel={handleCancel}
            isSubmitting={isSubmitting}
          />
        )}
        {currentType === TreatmentProductType.PRODUCT && (
          <ProductForm
            form={form}
            formType={formType}
            handleCancel={handleCancel}
            isSubmitting={isSubmitting}
          />
        )}

        {/* Submit Button */}
        <div className="flex gap-4">
          <Button
            type="button"
            variant="outline"
            className="w-full shadow-md"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="w-full shadow-md"
            disabled={isSubmitting || !form.formState.isValid}
          >
            {isSubmitting
              ? "Loading..."
              : formType === FormType.CREATE
              ? `Create ${currentType || ""}`
              : "Save Changes"}
          </Button>
        </div>
      </form>

      <ConfirmDialog
        openDialog={openDialog}
        onCancel={() => setOpenDialog(false)}
        onSubmit={handleSubmit}
        title={formType === FormType.CREATE ? `Create New ${currentType}?` : "Save Changes?"}
        icon={
          formType === FormType.CREATE ? (
            <UserPlus className="size-24" />
          ) : (
            <EditPencil className="size-24" />
          )
        }
        description={
          formType === FormType.CREATE
            ? "Are you sure you want to create a new treatment product with the entered information?"
            : "Are you sure you want to save the changes to this treatment product's information?"
        }
        submitText={
          formType === FormType.CREATE ? `Yes, Create ${currentType}` : "Yes, Save Changes"
        }
      />
    </Form>
  );
};
