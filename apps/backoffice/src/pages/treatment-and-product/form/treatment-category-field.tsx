import { TagInput } from "#/components/tag-input";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "shared-ui";
import { UseFormReturn } from "react-hook-form";
import { TreatmentFormValues } from "../schema/treatment-product-form-schema";
import { useGetAllTreatmentCategories } from "#/context/hooks/use-get-treatment-category";
import { TreatmentCategory } from "#/services/types/treatment-category";
import { PaginatedData } from "#/services/types/base";

interface TreatmentCategoryFieldProps {
  form: UseFormReturn<TreatmentFormValues>;
}

export const TreatmentCategoryField = (props: TreatmentCategoryFieldProps) => {
  const { data } = useGetAllTreatmentCategories();
  const paginatedData = data?.data || [];
  let categories: string[] = [];
  if (paginatedData) {
    categories =
      (paginatedData as PaginatedData<TreatmentCategory>).content?.map((item) => item.name) || [];
  }
  const { form } = props;
  return (
    <FormField
      control={form.control}
      name="category"
      render={({ field, formState }) => (
        <FormItem>
          <FormLabel>Category *</FormLabel>
          <FormControl>
            <TagInput
              placeholder="Select Category"
              inputPlaceholder="Type here to add category"
              items={categories}
              error={formState.errors.category !== undefined}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
