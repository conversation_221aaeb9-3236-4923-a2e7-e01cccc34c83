import { useApiClient } from "#/context/hooks/api-client";
import { MutateOptions, useMutation } from "@tanstack/react-query";
import {
  TreatmentProductBodyRequest,
  TreatmentProductResponse,
} from "#/services/types/treatment-product";

/**
 * Hook to create a single treatment product.
 *
 * Sequential creation of treatment products should be handled in the page component.
 */
export const useCreateTreatmentProduct = (
  options: MutateOptions<
    TreatmentProductResponse,
    Error,
    Partial<TreatmentProductBodyRequest>,
    unknown
  >
) => {
  const apiClient = useApiClient();
  return useMutation({
    mutationFn: async (data: TreatmentProductBodyRequest) => {
      return await apiClient.treatmentProduct.create(data as TreatmentProductBodyRequest);
    },
    ...options,
  });
};
