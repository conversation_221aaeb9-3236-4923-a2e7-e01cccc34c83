import { useApiClient } from "#/context/hooks/api-client";
import { MutateOptions, useMutation } from "@tanstack/react-query";
import {
  TreatmentProductBodyRequest,
  TreatmentProductResponse,
} from "#/services/types/treatment-product";

/**
 * Hook to update a single treatment product.
 *
 * Sequential updating of treatment products should be handled in the page component.
 */
export const useUpdateTreatmentProduct = (
  options: MutateOptions<
    TreatmentProductResponse,
    Error,
    Partial<TreatmentProductBodyRequest>,
    unknown
  >
) => {
  const apiClient = useApiClient();
  return useMutation({
    mutationFn: async (data: TreatmentProductBodyRequest) => {
      return await apiClient.treatmentProduct.update(
        data.id ?? "",
        data as TreatmentProductBodyRequest
      );
    },
    ...options,
  });
};
