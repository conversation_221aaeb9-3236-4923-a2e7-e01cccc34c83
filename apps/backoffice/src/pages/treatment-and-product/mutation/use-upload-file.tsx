import { useApiClient } from "#/context/hooks/api-client";
import { MutateOptions, useMutation } from "@tanstack/react-query";

export const useUploadFile = (
  options: MutateOptions<string, Error, File, unknown>
) => {
  const apiClient = useApiClient();

  return useMutation({
    mutationFn: async (file: File) => {
      const presigned = await apiClient.mediaUpload.presignUrl(file.name);
      await apiClient.mediaUpload.uploadFile(presigned.data.url, file);
      return presigned.data.object_key;
    },
    ...options,
  });
};
