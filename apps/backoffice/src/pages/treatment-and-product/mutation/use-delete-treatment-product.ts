import { useApiClient } from "#/context/hooks/api-client";
import { MutateOptions, useMutation } from "@tanstack/react-query";
import {
  TreatmentProductResponse,
} from "#/services/types/treatment-product";

/**
 * Hook to delete a single treatment product.
 *
 * Sequential deleting of treatment products should be handled in the page component.
 */
export const useDeleteTreatmentProduct = (
  options: MutateOptions<TreatmentProductResponse, Error, string, unknown>
) => {
  const apiClient = useApiClient();
  return useMutation({
    mutationFn: async (data: string) => {
      return await apiClient.treatmentProduct.delete(data);
    },
    ...options,
  });
};
