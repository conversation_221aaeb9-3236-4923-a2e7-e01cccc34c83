import { Row } from "@tanstack/react-table";
import { TreatmentProduct } from "#/services/types/treatment-product";
import { ClickTooltip } from "#/components/click-tooltip";

interface DataTableCategoryCellProps {
  row: Row<TreatmentProduct>;
}

export default function DataTableCategoryCell(props: DataTableCategoryCellProps) {
  const { row } = props;
  const isMoreThanOne = row.original.category && row.original.category?.length > 1;

  return (
    <div className="flex gap-1 items-center">
      {row.original.type === "product" ? "-" : <div>{row.original.category?.[0].name}{isMoreThanOne ? "," : ""}</div>}
      {isMoreThanOne ? (
        <ClickTooltip 
          content={row.original.category?.map((category) => category.name).join(", ")}
          className="bg-white text-foreground border border-neutral-200 p-3 whitespace-normal"
        >
          <span className="text-primary">+{row.original.category && row.original.category?.length - 1}</span>
        </ClickTooltip>
      ) : (
        ""
      )}
    </div>
  );
}
