import { Input } from "#/components/base";
import { DataTable } from "#/components/data-table";
import { Plus, Search } from "iconoir-react";
import { Button } from "shared-ui";
import { treatmentProductColumns } from "./data-table";
import { useApiClient } from "#/context/hooks/api-client";
import { usePaginationQuery } from "#/context/hooks/use-pagination";
import {
  TreatmentProductPaginatedResponse,
} from "#/services/types/treatment-product";
import { useNavigate } from "react-router-dom";
import { treatmentAndProductFilterItems } from "./filter-items";
import { FilterProvider } from "#/context/providers/filter-provider";
import { FilterDropdown } from "#/components/filter-dropdown";
import { FilterList } from "#/components/filter-list";
import { useGetAllTreatmentCategories } from "#/context/hooks/use-get-treatment-category";
import { FilterFieldType } from "#/constants/filter-field-type";
import { useMemo } from "react";

export default function TreatmentAndProductListPage() {
  const apiClient = useApiClient();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);

  const { data, page, search, setSearch, queryParamsChanged } =
    usePaginationQuery<TreatmentProductPaginatedResponse>({
      queryKey: ["treatment-products", queryParams.toString()],
      queryFn: (params) =>
        apiClient.treatmentProduct.getAll({
          ...params,
          category_ids: queryParams.getAll("category"),
          types: queryParams.getAll("type"),
          min_price: queryParams.get("price")
            ? Number.parseInt(queryParams.get("price")?.split("-")[0] ?? "0")
            : undefined,
          max_price: queryParams.get("price")
            ? Number.parseInt(queryParams.get("price")?.split("-")[1] ?? "0")
            : undefined,
        }),
    });

  const { data: categoryData } = useGetAllTreatmentCategories();

  // Generate a unique key for FilterProvider whenever categoryData changes
  const filterProviderKey = useMemo(() => {
    return categoryData ? `filter-provider-${Date.now()}` : 'initial-filter-provider';
  }, [categoryData]);

  const treatmentAndProductFilterItemsWithCategory = [
    {
      label: "Category",
      type: FilterFieldType.MultiSelect,
      options:
        categoryData?.data?.content?.map((item) => ({
          label: item.name,
          value: item.id,
        })) ?? [],
    },
    ...treatmentAndProductFilterItems,
  ];

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value;
    setSearch(searchValue);
  };
  const onPageChanged = (value: number) => queryParamsChanged("page", value.toString());
  const onPageSizeChanged = (value: number) => queryParamsChanged("page_size", value.toString());

  const { content, total_pages, page_size } = data?.data ?? {};

  return (
    <div className="rounded-xl bg-background p-6 shadow-md">
      <div className="flex flex-col gap-4">
        <FilterProvider
          key={filterProviderKey}
          filterItems={treatmentAndProductFilterItemsWithCategory}
        >
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Input
                name="search"
                placeholder="Search Name"
                className="w-64"
                prefixIcon={<Search className="size-5" />}
                value={search}
                onChange={handleSearch}
              />
              <FilterDropdown />
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => navigate("/treatment-and-product/create")}>
                <Plus className="size-5" />
                <span>Create Treatment & Product</span>
              </Button>
            </div>
          </div>
          <FilterList />
        </FilterProvider>
        <DataTable
          columns={treatmentProductColumns}
          data={content ?? []}
          page={page}
          totalPage={total_pages ?? 1}
          pageSize={page_size ?? 50}
          onPageSizeChanged={onPageSizeChanged}
          onPageChanged={onPageChanged}
        />
      </div>
    </div>
  );
}
