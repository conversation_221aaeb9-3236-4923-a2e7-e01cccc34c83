import React from "react";
import { <PERSON><PERSON>, toast } from "shared-ui";
import { useNavigate } from "react-router-dom";
import { ConfirmDialog, ConfirmDialogType } from "#/components/confirm-dialog";
import { TrashIcon } from "#/components/icons/trash";
import { Row } from "@tanstack/react-table";
import { TreatmentProduct } from "#/services/types/treatment-product";
import { useDeleteTreatmentProduct } from "../mutation/use-delete-treatment-product";
import { useQueryClient } from "@tanstack/react-query";

export default function DataTableAction({ row }: { row: Row<TreatmentProduct> }) {
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = React.useState(false);
  const queryClient = useQueryClient();

  const { mutate: deleteTreatmentProduct } = useDeleteTreatmentProduct({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["treatment-products"],
      });
      toast({
        title: "Treatment Product Deleted Successfully",
        description: "The treatment product has been deleted successfully.",
      });
    },
    onError: () => {
      setOpenDialog(false);
      toast({
        variant: "destructive",
        title: "Treatment Product Deletion Failed",
        description: "The treatment product has not been deleted.",
      });
    },
  });

  const handleDialogCancel = () => {
    setOpenDialog(false);
  };

  const handleDialogDelete = () => {
    setOpenDialog(false);
    deleteTreatmentProduct(row.original.id);
  };

  return (
    <>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          onClick={() => navigate("/treatment-and-product/edit/" + row.original.id)}
          size="sm"
          className="px-8"
        >
          Edit
        </Button>
        <Button
          size="icon"
          className="size-8 text-destructive border-destructive-400 hover:bg-destructive hover:text-background"
          variant="outline"
          onClick={() => setOpenDialog(true)}
        >
          <TrashIcon className="h-4 w-4" />
        </Button>
      </div>
      <ConfirmDialog
        openDialog={openDialog}
        onCancel={handleDialogCancel}
        onSubmit={handleDialogDelete}
        title="Delete Treatment Product ?"
        icon={<TrashIcon />}
        description="Are you sure you want to delete this treatment product?"
        submitText="Yes, Delete"
        type={ConfirmDialogType.DELETE}
      />
    </>
  );
}
