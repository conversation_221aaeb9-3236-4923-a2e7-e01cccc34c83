import type { TreatmentProduct } from '#/services/types/treatment-product'
import type { ColumnDef } from '@tanstack/react-table'
import { convertToRupiah } from '#/utils/number-helper'
import DataTableAction from './data-table-action'
import { TreatmentProductType } from '#/services/types/treatment-product-type'
import DataTableCategoryCell from './data-table-category-cell'
import DataTableNameCell from '#/pages/skin-concern-indication/data-table-name-cell'

export const treatmentProductColumns: ColumnDef<TreatmentProduct>[] = [
  {
    accessorKey: "item_code",
    header: "Item Code",
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => {
      return <DataTableNameCell row={row} />;
    }
  },
  {
    accessorKey: 'category',
    header: 'Category',
    cell: ({ row }) => {
      return <DataTableCategoryCell row={row} />;
    },
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const type = row.original.type;
      return TreatmentProductType.getDisplayValue(type);
    }
  },
  {
    accessorKey: "price",
    header: "Price (Rp)",
    cell: ({ row }) => convertToRupiah(row.getValue("price")),
  },
  {
    header: "Action",
    cell: ({ row }) => {
      return <DataTableAction row={row} />;
    },
  },
];
