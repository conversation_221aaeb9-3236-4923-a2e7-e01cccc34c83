import { FormType } from "#/services/types/form-type";
import { useLocation, useNavigate } from "react-router-dom";
import { useToast } from "shared-ui";
import { useUpdateTreatmentProduct } from "../mutation/use-update-treatment-product";
import { useGetTreatmentProduct } from "#/context/hooks/use-treatment-product";
import { TreatmentProductForm } from "../form/treatment-product-form";
import { useQueryClient } from "@tanstack/react-query";
import { getMediaFilenameFromUrl } from "#/utils/media-url-helper";

export default function EditTreatmentProductPage() {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { mutate, isPending } = useUpdateTreatmentProduct({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["treatment-product"],
      });
      navigate(`/treatment-and-product`, { replace: true });
      toast({
        title: "Treatment Product Updated Successfully",
        description: "The treatment product has been updated successfully.",
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Treatment Product Update Failed",
        description: "The treatment product has not been updated.",
      });
    },
  });

  const uid = pathname.split("/").pop();
  const { data, isLoading } = useGetTreatmentProduct(uid ?? "");
  const treatmentProductData = {
    id: data?.data.id ?? undefined,
    item_code: data?.data.item_code ?? undefined,
    name: data?.data.name ?? undefined,
    type: data?.data.type ?? undefined,
    category: data?.data.category?.map((item) => item.name) ?? [],
    concern: data?.data.concern?.map((item) => item.name) ?? [],
    top_recommendation: data?.data.is_top_recommendation === true ? "yes" : "no",
    price: data?.data.price.toString() ?? undefined,
    notes: data?.data.notes ?? undefined,
    treatment_interval: data?.data.interval?.days.toString() ?? undefined,
    description: data?.data.description ?? undefined,
    media_url: data?.data.media_url ? getMediaFilenameFromUrl(data?.data.media_url) : undefined,
    thumbnail_url: data?.data.thumbnail_url ? getMediaFilenameFromUrl(data?.data.thumbnail_url) : undefined,
    top_recommendation_end_date: data?.data.duration_top_recommendation
      ? new Date(data?.data.duration_top_recommendation)
      : undefined,
    survey_questions: data?.data.survey_questions?.map((item) => ({
      survey_question_id: item.id,
      selected_answer: item.selected_answer,
    })) ?? [],
  };

  return (
    <>
      <div className="p-6 rounded-xl bg-background shadow-md max-w-2xl">
        <h4 className="text-heading-4 mb-4">Edit Treatment & Product</h4>
        {isLoading ? (
          <span>Loading...</span>
        ) : (
          <TreatmentProductForm
            onSubmit={(treatmentProduct) => mutate(treatmentProduct)}
            formType={FormType.EDIT}
            isSubmitting={isPending}
            defaultValues={treatmentProductData}
          />
        )}
      </div>
    </>
  );
}
