import { Row } from "@tanstack/react-table";
import { User } from "#/services/types/user";
import { MoreHoriz } from "iconoir-react";
import { ClickTooltip } from "#/components/click-tooltip";

export default function DataTableUserID({ row }: { row: Row<User> }) {
  return (
    <ClickTooltip
      className="bg-white text-foreground border border-neutral-200 whitespace-normal"
      content={row.original.id}
    >
      <div className="flex items-center gap-1.5">
        <span>{row.original.id.split("-").shift()}</span>
        <MoreHoriz className="size-6 cursor-pointer" />
      </div>
    </ClickTooltip>
  );
}
