import { Input } from "#/components/base";
import { DataTable } from "#/components/data-table";
import { Plus, Search } from "iconoir-react";
import { Button } from "shared-ui";
import { userColumns } from "./data-table";
import { useNavigate } from "react-router-dom";
import { useApiClient } from "#/context/hooks/api-client";
import { usePaginationQuery } from "#/context/hooks/use-pagination";
import { UserPaginatedResponse } from "#/services/types/user";
import { FilterProvider } from "#/context/providers/filter-provider";
import { FilterDropdown } from "#/components/filter-dropdown";
import { FilterList } from "#/components/filter-list";
import { userFilterItems } from "./filter-items";

export default function UserPage() {
  const navigate = useNavigate();
  const apiClient = useApiClient();

  const { data, page, search, setSearch, queryParamsChanged } = usePaginationQuery<UserPaginatedResponse>({
    queryKey: ["users"],
    queryFn: (params) => apiClient.user.getAll(params),
  });

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value;
    setSearch(searchValue);
  };
  const onPageChanged = (value: number) => queryParamsChanged("page", value.toString());
  const onPageSizeChanged = (value: number) => queryParamsChanged("page_size", value.toString());

  const { content, total_pages, page_size } = data?.data ?? {};

  return (
    <div className="p-6 rounded-xl bg-background shadow-md">
      <div className="flex flex-col gap-4">
        <FilterProvider filterItems={userFilterItems}>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Input
                name="search"
                placeholder="Search user"
                className="w-64"
                prefixIcon={<Search className="size-5" />}
                value={search}
                onChange={handleSearch}
              />
              <FilterDropdown />
            </div>
            <div className="flex gap-2 items-center">
              <Button onClick={() => navigate("create")}>
                <Plus className="size-5" />
                <span className="text-small">Create User</span>
              </Button>
            </div>
          </div>
          <FilterList />
        </FilterProvider>
        <DataTable
          columns={userColumns}
          data={content ?? []}
          page={page}
          totalPage={total_pages ?? 1}
          pageSize={page_size ?? 50}
          onPageSizeChanged={onPageSizeChanged}
          onPageChanged={onPageChanged}
        />
      </div>
    </div>
  );
}
