import { User } from "#/services/types/user";
import { getDateString } from "#/utils/get-date-string";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "shared-ui";
import { useNavigate } from "react-router-dom";
import { capitalizeEveryWord } from "#/utils/string-helper";
import DataTableUserID from "./data-table-user-id";

export const userColumns: ColumnDef<User>[] = [
  {
    accessorKey: "id",
    header: "User ID",
    cell: ({ row }) => <DataTableUserID row={row} />,
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "role",
    header: "Role",
    cell: ({ row }) => {
      const role = row.original.role;
      return capitalizeEveryWord(role);
    },
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "phone_number",
    header: "Phone Number",
  },

  {
    accessorKey: "created_at",
    header: "Created Date",
    cell: ({ row }) => {
      const date = new Date(row.original.created_at ?? 0);
      return getDateString(date);
    },
  },
  {
    header: "Action",
    cell: ({ row }) => {
      const navigate = useNavigate();
      return (
        <Button size="sm" className="px-8" onClick={() => navigate(row.original.id.toString())}>
          See Detail
        </Button>
      );
    },
  },
];
