import { Button } from "shared-ui";
import { UserDetaiItem } from "./user-detail-item";
import { NavArrowRight } from "iconoir-react";
import { useNavigate } from "react-router-dom";
import { ConfirmDialog, ConfirmDialogType } from "#/components/confirm-dialog";
import { useState } from "react";
import { TrashIcon } from "#/components/icons/trash";
import { useGetUser } from "#/context/hooks/use-get-user";
import { useLocation } from "react-router-dom";
import { getDateString } from "#/utils/get-date-string";
import { useDeleteUser } from "../mutation/use-delete-user";
import { useToast } from "shared-ui";
import { getPascalCase } from "#/utils/string-helper";
import { useAuth } from "#/context/hooks/use-auth";

export default function UserDetailPage() {
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const { pathname } = useLocation();
  const { toast } = useToast();

  const { mutate: deleteUser, isPending } = useDeleteUser({
    onSuccess: () => {
      navigate("/user", { replace: true });
      toast({
        title: "User Deleted Successfully",
        description: "The user has been deleted successfully.",
      });
    },
    onError: () => {
      setOpenDialog(false);
      toast({
        variant: "destructive",
        title: "User Deletion Failed",
        description: "The user has not been deleted.",
      });
    },
  });

  const handleDialogCancel = () => {
    setOpenDialog(false);
  };

  const handleDialogDelete = () => {
    setOpenDialog(false);
    deleteUser(uid ?? "");
  };

  const uid = pathname.split("/").pop();
  const { data, isLoading } = useGetUser(uid ?? "");
  const user = data?.data;

  const { user: loggedInUser } = useAuth();

  return (
    <>
      <div className="p-6 rounded-xl bg-background shadow-md max-w-2xl">
        <h4 className="text-heading-4 mb-4">User Detail</h4>
        {isLoading ? (
          <span>Loading...</span>
        ) : (
          <div className="flex flex-col space-y-4">
            <UserDetaiItem label="User ID" value={user?.id ?? "-"} />
            <UserDetaiItem label="Name" value={user?.name ?? "-"} />
            <UserDetaiItem label="Role" value={getPascalCase(user?.role ?? "-")} />
            <UserDetaiItem label="Email" value={user?.email ?? "-"} />
            <UserDetaiItem label="Phone Number" value={user?.phone_number ?? "-"} />
            <UserDetaiItem label="Address" value={user?.address ?? "-"} />
            <UserDetaiItem
              label="Created Date"
              value={user?.created_at ? getDateString(new Date(user.created_at)) : "-"}
            />
            <Button variant="outline" className="shadow-md">
              <span>See Branch History</span>
              <NavArrowRight className="ml-2 size-4" />
            </Button>
            <div className="flex w-full gap-2">
              <Button
                variant="outline"
                className="shadow-md w-full"
                onClick={() => {
                  navigate(`/user/edit/${uid}`);
                }}
              >
                <span>Edit</span>
              </Button>
              <Button
                variant="outline"
                className="shadow-md w-full bg-destructive-50 text-destructive hover:bg-destructive-100 hover:text-destructive-500"
                disabled={isPending || loggedInUser?.id === uid}
                onClick={() => {
                  setOpenDialog(true);
                }}
              >
                <span>{isPending ? "Deleting..." : "Delete"}</span>
              </Button>
            </div>
          </div>
        )}
      </div>

      <ConfirmDialog
        openDialog={openDialog}
        onCancel={handleDialogCancel}
        onSubmit={handleDialogDelete}
        title="Delete User ?"
        icon={<TrashIcon />}
        description="Are you sure you want to delete this user?"
        submitText="Yes, Delete"
        type={ConfirmDialogType.DELETE}
      />
    </>
  );
}
