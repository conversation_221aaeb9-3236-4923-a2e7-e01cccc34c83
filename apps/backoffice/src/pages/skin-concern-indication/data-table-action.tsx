import React from "react";
import { Button } from "shared-ui";
import { ConfirmDialog, ConfirmDialogType } from "#/components/confirm-dialog";
import { TrashIcon } from "#/components/icons/trash";
import { Row } from "@tanstack/react-table";
import { SkinProblemIndication } from "#/services/types/skin-problem-indication";

interface DataTableActionProps {
  row: Row<SkinProblemIndication>;
}

export default function DataTableAction({ row }: DataTableActionProps) {
  const [openDialog, setOpenDialog] = React.useState(false);

  const handleDialogCancel = () => {
    setOpenDialog(false);
  };

  const handleDialogDelete = () => {
    setOpenDialog(false);
  };

  return (
    <>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          onClick={() => { }}
          size="sm"
          className="px-8"
        >
          Edit
        </Button>
        <Button
          size="icon"
          className="size-8 text-destructive border-destructive-400 hover:bg-destructive hover:text-background"
          variant="outline"
          onClick={() => setOpenDialog(true)}
        >
          <TrashIcon className="h-4 w-4" />
        </Button>
      </div >
      <ConfirmDialog
        openDialog={openDialog}
        onCancel={handleDialogCancel}
        onSubmit={handleDialogDelete}
        title="Delete Indication?"
        icon={<TrashIcon />}
        description="Are you sure you want to delete this indication?"
        submitText="Yes, Delete"
        type={ConfirmDialogType.DELETE}
      />
    </>
  );
}
