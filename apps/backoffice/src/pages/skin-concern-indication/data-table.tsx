import { ColumnDef } from "@tanstack/react-table";
import { SkinProblemIndication } from "#/services/types/skin-problem-indication";
import DataTableAction from "./data-table-action";

export const skinProblemIndicationColumns: ColumnDef<SkinProblemIndication>[] = [
  {
    accessorKey: "name",
    header: "Indication",
    cell: ({ row }) => {
      return (<div className="flex w-60">
        {row.original.name}
      </div>);
    }
  },
  {
    header: "Action",
    cell: ({ row }) => {
      return <div className="flex w-6">
        <DataTableAction row={row} />
      </div>;
    },
  },
];
