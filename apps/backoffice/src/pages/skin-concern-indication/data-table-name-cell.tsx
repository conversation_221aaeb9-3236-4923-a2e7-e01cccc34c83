import { ClickTooltip } from "#/components/click-tooltip";
import { Row } from "@tanstack/react-table";
import { CheckCircleSolid } from "iconoir-react";
import { TreatmentProduct } from "#/services/types/treatment-product";

interface DataTableNameCellProps {
  row: Row<TreatmentProduct>;
}

export default function DataTableNameCell(props: DataTableNameCellProps) {
  const { row } = props;

  return (
    <div className="flex gap-1 items-center">
      <div>
        {row.original.name}
      </div>
      {row.original.is_top_recommendation && (
        <ClickTooltip
          content="Top Recommendation"
          className="bg-white text-foreground border border-neutral-200 p-3 max-w-[400px] whitespace-normal"
        >
            <CheckCircleSolid className="size-4 text-primary" />
        </ClickTooltip>
      )}
    </div>
  );
}
