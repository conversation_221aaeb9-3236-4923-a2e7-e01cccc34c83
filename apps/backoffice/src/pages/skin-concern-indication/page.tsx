import { Input } from "#/components/base";
import { DataTableWithoutPagination } from "#/components/data-table-without-pagination";
import { useApiClient } from "#/context/hooks/api-client";
import { usePaginationQuery } from "#/context/hooks/use-pagination";
import { SkinProblemIndicationPaginatedResponse } from "#/services/types/skin-problem-indication";
import { Plus, Search } from "iconoir-react";
import { Button } from "shared-ui";
import { skinProblemIndicationColumns } from "./data-table";

export default function SkinConcernIndicationPage() {
    const apiClient = useApiClient();
    const queryParams = new URLSearchParams(location.search);

    const { data, search, setSearch } = usePaginationQuery<SkinProblemIndicationPaginatedResponse>({
        queryKey: ["skin-concern-indications", queryParams.toString()],
        queryFn: (params) => apiClient.skinProblemIndication.getAll({ ...params }),
    });

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const searchValue = e.target.value;
        setSearch(searchValue);
    };

    const { content } = data?.data ?? {};

    return (
        <div className="p-6 rounded-xl bg-background shadow-md">
            <div className="flex flex-col gap-4">

                <div className="flex items-center gap-2">
                    <Input
                        name="search"
                        placeholder="Search Name"
                        className="w-64"
                        prefixIcon={<Search className="size-5" />}
                        value={search}
                        onChange={handleSearch}
                    />

                    <div className="flex gap-2 items-center">
                        <Button onClick={() => {}}>
                            <Plus className="size-5" />
                            <span className="text-small">Create Indication</span>
                        </Button>
                    </div>
                </div>
                <DataTableWithoutPagination
                    columns={skinProblemIndicationColumns}
                    data={content ?? []}
                />
            </div>
        </div>
    );
}
