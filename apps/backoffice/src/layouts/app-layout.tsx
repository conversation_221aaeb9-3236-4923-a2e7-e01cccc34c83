import { ErrorBoundary } from "react-error-boundary";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "#/context/hooks/use-auth";
import InternalError from "#/pages/errors/internal-error";
import logger from "#/utils/logger";
import RootLayout from "./root-layout";
import AppSidebar from "#/components/app-sidebar";
import { Avatar, SidebarInset, SidebarProvider } from "shared-ui";
import { User } from "iconoir-react";
import Breadcrumbs from "#/components/breadcrumbs";

export default function AppLayout() {
  const { loggedIn, user } = useAuth();
  const { pathname } = useLocation();

  if (!loggedIn) {
    return <Navigate to={`/auth/login?returnTo=${pathname}`} replace />;
  }

  if (pathname === "/") {
    return <Navigate to="/user" replace />;
  }

  return (
    <ErrorBoundary
      FallbackComponent={InternalError}
      onReset={() => logger.info("ErrorBoundary", "reset errror state - app")}
      resetKeys={["app"]}
    >
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="relative flex h-16 bg-background border-b border-secondary-200 shrink-0 items-center gap-2 px-4 justify-between">
            <Breadcrumbs />
            <div className="flex items-center gap-2">
              <Avatar className="bg-primary text-background justify-center items-center">
                <User />
              </Avatar>
              <span className="text-small">{user?.name}</span>
            </div>
          </header>
          <RootLayout className="h-full min-h-screen bg-secondary-100 p-6">
            <Outlet />
          </RootLayout>
        </SidebarInset>
      </SidebarProvider>
    </ErrorBoundary>
  );
}
