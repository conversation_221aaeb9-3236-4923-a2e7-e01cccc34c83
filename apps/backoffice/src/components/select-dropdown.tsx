import React from "react";
import { cn, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "shared-ui";

interface SelectDropdownProps {
  trigger: React.ReactNode;
  items: (string | number)[];
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  ref?: React.Ref<HTMLButtonElement>;
  error?: boolean;
}

const SelectDropdown = React.forwardRef<HTMLButtonElement, SelectDropdownProps>(
  ({ trigger, items, value, onChange, onBlur, error }, ref) => {
    return (
      <Select
        value={value}
        onValueChange={(newValue) => onChange?.(newValue)}
        onOpenChange={(open) => {
          if (!open) onBlur?.();
        }}
      >
        <SelectTrigger className={cn("w-full p-2 text-left", error ? "border-destructive" : "")} ref={ref}>
          {trigger}
        </SelectTrigger>
        <SelectContent align="end" sideOffset={8}>
          {items.map((item, index) => (
            <SelectItem key={index} value={item?.toString() || ""}>
              {item}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }
);

SelectDropdown.displayName = "SelectDropdown";

interface SelectDropdownValueProps {
  placeholder?: string;
  children?: React.ReactNode;
  className?: string;
}

const SelectDropdownValue = React.forwardRef<
  React.ElementRef<typeof SelectValue>,
  SelectDropdownValueProps
>(({ placeholder, children, className }, ref) => {
  return (
    <SelectValue
      ref={ref}
      placeholder={
        placeholder ? (
          <span className="text-secondary">{placeholder}</span>
        ) : undefined
      }
      className={className}
    >
      {children}
    </SelectValue>
  );
});

SelectDropdownValue.displayName = "SelectDropdownValue";

export { SelectDropdown, SelectDropdownValue };
