import { useState, useEffect, useRef } from "react";
import { NavArrowDown, Xmark } from "iconoir-react";
import { cn, Input } from "shared-ui";
import React from "react";
import { Checkbox } from "./ui/checkbox";

interface TagInputProps {
  placeholder?: string;
  inputPlaceholder?: string;
  name?: string;
  value?: string[];
  items?: string[];
  onChange?: (value: string[]) => void;
  onBlur?: () => void;
  className?: string;
  style?: React.CSSProperties;
  error?: boolean;
}

const TagInput = React.forwardRef<HTMLInputElement, TagInputProps>((tagProps, ref) => {
  const {
    placeholder,
    inputPlaceholder,
    name,
    value = [],
    items = [],
    onChange,
    onBlur,
    className,
    style,
    error,
  } = tagProps;

  const [inputValue, setInputValue] = useState("");
  const [openModal, setOpenModal] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenModal(false);
        onBlur?.();
      }
    };

    if (openModal) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openModal]);

  const handleInputOnEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (inputValue.trim() !== "") {
        const newValue = [...value, inputValue.trim()];
        if (onChange) {
          onChange(newValue);
        }
        setInputValue("");
      }
    }
  };

  return (
    <div className={cn("relative", className)} style={style}>
      <div ref={ref}>
        <div
          className={cn(
            "flex items-center justify-between px-3 py-1 border border-border rounded-md cursor-pointer",
            error && "border-destructive"
          )}
          onClick={() => setOpenModal(!openModal)}
        >
          <div className="flex items-center gap-2 overflow-x-auto">
            {value.length === 0 ? (
              <span className="text-secondary text-small py-1.5">{placeholder}</span>
            ) : (
              value.map((item, index) => (
                <div
                  key={`${item}-${index}`}
                  className="flex items-center gap-2 py-2 px-4 text-xs bg-primary-50 rounded-full"
                >
                  <span className="flex text-nowrap">
                    {item}
                    <span
                      className="ml-2 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        const newValue = value.filter((_, i) => i !== index);
                        if (onChange) {
                          onChange(newValue);
                        }
                      }}
                    >
                      <Xmark className="size-4 text-secondary" />
                    </span>
                  </span>
                </div>
              ))
            )}
          </div>
          <NavArrowDown className="size-4 text-secondary" />
        </div>
        {openModal && (
          <div
            ref={dropdownRef}
            className="absolute top-12 shadow-md left-0 right-0 z-50 p-4 border rounded-md space-y-2 bg-background"
          >
            <Input
              placeholder={inputPlaceholder}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleInputOnEnter}
            />
            <div className="space-y-2">
              {items?.map((item) => (
                <Checkbox
                  key={item}
                  id={item}
                  label={item}
                  value={item}
                  name={name}
                  checked={value?.includes(item) || false}
                  onChange={(e) => {
                    const isChecked = e.target.checked;
                    const newValue = [...(value || [])];
                    if (isChecked) {
                      newValue.push(item);
                    } else {
                      const index = newValue.indexOf(item);
                      if (index > -1) {
                        newValue.splice(index, 1);
                      }
                    }
                    if (onChange) {
                      onChange(newValue);
                    }
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

TagInput.displayName = "TagInput";

export { TagInput };
