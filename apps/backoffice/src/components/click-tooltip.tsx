import React, { useState, useRef, useEffect } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "shared-ui";

interface ClickTooltipProps {
  children: React.ReactNode;
  content: React.ReactNode;
  className?: string;
}

export const ClickTooltip = ({ children, content, className }: ClickTooltipProps) => {
  const [open, setOpen] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current && 
        !triggerRef.current.contains(event.target as Node) &&
        open
      ) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open]);

  const handleOpenChange = () => {
    // Only allow programmatic changes (from our click handler)
    // Ignore hover-triggered open changes
  };

  const handleClick = () => {
    setOpen(!open);
  };

  return (
    <Tooltip open={open} onOpenChange={handleOpenChange}>
      <TooltipTrigger asChild onClick={handleClick}>
        <div 
          ref={triggerRef}
          className="cursor-pointer"
        >
          {children}
        </div>
      </TooltipTrigger>
      <TooltipContent side="bottom" className={className}>
        {content}
      </TooltipContent>
    </Tooltip>
  );
};
