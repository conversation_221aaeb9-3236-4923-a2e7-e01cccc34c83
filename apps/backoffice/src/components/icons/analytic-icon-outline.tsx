export const AnalyticIconOutline = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_28_354)">
        <path
          d="M0.850098 1.59998C0.850098 1.18576 1.18589 0.849976 1.6001 0.849976H18.4001C18.8143 0.849976 19.1501 1.18577 19.1501 1.59998V18.4C19.1501 18.8142 18.8143 19.15 18.4001 19.15H1.6001C1.18588 19.15 0.850098 18.8142 0.850098 18.4V1.59998ZM15.3501 5.99998C15.3501 5.25439 14.7457 4.64998 14.0001 4.64998C13.2545 4.64998 12.6501 5.25439 12.6501 5.99998V14C12.6501 14.7456 13.2545 15.35 14.0001 15.35C14.7457 15.35 15.3501 14.7456 15.3501 14V5.99998ZM11.3501 8.99998C11.3501 8.25441 10.7457 7.64998 10.0001 7.64998C9.25453 7.64998 8.6501 8.25441 8.6501 8.99998V14C8.6501 14.7456 9.25453 15.35 10.0001 15.35C10.7457 15.35 11.3501 14.7456 11.3501 14V8.99998ZM7.3501 11C7.3501 10.2544 6.74569 9.64997 6.0001 9.64997C5.25451 9.64997 4.6501 10.2544 4.6501 11V14C4.6501 14.7456 5.25451 15.35 6.0001 15.35C6.74569 15.35 7.3501 14.7456 7.3501 14V11Z"
          stroke="currentColor"
          strokeWidth="1.2"
        />
      </g>
      <defs>
        <clipPath id="clip0_28_354">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
