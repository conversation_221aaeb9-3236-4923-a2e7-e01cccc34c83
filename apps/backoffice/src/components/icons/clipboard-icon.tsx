export const ClipboardIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M10.5 3.5C10.1023 3.50027 9.72088 3.65839 9.43963 3.93963C9.15839 4.22088 9.00027 4.60226 9 5H15C15 4.60218 14.842 4.22064 14.5607 3.93934C14.2794 3.65804 13.8978 3.5 13.5 3.5H10.5ZM7.807 3.678C8.05415 3.17453 8.43749 2.75043 8.9135 2.45383C9.38952 2.15723 9.93914 2 10.5 2H13.5C14.061 1.99982 14.6109 2.15695 15.0871 2.45357C15.5633 2.75018 15.9468 3.17438 16.194 3.678C16.691 3.72 17.186 3.77 17.68 3.828C19.177 4.001 20.25 5.288 20.25 6.757V20C20.25 20.7956 19.9339 21.5587 19.3713 22.1213C18.8087 22.6839 18.0456 23 17.25 23H6.75C5.95435 23 5.19129 22.6839 4.62868 22.1213C4.06607 21.5587 3.75 20.7956 3.75 20V6.757C3.75 5.287 4.823 4.001 6.32 3.827C6.813 3.77 7.309 3.721 7.807 3.678Z"
        fill="white"
      />
    </svg>
  );
};
