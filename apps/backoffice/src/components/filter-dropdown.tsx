import {
  DropdownMenu,
  DropdownMenuTrigger,
  Button,
  DropdownMenuContent,
  DropdownMenuGroup,
} from "shared-ui";
import { Filter as FilterIcon } from "iconoir-react";
import { FilterDropdownItem } from "./filter-dropdown-item";
import { FilterFieldType } from "#/constants/filter-field-type";
import { FilterPriceRange } from "./filter-price-range";
import { useFilter } from "#/context/hooks/use-filter";
import FilterRadio from "./filter-radio";

export const FilterDropdown = () => {
  const { filterItems } = useFilter();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="px-3">
          <FilterIcon className="size-4 mr-2" />
          <span className="text-small text-secondary-500">Filter</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" sideOffset={8}>
        <DropdownMenuGroup>
          {filterItems.map((item) => {
            switch (item.type) {
              case FilterFieldType.MultiSelect:
                return (
                  <FilterDropdownItem
                    key={item.label}
                    label={item.label}
                    options={item.options ?? []}
                  />
                );
              case FilterFieldType.PriceRange:
                return <FilterPriceRange key={item.label} label={item.label} />;
              case FilterFieldType.SingleSelect:
                return <FilterRadio key={item.label} label={item.label} options={item.options ?? []} />;
            }
          })}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
