import { SidebarItem } from "#/constants";
import { NavArrowRight } from "iconoir-react";
import { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import {
  Collapsible,
  SidebarMenuItem,
  CollapsibleTrigger,
  SidebarMenuButton,
  CollapsibleContent,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "shared-ui";

interface AppSidebarItemProps {
  item: SidebarItem;
}

export default function AppSidebarItem(props: AppSidebarItemProps) {
  const { item } = props;
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Check if the current pathname matches the item's URL or any of its sub-items' URLs
    if (
      pathname.startsWith(item.url) ||
      (item.items && item.items.some((subItem: any) => pathname.startsWith(subItem.url)))
    ) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, []);

  return (
    <Collapsible
      asChild
      ref={ref}
      className="group/collapsible"
      key={item.title}
      open={open}
      onOpenChange={setOpen}
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            size="lg"
            className={`rounded-full px-5 ${
              pathname.startsWith(item.url) ? "bg-primary text-white" : ""
            }`}
            onClick={() => {
              if (item.items) return;
              navigate(item.url);
            }}
            tooltip={item.title}
            isActive={pathname.startsWith(item.url)}
          >
            {pathname.startsWith(item.url)
              ? item.activeIcon && <item.activeIcon width={48} height={48} />
              : item.icon && <item.icon width={48} height={48} />}
            <div className="overflow-visible whitespace-wrap px-1">{item.title}</div>
            {item.items && (
              <NavArrowRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            )}
          </SidebarMenuButton>
        </CollapsibleTrigger>
        {item.items && (
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items?.map((subItem: any) => (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton
                    className={`rounded-full pl-14 pr-5 py-6 ${
                      pathname.startsWith(subItem.url) ? "bg-primary text-white" : ""
                    }`}
                    asChild
                    isActive={pathname.startsWith(subItem.url)}
                  >
                    <Link to={subItem.url}>
                      {subItem.icon && <subItem.icon />}
                      <div className="overflow-visible whitespace-wrap">{subItem.title}</div>
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        )}
      </SidebarMenuItem>
    </Collapsible>
  );
}
