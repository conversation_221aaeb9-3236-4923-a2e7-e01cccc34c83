import {
  <PERSON>bar,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bar<PERSON>ooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  Button,
} from "shared-ui";
import { sidebarItems } from "#/constants";
import { useAuth } from "#/context/hooks/use-auth";
import AppSidebarItem from "./app-sidebar-item";

export default function AppSidebar() {
  const { logout } = useAuth();

  return (
    <Sidebar className="w-[--sidebar-width] p-1.5">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="flex items-center justify-center">
              <img src="/logo.png" alt="Euromedica" className="h-24 w-28 object-contain" />
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu>{sidebarItems.map((item) => <AppSidebarItem key={item.title} item={item} />)}</SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="flex items-center justify-center">
              <Button
                variant="ghost"
                size="lg"
                className="px-4 bg-destructive-50 text-destructive w-full shadow-md text-destructive hover:bg-destructive-100 hover:text-destructive-500"
                onClick={() => logout()}
              >
                Logout
              </Button>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
