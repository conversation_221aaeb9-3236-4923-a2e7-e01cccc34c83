import { useFilter } from '#/context/hooks/use-filter';
import { useMemo } from 'react';
import { DropdownMenuPortal, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, Label, RadioGroup, RadioGroupItem } from 'shared-ui';

interface FilterRadioOption {
    value: string;
    label: string;
}

interface FilterRadioProps {
    label: string;
    options: FilterRadioOption[];
}

export default function FilterRadio({ label, options }: FilterRadioProps) {
    const { queryParamsChanged } = useFilter();
    const queryParams = useMemo(() => new URLSearchParams(location.search), [location.search]);
    const query = label.toLowerCase();
    const queryParamsValue = queryParams.get(query)  || '';

    const handleCheckedChange = (value: string) => {
        const newQueryParams = new URLSearchParams(queryParams);
        newQueryParams.delete(query);
        if (value) {
            // if value is not empty, set the new value
            newQueryParams.set(query, value);
        }
        queryParamsChanged(newQueryParams);
    };

    return (
        <DropdownMenuSub>
            <DropdownMenuSubTrigger>{label}</DropdownMenuSubTrigger>
            <DropdownMenuPortal>
                <DropdownMenuSubContent sideOffset={10}>
                    <div className="flex flex-col gap-2 p-2">
                        <RadioGroup className="space-y-2" defaultValue={queryParamsValue} onValueChange={handleCheckedChange}>
                            {options.map((option: FilterRadioOption) => (
                                <div className="flex items-center gap-2" key={option.value}>
                                    <RadioGroupItem value={option.value} id={`radio-${option.value}`} />
                                    <Label htmlFor={`radio-${option.value}`}>{option.label}</Label>
                                </div>
                            ))}
                        </RadioGroup>
                    </div>
                </DropdownMenuSubContent>
            </DropdownMenuPortal>
        </DropdownMenuSub>
    )
}
