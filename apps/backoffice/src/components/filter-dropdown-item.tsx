import { useFilter } from "#/context/hooks/use-filter";
import { useMemo } from "react";
import {
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
  DropdownMenuSubContent,
  DropdownMenuCheckboxOutlineItem,
} from "shared-ui";

interface FilterDropdownItemOptionProps {
  label: string;
  value: string;
}

interface FilterDropdownItemProps {
  label: string;
  options: FilterDropdownItemOptionProps[];
}

export const FilterDropdownItem = ({ label, options }: FilterDropdownItemProps) => {
  const context = useFilter();
  const queryParams = useMemo(() => new URLSearchParams(location.search), [location.search]);
  const query = label.toLowerCase();
  const queryParamsValues = queryParams.getAll(query);

  const handleCheckedChange = (value: string) => {
    const newQueryParams = new URLSearchParams(queryParams);
    if (newQueryParams.has(query)) {
      const existingQuery = newQueryParams.getAll(query);
      for (let i = 0; i < existingQuery.length; i++) {
        if (existingQuery[i] === value) {
          // If the value is already exist, we can just set it
          newQueryParams.delete(query, value);
          context?.queryParamsChanged(newQueryParams);
          return;
        }
      }
      // If the value is not exist, we can append it
      newQueryParams.append(query, value);
    } else {
      newQueryParams.append(query, value);
    }
    context?.queryParamsChanged(newQueryParams);
  };

  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger>{label}</DropdownMenuSubTrigger>
      <DropdownMenuPortal>
        <DropdownMenuSubContent sideOffset={10}>
          {options.map((item) => (
            <DropdownMenuCheckboxOutlineItem
              checked={queryParamsValues?.includes(item.value) ?? false}
              onCheckedChange={() => handleCheckedChange(item.value)}
              key={item.value}
            >
              {item.label}
            </DropdownMenuCheckboxOutlineItem>
          ))}
        </DropdownMenuSubContent>
      </DropdownMenuPortal>
    </DropdownMenuSub>
  );
};
