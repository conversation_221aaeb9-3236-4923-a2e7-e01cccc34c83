import { FilterFieldType } from "#/constants/filter-field-type";
import { FilterMenuItem } from "#/constants/filter-menu-item";
import { useFilter } from "#/context/hooks/use-filter";

import { convertToRupiah } from "#/utils/number-helper";
import { Xmark } from "iconoir-react";
import React from "react";
import { Button } from "shared-ui";

export const FilterList = () => {
  const { filterItems, removeFilter, resetFilter, isFilterActive } = useFilter();
  const queryParams = React.useMemo(() => new URLSearchParams(location.search), [location.search]);

  const renderPriceRange = (filterItem: FilterMenuItem) => {
    const priceQuery = queryParams.get(filterItem.label.toLowerCase());
    if (priceQuery) {
      const [minPriceQuery, maxPriceQuery] = priceQuery.split("-");
      return (
        <div key="price" className="flex items-center px-4 py-1 rounded-full bg-primary-50">
          <span className="text-small">
            Rp {convertToRupiah(Number(minPriceQuery))} - Rp{" "}
            {convertToRupiah(Number(maxPriceQuery))}
          </span>
          <Xmark
            className="size-4 ml-2 cursor-pointer"
            onClick={() => removeFilter(filterItem.label.toLowerCase(), priceQuery)}
          />
        </div>
      );
    }
    return null;
  };

  const renderMultiSelect = (filterItem: FilterMenuItem, queryParamsValue: string[]) => {
    return queryParamsValue.map((value) => {
      const filterItemValue = filterItem.options?.find((option) => option.value === value);
      if (filterItemValue !== undefined) {
        return (
          <div
            key={filterItemValue.value}
            className="flex items-center px-4 py-1 rounded-full bg-primary-50"
          >
            <span className="text-small">{filterItemValue.label}</span>
            <Xmark
              className="size-4 ml-2 cursor-pointer"
              onClick={() => removeFilter(filterItem.label.toLowerCase(), filterItemValue.value)}
            />
          </div>
        );
      }
      return null;
    });
  };

  const renderList = React.useMemo(() => {
    return filterItems.map((filterItem) => {
      const queryParamsValue = queryParams.getAll(filterItem.label.toLowerCase());
      if (!queryParamsValue) return null;

      switch (filterItem.type) {
        case FilterFieldType.MultiSelect:
          return renderMultiSelect(filterItem, queryParamsValue);
        case FilterFieldType.PriceRange:
          return renderPriceRange(filterItem);
        case FilterFieldType.SingleSelect:
          return renderMultiSelect(filterItem, queryParamsValue);
      }
    });
  }, [location.search]);

  return (
    <div className={`flex ${isFilterActive ? "flex" : "hidden"} justify-start items-center gap-4`}>
      <Button
        variant="link"
        className="text-destructive p-0 hover:no-underline"
        onClick={resetFilter}
      >
        Reset Filter
      </Button>
      <div className="flex items-center gap-2">{renderList}</div>
    </div>
  );
};
