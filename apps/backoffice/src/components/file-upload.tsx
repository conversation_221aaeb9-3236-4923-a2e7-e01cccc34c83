import { CloudUpload, EmptyPage, Upload } from "iconoir-react";
import { useCallback, useState, useRef, ChangeEvent, useEffect } from "react";
import { cn } from "shared-ui";
import { TrashIcon } from "./icons/trash";
import { getFilenameFromMediaPath, sanitizeFilename } from "#/utils/media-url-helper";

export interface VideoUploadProps {
  value?: File | string | null;
  onChange?: (file: File | null) => Promise<void>;
  className?: string;
  name?: string;
  maxSizeMB?: number;
  accept?: string;
  disabled?: boolean;
  label?: string;
}

export function FileUpload({
  onChange,
  className,
  name,
  accept,
  maxSizeMB = 50,
  disabled = false,
  label,
  value,
}: VideoUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // If the value is a string, it means the file is already uploaded.
    // Then user get the filename from the endpoint.
    if (value && typeof value === "string") {
      const filename = getFilenameFromMediaPath(value);
      if (!filename) return;
      setFileName(filename);
    }
  }, [value]);

  // Handle file selection
  const handleFileChange = useCallback(
    async (file: File | undefined) => {
      if (!file) {
        if (onChange) onChange(null);
        setFileName(null);
        return;
      }

      // Create a new file with sanitized name
      const sanitizedName = sanitizeFilename(file.name);
      const sanitizedFile = new File([file], sanitizedName, { type: file.type });

      // Check file size
      if (sanitizedFile.size > maxSizeMB * 1024 * 1024) {
        alert(`File size must be less than ${maxSizeMB}MB`);
        return;
      }

      setFileName(sanitizedName);
      setIsLoading(true);
      if (onChange) {
        await onChange(sanitizedFile);
      }
      setIsLoading(false);
    },
    [maxSizeMB, onChange]
  );

  // Handle drag events
  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      if (!disabled && !fileName) {
        setIsDragging(true);
      }
    },
    [disabled, fileName]
  );

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      if (disabled || fileName) return;

      const file = e.dataTransfer.files?.[0];
      handleFileChange(file);
    },
    [disabled, fileName, handleFileChange]
  );

  // Handle file input change
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    handleFileChange(file);
    // Reset the input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle remove video
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleFileChange(undefined);
  };

  return (
    <>
      <div
        className={cn(
          "relative w-full rounded-lg border-2 border-dashed border-border transition-colors cursor-pointer",
          isDragging && "border-primary bg-primary-50",
          disabled || (!!fileName && "cursor-not-allowed opacity-60"),
          className
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          id={"id-" + name}
          name={name}
          className="hidden"
          onChange={handleInputChange}
          disabled={disabled || !!fileName || isLoading}
        />

        <div className="flex flex-col items-center justify-center space-y-2 p-6 text-center">
          <Upload
            className={cn(
              "h-10 w-10 text-muted-foreground",
              disabled || (!!fileName && "text-secondary-300")
            )}
          />
          <div
            className={cn(
              "text-sm text-muted-foreground",
              disabled || (!!fileName && "text-secondary-300")
            )}
          >
            <p className="font-medium">Click to upload or drag and drop</p>
            <p className="text-xs">{label}</p>
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="border border-border rounded-lg px-3 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CloudUpload className="size-4" />
              <span className="text-subtle mt-1 animate-pulse">Uploading, please wait...</span>
            </div>
          </div>
        </div>
      )}
      {!isLoading && fileName && (
        <div className="border border-border rounded-lg px-3 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 truncate flex-1">
              <div className="w-4">
                <EmptyPage className="size-4" />
              </div>
              <span className="text-subtle mt-1 truncate">{fileName}</span>
            </div>
            <TrashIcon className="size-4 cursor-pointer text-destructive" onClick={handleRemove} />
          </div>
        </div>
      )}
    </>
  );
}
