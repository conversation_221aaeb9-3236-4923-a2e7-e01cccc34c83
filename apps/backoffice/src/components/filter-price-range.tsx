import { useFilter } from "#/context/hooks/use-filter";
import { convertToRupiah } from "#/utils/number-helper";
import React from "react";
import {
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
  DropdownMenuSubContent,
  Input,
  Button,
} from "shared-ui";

interface FilterPriceRangeProps {
  label: string;
}

export const FilterPriceRange = ({ label }: FilterPriceRangeProps) => {
  const context = useFilter();
  // Store numeric values internally
  const [minPrice, setMinPrice] = React.useState<string>("");
  const [maxPrice, setMaxPrice] = React.useState<string>("");
  // Store formatted display values
  const [formattedMin, setFormattedMin] = React.useState<string>("");
  const [formattedMax, setFormattedMax] = React.useState<string>("");

  const queryParams = React.useMemo(() => new URLSearchParams(location.search), [location.search]);
  React.useEffect(() => {
    const priceQuery = queryParams.get(label.toLowerCase());
    if (priceQuery) {
      const [minPriceQuery, maxPriceQuery] = priceQuery.split("-");
      setMinPrice(minPriceQuery);
      setMaxPrice(maxPriceQuery);
      // Format the values for display
      setFormattedMin(minPriceQuery ? convertToRupiah(Number(minPriceQuery)) : "");
      setFormattedMax(maxPriceQuery ? convertToRupiah(Number(maxPriceQuery)) : "");
    }
  }, [queryParams]);

  const handleMinPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove all non-numeric characters for internal value
    const numericValue = e.target.value.replace(/\D/g, '');
    setMinPrice(numericValue);
    // Format for display
    setFormattedMin(numericValue ? convertToRupiah(Number(numericValue)) : "");
  };

  const handleMaxPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove all non-numeric characters for internal value
    const numericValue = e.target.value.replace(/\D/g, '');
    setMaxPrice(numericValue);
    // Format for display
    setFormattedMax(numericValue ? convertToRupiah(Number(numericValue)) : "");
  };

  const handleApplyFilter = () => {
    const newQueryParams = new URLSearchParams(queryParams);
    if (newQueryParams.has(label.toLowerCase())) {
      newQueryParams.set(label.toLowerCase(), `${minPrice}-${maxPrice}`);
    } else {
      newQueryParams.append(label.toLowerCase(), `${minPrice}-${maxPrice}`);
    }
    context?.queryParamsChanged(newQueryParams);
  };

  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger>{label}</DropdownMenuSubTrigger>
      <DropdownMenuPortal>
        <DropdownMenuSubContent sideOffset={10}>
          <div className="flex flex-col gap-2 p-2">
            <span className="text-sm">Price Range</span>
            <Input
              placeholder="Min Price"
              type="text" /* Changed from number to text to allow formatted display */
              value={formattedMin}
              onChange={handleMinPriceChange}
            />
            <Input
              placeholder="Max Price"
              type="text" /* Changed from number to text to allow formatted display */
              value={formattedMax}
              onChange={handleMaxPriceChange}
            />
            <Button onClick={handleApplyFilter}>Apply Filter</Button>
          </div>
        </DropdownMenuSubContent>
      </DropdownMenuPortal>
    </DropdownMenuSub>
  );
};
