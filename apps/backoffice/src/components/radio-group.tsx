import React from "react";
import {
  Label,
  RadioGroup as RadioGroupComponent,
  RadioGroupItem as RadioGroupItemComponent,
} from "shared-ui";

interface RadioGroupProps {
  name: string;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
  options: { value: string; label: string }[];
  isStretched?: boolean;
}
export default function RadioGroup({
  name,
  defaultValue,
  onValueChange,
  options,
  isStretched,
}: RadioGroupProps) {
  const [value, setValue] = React.useState<string | undefined>(defaultValue);

  const handleValueChange = (value: string) => {
    setValue(value);
    onValueChange?.(value);
  };

  return (
    <RadioGroupComponent
      className="flex"
      name={name}
      value={value}
      onValueChange={handleValueChange}
    >
      {options.map((option) => {
        const isActive = value === option.value;
        return (
          <div
            key={option.value}
            className={`flex items-center space-x-1.5 border rounded-lg px-3 py-2 transition-colors
              ${isStretched ? "flex-1" : ""}
              ${isActive ? "bg-primary-500 text-foreground border-primary-500" : "bg-transparent"}
            `}
          >
            <RadioGroupItemComponent
              className={`size-4 ${isActive ? "border-background text-background" : "border-foreground"}`}
              value={option.value}
            />
            <Label
              className={`text-small w-[calc(100%-1rem)] font-normal transition-colors ${
                isActive ? "text-background" : "text-foreground"
              }`}
            >
              {option.label}
            </Label>
          </div>
        );
      })}
    </RadioGroupComponent>
  );
}
