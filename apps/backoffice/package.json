{"name": "backoffice", "version": "0.0.0", "description": "Euromedica Backoffice", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "biome lint . --write", "check": "biome check . --write", "format": "biome format . --write", "cleanup": "pnpm dlx rimraf dist node_modules pnpm-lock.yaml", "analyze-bundle": "pnpm dlx vite-bundle-visualizer"}, "dependencies": {"@modular-forms/react": "^0.10.0", "@nanostores/persistent": "^0.10.2", "@nanostores/query": "^0.3.4", "@nanostores/react": "^0.8.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.3", "clsx": "^2.1.1", "consola": "^3.2.3", "iconoir-react": "^7.11.0", "jwt-decode": "^4.0.0", "nanostores": "^0.11.3", "ofetch": "^1.4.0", "react": "^18.3.1", "react-cookie": "^7.2.0", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.56.0", "react-router-dom": "^6.26.2", "shared-ui": "workspace:*", "sonner": "^1.5.0", "tailwind-merge": "^2.5.3", "universal-cookie": "^7.2.0", "valibot": "^0.42.1", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.3", "@hookform/resolvers": "^3.9.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.7.4", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwind-debug-breakpoints": "^1.0.3", "tailwindcss": "^3.4.13", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.2", "vite": "^5.4.8", "vite-plugin-inspect": "^0.8.7", "vite-tsconfig-paths": "^5.0.1"}}